package one.srp.utils.datetime

import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit


fun parseIsoToMillis(
    iso: String,
    defaultOffset: ZoneOffset = ZoneOffset.UTC,
): Long {
    return runCatching {
        Instant.parse(iso).toEpochMilli()
    }.getOrElse {
        val odt = OffsetDateTime.parse(
            if (iso.endsWith("Z") || iso.contains(Regex("[+-]\\d{2}:\\d{2}$"))) iso else iso + defaultOffset.toString(),
            DateTimeFormatter.ISO_OFFSET_DATE_TIME
        )
        odt.toInstant().toEpochMilli()
    }
}

fun formatTimestampAgo(timestampMillis: Long): String {
    val now = getCurrMillis()
    val diff = now - timestampMillis

    return when {
        diff < TimeUnit.MINUTES.toMillis(1) -> {
            "just now"
        }

        diff < TimeUnit.HOURS.toMillis(1) -> {
            val minutes = TimeUnit.MILLISECONDS.toMinutes(diff)
            "$minutes minutes ago"
        }

        diff < TimeUnit.DAYS.toMillis(1) -> {
            val hours = TimeUnit.MILLISECONDS.toHours(diff)
            "$hours hours ago"
        }

        else -> {
            val days = TimeUnit.MILLISECONDS.toDays(diff)
            "$days days ago"
        }
    }
}