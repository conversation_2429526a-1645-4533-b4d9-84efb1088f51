package one.srp.utils.datetime

import kotlin.time.Clock
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
fun getCurrSecond(): Long {
    val now = Clock.System.now()
    return now.epochSeconds
}

@OptIn(ExperimentalTime::class)
fun getCurrMillis(): Long {
    val now = Clock.System.now()
    return now.toEpochMilliseconds()
}

@OptIn(ExperimentalTime::class)
fun getCurrMicros(): Long {
    val now = Clock.System.now()
    return now.toEpochMilliseconds() * 1_000L + (now.nanosecondsOfSecond / 1_000L)
}