package one.srp.core.network.model

import kotlinx.serialization.Serializable


@Serializable
data class AccountTokenResponse(
    val accessToken: String,
    val tokenType: String,
    val expiresIn: Int,
)

@Serializable
data class UserInfoResponse(
    val uid: Long,
    val uidStr: String? = null,
    var avatar: String? = null,
    val name: String? = null,
)

@Serializable
data class JwtToken(
    val accessToken: String,
    val tokenType: String,
    val expiresIn: Long
)

@Serializable
data class UnregisterUserTokenResponse(
    val uid: String,
    val jwtToken: JwtToken
)


