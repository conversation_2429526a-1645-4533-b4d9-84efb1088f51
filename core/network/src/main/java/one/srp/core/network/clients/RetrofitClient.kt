package one.srp.core.network.clients

import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import one.srp.core.network.clients.converters.NullOnEmptyConverterFactory
import one.srp.core.network.utils.JSON
import retrofit2.Retrofit


fun createMetricRetrofit(client: OkHttpClient): Retrofit {
    val metricBaseUrl = "https://t.iminsp.com"
    return Retrofit.Builder().apply {
        client(client)
        baseUrl(metricBaseUrl)

        addConverterFactory(JSON.asConverterFactory("application/json".toMediaType()))
    }.build()
}

fun createApiRetrofit(client: OkHttpClient, baseUrl: String): Retrofit {
    return Retrofit.Builder().apply {
        client(client)
        baseUrl(baseUrl)

        addConverterFactory(NullOnEmptyConverterFactory())
        addConverterFactory(JSON.asConverterFactory("application/json".toMediaType()))
    }.build()
}