package one.srp.core.network.model

import kotlinx.serialization.Serializable

@Serializable
data class CameraProductsRes(
    val data: CameraProductsData? = null,
)

@Serializable
data class CameraProductsData(
    val brandProducts: List<BrandItem> = emptyList(),
)

@Serializable
data class BrandItem(
    val brand: String? = null,
    val brandIcon: String? = null,
    val products: List<BrandProductItem> = emptyList(),
)

@Serializable
data class BrandProductItem(
    val fSkuId: String? = null,
    val showImage: String? = null,
    val cateTag: String? = null,
    val product: ProductItem? = null,
)