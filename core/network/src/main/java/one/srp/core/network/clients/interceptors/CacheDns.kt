package one.srp.core.network.clients.interceptors

import okhttp3.Dns
import java.net.InetAddress
import java.net.UnknownHostException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

class CacheDns : Dns {
    private val cache = ConcurrentHashMap<String, List<InetAddress>>()
    private val cacheTimestamps = ConcurrentHashMap<String, Long>()
    private val cacheTimeout = TimeUnit.MINUTES.toMillis(30) // 30分钟缓存

    override fun lookup(hostname: String): List<InetAddress> {
        val now = System.currentTimeMillis()
        val cached = cache[hostname]
        val timestamp = cacheTimestamps[hostname] ?: 0

        // 如果缓存有效，直接返回
        if (cached != null && (now - timestamp) < cacheTimeout) {
            return cached
        }

        return try {
            val addresses = Dns.SYSTEM.lookup(hostname)
            // 更新缓存
            cache[hostname] = addresses
            cacheTimestamps[hostname] = now
            addresses
        } catch (e: UnknownHostException) {
            // 如果DNS解析失败，尝试使用缓存的地址
            cached ?: throw e
        }
    }
}