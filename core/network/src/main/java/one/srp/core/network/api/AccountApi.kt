package one.srp.core.network.api

import kotlinx.serialization.Serializable
import one.srp.core.network.model.AccountTokenResponse
import one.srp.core.network.model.UnregisterUserTokenResponse
import one.srp.core.network.model.UserInfoResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

@Serializable
data class DeviceIdRequest(
    val deviceId: String,
)

@Serializable
data class StatusResponse(
    val status: String,
)

interface AccountApi {
    @POST("/auth/exchange?business=gem")
    suspend fun exchangeAuth(
        @Header("fb-token") token: String,
    ): Response<AccountTokenResponse>

    @GET("/user/me?business=gem")
    suspend fun getUserInfo(
    ): Response<UserInfoResponse>

    @DELETE("/user/me?business=gem")
    suspend fun deleteUser(
    ): Response<StatusResponse>

    @POST("/unregister/user/token/v2?business=gem")
    suspend fun getUnregisterUserToken(
        @Body deviceIdRequest: DeviceIdRequest,
    ): Response<UnregisterUserTokenResponse>
}
