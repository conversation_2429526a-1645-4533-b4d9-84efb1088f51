package one.srp.core.network.api

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 通知项数据类
 */
@Serializable
data class NotificationItem(
    @SerialName("user_id") val userId: String,
    val content: String,
    val link: String,
    @SerialName("notification_id") val notificationId: String,
    @SerialName("notification_type") val notificationType: String,
    val status: String,
    @SerialName("sender_uid") val senderUid: String,
    @SerialName("sender_name") val senderName: String? = null,
    @SerialName("sender_avatar") val senderAvatar: String? = null,
    @SerialName("created_at") val createdAt: String,
)

/**
 * 分页信息数据类
 */
@Serializable
data class Pagination(
    val total: Int,
    val page: Int,
    @SerialName("page_size") val pageSize: Int,
    @SerialName("total_pages") val totalPages: Int,
)

/**
 * 通知列表响应数据类
 */
@Serializable
data class NotificationListResponse(
    val data: List<NotificationItem>,
    val pagination: Pagination,
)

/**
 * 标记通知已读请求数据类
 */
@Serializable
data class MarkNotificationReadRequest(
    @SerialName("notification_id") val notificationId: List<String>,
)

/**
 * 通知API接口
 */
interface NotificationApi {
    /**
     * 获取通知列表
     * GET /notification/list
     *
     * @param page 页码
     * @param pageSize 每页大小
     * @return 通知列表响应
     */
    @GET("/notification/list")
    suspend fun getNotificationList(
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int,
    ): Response<NotificationListResponse>

    @POST("/notification/read")
    suspend fun readNotification(
        @Body request: MarkNotificationReadRequest,
    ): Response<Unit>
}
