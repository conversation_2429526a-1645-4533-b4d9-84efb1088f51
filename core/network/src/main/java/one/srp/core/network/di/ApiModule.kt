package one.srp.core.network.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import one.srp.core.network.api.AbApi
import one.srp.core.network.api.AccountApi
import one.srp.core.network.api.CommentApi
import one.srp.core.network.api.CommunityApi
import one.srp.core.network.api.FeedApi
import one.srp.core.network.api.MiscApi
import one.srp.core.network.api.MoodboardApi
import one.srp.core.network.api.NotificationApi
import one.srp.core.network.api.ProductApi
import one.srp.core.network.api.SessionApi
import one.srp.core.network.api.TryOnApi
import one.srp.core.network.api.UserApi
import one.srp.core.network.clients.createApiClient
import one.srp.core.network.clients.createApiRetrofit
import one.srp.core.network.utils.EnvConfig
import retrofit2.Retrofit
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ApiService

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ApiHeaderInterceptor

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class WorkflowApiService

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class AccountApiService

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RecommendApiService

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MetricApiService

@Module
@InstallIn(SingletonComponent::class)
object ApiModule {
    @Provides
    @Singleton
    @ApiService
    fun provideApiOkHttpClient(
        @ApiHeaderInterceptor headerInterceptor: Interceptor,
    ): OkHttpClient {
        return createApiClient(headerInterceptor)
    }

    @Provides
    @Singleton
    @WorkflowApiService
    fun provideWorkflowApiRetrofit(
        @ApiService okHttpClient: OkHttpClient,
        @ApiService envConf: EnvConfig,
    ): Retrofit {
        return createApiRetrofit(okHttpClient, envConf.baseUrl.workflow)
    }

    @Provides
    @Singleton
    @AccountApiService
    fun provideAccountApiRetrofit(
        @ApiService okHttpClient: OkHttpClient,
        @ApiService envConf: EnvConfig,
    ): Retrofit {
        return createApiRetrofit(okHttpClient, envConf.baseUrl.account)
    }

    @Provides
    @Singleton
    @RecommendApiService
    fun provideRecommendApiRetrofit(
        @ApiService okHttpClient: OkHttpClient,
        @ApiService envConf: EnvConfig,
    ): Retrofit {
        return createApiRetrofit(okHttpClient, envConf.baseUrl.recommend)
    }

    @Provides
    @Singleton
    fun provideAbApi(@AccountApiService retrofit: Retrofit): AbApi {
        return retrofit.create(AbApi::class.java)
    }

    @Provides
    @Singleton
    fun provideAccountApi(@AccountApiService retrofit: Retrofit): AccountApi {
        return retrofit.create(AccountApi::class.java)
    }

    @Provides
    @Singleton
    fun provideCommentApi(@RecommendApiService retrofit: Retrofit): CommentApi {
        return retrofit.create(CommentApi::class.java)
    }

    @Provides
    @Singleton
    fun provideCommunityApi(@WorkflowApiService retrofit: Retrofit): CommunityApi {
        return retrofit.create(CommunityApi::class.java)
    }

    @Provides
    @Singleton
    fun provideFeedApi(@RecommendApiService retrofit: Retrofit): FeedApi {
        return retrofit.create(FeedApi::class.java)
    }

    @Provides
    @Singleton
    fun provideMiscApi(@WorkflowApiService retrofit: Retrofit): MiscApi {
        return retrofit.create(MiscApi::class.java)
    }

    @Provides
    @Singleton
    fun provideMoodboardApi(@WorkflowApiService retrofit: Retrofit): MoodboardApi {
        return retrofit.create(MoodboardApi::class.java)
    }

    @Provides
    @Singleton
    fun provideNotificationApi(@WorkflowApiService retrofit: Retrofit): NotificationApi {
        return retrofit.create(NotificationApi::class.java)
    }

    @Provides
    @Singleton
    fun provideProductApi(@WorkflowApiService retrofit: Retrofit): ProductApi {
        return retrofit.create(ProductApi::class.java)
    }

    @Provides
    @Singleton
    fun provideSessionApi(@WorkflowApiService retrofit: Retrofit): SessionApi {
        return retrofit.create(SessionApi::class.java)
    }

    @Provides
    @Singleton
    fun provideTryOnApi(@WorkflowApiService retrofit: Retrofit): TryOnApi {
        return retrofit.create(TryOnApi::class.java)
    }

    @Provides
    @Singleton
    fun provideUserApi(@WorkflowApiService retrofit: Retrofit): UserApi {
        return retrofit.create(UserApi::class.java)
    }
}
