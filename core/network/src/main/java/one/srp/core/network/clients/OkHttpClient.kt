package one.srp.core.network.clients

import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import one.srp.core.network.clients.interceptors.CacheDns
import java.util.concurrent.TimeUnit


fun createMetricClient(): OkHttpClient {
    val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    return OkHttpClient.Builder().apply {
        readTimeout(30, TimeUnit.SECONDS)
        writeTimeout(30, TimeUnit.SECONDS)
        connectTimeout(5, TimeUnit.SECONDS)
        callTimeout(15, TimeUnit.SECONDS)

        retryOnConnectionFailure(true)

        addInterceptor(loggingInterceptor)
    }.build()
}

fun createApiClient(headerInterceptor: Interceptor): OkHttpClient {
    val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    return OkHttpClient.Builder().apply {
        readTimeout(60, TimeUnit.SECONDS)
        writeTimeout(60, TimeUnit.SECONDS)
        connectTimeout(51, TimeUnit.SECONDS)
        callTimeout(90, TimeUnit.SECONDS)

        retryOnConnectionFailure(true)

        dns(CacheDns())

        addInterceptor(loggingInterceptor)
        addInterceptor(headerInterceptor)
    }.build()
}
