package one.srp.core.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonIgnoreUnknownKeys

@Serializable
data class MainImage(
    val link: String? = null,
)

@Serializable
data class Price(
    val raw: String? = null,
)

@Serializable
data class BuyboxWinner(
    val price: Price? = null,
)

@Serializable
data class Tags(
    @SerialName("cate_tag")
    val cateTag: String? = null,
)

@OptIn(kotlinx.serialization.ExperimentalSerializationApi::class)
@Serializable
@JsonIgnoreUnknownKeys
data class ProductItem(
    @SerialName("global_id")
    val globalId: String = "",
    val id: String = "",
    val title: String? = null,
    val link: String? = null,
    @SerialName("search_engine")
    val searchEngine: String? = null,
    val platform: String? = null,
    val source: String? = null,
    @SerialName("source_icon")
    val sourceIcon: String? = null,
    @SerialName("main_image")
    val mainImage: MainImage? = null,
    @SerialName("buybox_winner")
    val buyboxWinner: BuyboxWinner? = null,
    val tags: Tags? = null,
    val brand: String? = null,
    @SerialName("brand_parent_name")
    val brandParentName: String? = null,

    var isFavorited: Boolean? = null,
)