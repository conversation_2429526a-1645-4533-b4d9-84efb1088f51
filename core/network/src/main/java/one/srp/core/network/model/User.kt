package one.srp.core.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PostUserCollectionRes(
    val status: String,
)

enum class CollectionType(val value: String) {
    Collage("collage"), CollageFromSearch("collage_from_search"), TryOn("try_on"), Product("product");

    override fun toString() = value
}

@Serializable
data class SavedItem(
    val moodboardId: String? = null,
    val image: String? = null,
    val isFeed: Boolean? = null,
    val reasoning: String? = null,
    val detailTitle: String? = null,
    val detailDescription: String? = null,
    val detailTag: List<String>? = null,
    val collectionType: String,
    val imageUrl: String? = null,
    val collectionId: String? = null,
    val tryOnTaskId: String? = null,
    val tryOnUrl: String? = null,
)

@Serializable
data class SavedRes(
    val data: List<SavedItem>,
    val pagination: SavedPagination? = null,
)

@Serializable
data class SavedPagination(
    val total: Int,
    val page: Int,
    val pageSize: Int,
    val totalPages: Int,
)

@Serializable
data class HistoryQueriesRes(
    val data: List<HistoryQueriesItem>,
    val pagination: HistoryQueriesPagination,
)

@Serializable
data class HistoryQueriesItem(
    val query: String,
    val imageUrl: String,
    val lastUpdated: Long,
    val taskId: String,
)

@Serializable
data class HistoryQueriesPagination(
    val total: Int,
    val page: Int,
    val pageSize: Int,
    val totalPages: Int,
)

@Serializable
data class PostUserProfile(
    val profilePicture: String? = null,
    val userName: String? = null,
    @SerialName("following_count")
    val followingCount: Int? = null,
    @SerialName("follower_count")
    val followerCount: Int? = null,
    @SerialName("total_likes")
    val totalLikes: Int? = null,
    @SerialName("total_saves")
    val totalSaves: Int? = null,
    @SerialName("follow_status")
    val followStatus: String? = null,
)

@Serializable
data class PublishHistoryRes(
    val data: List<FeedItem>,
    val pagination: HistoryQueriesPagination,
)

@Serializable
data class PublishPostRequest(
    val feedType: String,
    val documentId: String,
    val title: String,
    val description: String,
    val hashtags: List<String> = emptyList(),
)

@Serializable
data class OnboardTagsRequest(
    val tags: List<String>,
)
