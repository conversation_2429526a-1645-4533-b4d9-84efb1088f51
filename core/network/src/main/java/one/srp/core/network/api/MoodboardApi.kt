package one.srp.core.network.api

import one.srp.core.network.model.MoodboardItem
import one.srp.core.network.model.PostSearchItem
import one.srp.core.network.model.SearchItem
import one.srp.core.network.model.SearchV2Request
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface MoodboardApi {
    @GET("/moodboard/{id}")
    suspend fun getMoodboard(
        @Path("id") id: String,
    ): Response<MoodboardItem>

    @POST("/moodboard")
    suspend fun postMoodboard(
        @Body moodboard: PostSearchItem,
    ): Response<MoodboardItem>

    @POST("/search/v2")
    suspend fun createSearchTaskV2(
        @Body request: SearchV2Request,
    ): Response<SearchItem>

    @GET("search/{taskId}")
    suspend fun getSearchTask(
        @Path("taskId") taskId: String,
    ): Response<SearchItem>

    @GET("/moodboard/onboard/filter")
    suspend fun getOnboardMoodboards(
        @Query("content_num") contentNum: Int,
        @Query("taxonomy_primary_tags") taxonomyPrimaryTags: String,
    ): Response<List<MoodboardItem>>
}