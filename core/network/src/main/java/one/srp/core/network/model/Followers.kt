package one.srp.core.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FollowersResponse(
    val data: List<FollowerItem>,
    val pagination: FollowersPagination
)

@Serializable
data class FollowerItem(
    val uid: String,
    val username: String,
    val avatar: String,
    @SerialName("follow_status")
    val followStatus: String
)

@Serializable
data class FollowersPagination(
    val total: Int,
    val page: Int,
    @SerialName("page_size")
    val pageSize: Int,
    @SerialName("total_pages")
    val totalPages: Int
) 