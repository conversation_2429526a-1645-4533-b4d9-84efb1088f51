package one.srp.core.network.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import one.srp.core.network.api.MetricApi
import one.srp.core.network.clients.createMetricClient
import one.srp.core.network.clients.createMetricRetrofit
import retrofit2.Retrofit
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MetricService

@Module
@InstallIn(SingletonComponent::class)
object MetricModule {

    @Provides
    @Singleton
    @MetricService
    fun provideMetricOkHttpClient(): OkHttpClient {
        return createMetricClient()
    }

    @Provides
    @Singleton
    @MetricService
    fun provideMetricRetrofit(
        @MetricService okHttpClient: OkHttpClient,
    ): Retrofit {
        return createMetricRetrofit(okHttpClient)
    }

    @Provides
    @Singleton
    fun provideMetricA<PERSON>(@MetricService retrofit: Retrofit): <PERSON>ric<PERSON><PERSON> {
        return retrofit.create(MetricApi::class.java)
    }
}
