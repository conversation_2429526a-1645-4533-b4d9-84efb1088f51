package one.srp.core.network.model

import kotlinx.serialization.Serializable

@Serializable
data class CommentResponse(
    val id: String,
    val content: String,
    val images: List<String> = emptyList(),
    val parentCommentId: String? = null,
    val userId: String,
    val postId: String,
    val isDeleted: Boolean = false,
    val createdAt: String,
    val updatedAt: String,
    val replies: List<CommentResponse> = emptyList(),
    val userProfile: UserProfile? = null,
    val parentUserProfile: UserProfile? = null,
)

@Serializable
data class UserProfile(
    val uid: String,
    val name: String,
    val avatar: String? = null,
    val isBot: Boolean = false,
)


@Serializable
data class CommentListRes(
    val comments: List<CommentResponse>,
)

@Serializable
data class PostCommentReq(
    val content: String,
    val images: List<String>? = null,
    val parentCommentId: String? = null,
    val userId: String,
    val postId: String,
)