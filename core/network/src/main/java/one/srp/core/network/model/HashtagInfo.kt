package one.srp.core.network.model

import kotlinx.serialization.Serializable

@Serializable
data class HashtagInfo(
    val hashtag: String? = null,
    val imageUrl: String? = null,
    val backgroundImageUrl: String? = null,
    val description: String? = null,
    val iconUrl: String? = null,
    val coverTitle: String? = null,
    val coverImageUrl: String? = null,
    val ruleImageUrl: String? = null,
    val ruleTitle: String? = null,
    val ruleContent: String? = null,
    val tagTitle: String? = null,
    val tagIconUrl: String? = null,
    val status: String? = null,
    val communityStartTimestamp: Long? = null,
    val communityEndTimestamp: Long? = null,
    val joinButtonContent: String? = null,
    val isEditorPick: Boolean? = null,
    val hashtagType: List<String>? = null,
    val topIds: List<String>? = null,
    val createdAt: String? = null,
    val lastUpdated: Long? = null,
    val postCount: Int? = null,
)
