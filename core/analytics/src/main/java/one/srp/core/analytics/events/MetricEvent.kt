package one.srp.core.analytics.events

import kotlinx.serialization.Serializable
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventActionTypeSerializer
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventItemListNameSerializer
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventMethodSerializer
import one.srp.core.analytics.types.EventName
import one.srp.core.analytics.types.EventNameSerializer
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.types.EventReferSerializer
import one.srp.core.analytics.types.METRIC_VERSION
import one.srp.utils.datetime.getCurrMicros


@Serializable
data class MetricEvent(
    @Serializable(with = EventNameSerializer::class)
    val eventName: EventName,
    @Serializable(with = EventReferSerializer::class)
    val refer: EventRefer,

    @Serializable(with = EventItemListNameSerializer::class)
    val itemListName: EventItemListName,

    @Serializable(with = EventMethodSerializer::class)
    val method: EventMethod? = null,
    @Serializable(with = EventActionTypeSerializer::class)
    val actionType: EventActionType? = null,

    val items: List<EventItem> = emptyList(),

    val platform: String = "",
    val version: String = "",
    val eventVersion: String = METRIC_VERSION,

    val gensmoTimestamp: String = getCurrMicros().toString(),
    val abInfo: String = "",
    val gensmoUserId: String = "",
    val gensmoUserType: String = "",
    val gensmoActiveId: String = "",
    val gensmoUserSourceId: String = "",
    val appsflyerId: String = "",
)

@Serializable
sealed class MetricItem(
    @Serializable(with = EventItemListNameSerializer::class)
    open val itemListName: EventItemListName,
    open val items: List<EventItem> = emptyList(),

    @Serializable(with = EventMethodSerializer::class)
    open val method: EventMethod? = null,
    @Serializable(with = EventActionTypeSerializer::class)
    open val actionType: EventActionType? = null,

    @Serializable(with = EventNameSerializer::class)
    open val eventName: EventName,
    @Serializable(with = EventReferSerializer::class)
    open val refer: EventRefer = EventRefer.Default,
) {
    abstract fun copyWith(
        itemListName: EventItemListName = this.itemListName,
        items: List<EventItem> = this.items,
        method: EventMethod? = this.method,
        actionType: EventActionType? = this.actionType,
        eventName: EventName = this.eventName,
        refer: EventRefer = this.refer,
    ): MetricItem
}

data class SelectItem(
    override val itemListName: EventItemListName,
    override val items: List<EventItem> = emptyList(),

    override val method: EventMethod? = null,
    override val actionType: EventActionType? = null,

    override val eventName: EventName = EventName.SelectItem,
    override val refer: EventRefer = EventRefer.Default,
) : MetricItem(itemListName, items, method, actionType, eventName, refer) {
    override fun copyWith(
        itemListName: EventItemListName,
        items: List<EventItem>,
        method: EventMethod?,
        actionType: EventActionType?,
        eventName: EventName,
        refer: EventRefer,
    ): MetricItem = copy(
        itemListName = itemListName,
        items = items,
        method = method,
        actionType = actionType,
        eventName = eventName,
        refer = refer
    )
}

data class ViewItem(
    override val itemListName: EventItemListName,
    override val items: List<EventItem>,

    override val eventName: EventName = EventName.ViewItem,
    override val refer: EventRefer = EventRefer.Default,
) : MetricItem(itemListName, items, eventName = eventName, refer = refer) {
    override fun copyWith(
        itemListName: EventItemListName,
        items: List<EventItem>,
        method: EventMethod?,
        actionType: EventActionType?,
        eventName: EventName,
        refer: EventRefer,
    ): MetricItem = copy(
        itemListName = itemListName,
        items = items,
        eventName = eventName,
        refer = refer
    )
}


data class ViewItemList(
    override val itemListName: EventItemListName,
    override val items: List<EventItem>,

    override val eventName: EventName = EventName.ViewItemList,
    override val refer: EventRefer = EventRefer.Default,
) : MetricItem(itemListName, items, eventName = eventName, refer = refer) {
    override fun copyWith(
        itemListName: EventItemListName,
        items: List<EventItem>,
        method: EventMethod?,
        actionType: EventActionType?,
        eventName: EventName,
        refer: EventRefer,
    ): MetricItem = copy(
        itemListName = itemListName,
        items = items,
        eventName = eventName,
        refer = refer
    )
}


