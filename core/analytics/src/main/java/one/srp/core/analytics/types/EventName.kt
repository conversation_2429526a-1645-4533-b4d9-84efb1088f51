package one.srp.core.analytics.types

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = EventNameSerializer::class)
enum class EventName(val value: String) {
    SelectItem("select_item"), ViewItem("view_item"), ViewItemList("view_item_list")
}

object EventNameSerializer : KSerializer<EventName> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("EventName", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: EventName) {
        encoder.encodeString(value.value)
    }

    override fun deserialize(decoder: Decoder): EventName {
        val value = decoder.decodeString()
        return EventName.entries.find { it.value == value }
            ?: throw IllegalArgumentException("Unknown EventName value: $value")
    }
}
