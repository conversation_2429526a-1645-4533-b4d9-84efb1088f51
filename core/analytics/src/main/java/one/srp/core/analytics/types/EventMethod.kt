package one.srp.core.analytics.types

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = EventMethodSerializer::class)
enum class EventMethod(val value: String) {
    AppLaunch("app_launch"),
    AppForeground("app_foreground"),
    AppBackground("app_background"),
    PageView("page_view"),
    <PERSON><PERSON>("click"),
    Swipe("swipe"),
    TrueViewTrigger("true_view_trigger"),
    Zoom("zoom"),
    Shake("shake"),
    Screenshot("screenshot"),
    LongPress("long_press"),
    DoubleTap("double_tap"),
    InputText("input_text"),
    KeyboardReturn("keyboard_return"),
    Stay1s("stay_1s"),
    LoadComplete("load_complete"),
    WindowClose("window_close"),
    SwitchSection("switch_section"),
    GoBack("go_back"),
}

object EventMethodSerializer : KSerializer<EventMethod> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("EventMethod", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: EventMethod) {
        encoder.encodeString(value.value)
    }

    override fun deserialize(decoder: Decoder): EventMethod {
        val value = decoder.decodeString()
        return EventMethod.entries.find { it.value == value }
            ?: throw IllegalArgumentException("Unknown EventMethod value: $value")
    }
}
