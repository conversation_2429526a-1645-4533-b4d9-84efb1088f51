package one.srp.core.analytics.providers

import com.google.firebase.analytics.FirebaseAnalytics
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.utils.processMetricEventToBundle
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase Analytics provider implementation
 *
 * Integrates with Firebase Analytics SDK to track events and user properties.
 * This provider handles the conversion of AnalyticsEvent to Firebase-compatible format.
 */
@Singleton
class FirebaseAnalyticsProvider @Inject constructor(
    private val firebaseAnalytics: FirebaseAnalytics,
) : AnalyticsProvider {
    override val providerName: String = "Firebase"

    override var isInitialized = false

    override suspend fun initialize() {
        isInitialized = true
    }

    override suspend fun logEvent(event: MetricEvent) {
        try {
            val bundle = processMetricEventToBundle(event)
            firebaseAnalytics.logEvent(event.eventName.value, bundle)
        } catch (e: Exception) {
            Timber.w(e, "[Metric] Failed to log Firebase Analytics event: ${event.eventName.value}")
        }
    }
}
