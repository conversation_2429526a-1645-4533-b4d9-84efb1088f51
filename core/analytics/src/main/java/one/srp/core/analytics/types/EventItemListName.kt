package one.srp.core.analytics.types

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = EventItemListNameSerializer::class)
enum class EventItemListName(val value: String) {
    Default(STRING_DEFAULT),

    App("ap_app"),
    Screen("ap_screen"),
    InputBox("ap_input_box"),
    CameraBtn("ap_camera_btn"),
    CameraFlgBtn("ap_camera_flg_btn"),
    MenuBtn("ap_menu_btn"),
    CollageList("ap_collage_list"),
    InspoList("ap_inspo_list"),
    ShareBtn("ap_share_btn"),
    ShopBagBtn("ap_shop_bag_btn"),
    ProductCard("ap_product_card"),
    SavedBtn("ap_saved_btn"),
    SaveBtn("ap_save_btn"),
    InspoListInspo("ap_inspo_list_inspo"),
    PreferencesBtn("ap_preferences_btn"),
    TryOnBtn("ap_try_on_btn"),
    CollageListEntityList("ap_collage_list_entity_list"),
    CollageListEntityListEntity("ap_collage_list_entity_list_entity"),
    MenuDownloadBtn("ap_menu_download_btn"),
    MenuFeedbackBtn("ap_menu_feedback_btn"),
    LikeBtn("ap_like_btn"),
    UnlikeBtn("ap_unlike_btn"),
    DiscordBtn("ap_discord_btn"),
    ProductMainPic("ap_product_main_pic"),
    ShopInfoCollageBtn("ap_shop_info_collage_btn"),
    AddProductBtn("ap_add_product_btn"),
    ProductList("ap_product_list"),
    ProductListProduct("ap_product_list_product"),
    RemixBtn("ap_remix_btn"),
    ShareBtnCopy("ap_share_btn_copy"),
    ShareBtnFacebook("ap_share_btn_fb"),
    ShareBtnX("ap_share_btn_x"),
    ShareBtnInstagram("ap_share_btn_ins"),
    ShareBtnPinterest("ap_share_btn_pinterest"),
    ShareBtnReddit("ap_share_btn_reddit"),
    ShareBtnTiktok("ap_share_btn_tt"),
    ShareBtnDownload("ap_share_btn_download"),
    ShareBtnSystem("ap_share_btn_system"),
    HomeEntryBtn("ap_home_entry_btn"),
    CreateAvatarBtn("ap_create_avatar_btn"),
    SkipBtn("ap_skip_btn"),
    SwapIntoCollageBtn("ap_swap_into_collage_btn"),
    DeleteBtn("ap_delete_btn"),
    NoAvatarCreateBtn("ap_no_avatar_create_btn"),
    CollageEntityList("ap_collage_entity_list"),
    TopNavibarSearchInputBox("ap_top_navibar_search_input_box"),
    TopNavibarActivityEntry("ap_activity_btn"),
    DualColumnFeedForYouListItemCover("ap_dual_column_feed_for_you_list_item_cover"),
    DualColumnFeedForYouList("ap_dual_column_feed_for_you_list"),
    ApHashtagFeedList("ap_hashtag_feed_list"),
    ApHashtagFeedListItem("ap_hashtag_feed_list_item"),
    RetryBtn("ap_retry_btn"),
    EditYourInputBtn("ap_edit_your_input_btn"),
    TryOnResultList("ap_try_on_result_list"),
    ScenarioBtn("ap_scenario_btn"),
    MoreLooksBtn("ap_more_looks_btn"),
    TryOnResultEntityList("ap_try_on_result_entity_list"),
    TryOnResultEntityListEntity("ap_try_on_result_entity_list_entity"),
    DownloadBtn("ap_download_btn"),
    InAppPopup("ap_in_app_popup"),
    NotificationTurnOnBtn("ap_notification_turn_on_btn"),
    NotificationSettingsBtn("ap_notification_settings_btn"),
    NotificationTurnOnRefuse("ap_notification_turn_on_refuse"),
    NotificationTurnOnConfirm("ap_notification_turn_on_confirm"),
    NotificationBtn("ap_notification_btn"),
    NotificationListItem("ap_notification_list_item"),
    AvatarGenerateBtn("ap_avatar_generate_btn"),
    UseDefaultAvatarBtn("ap_use_default_avatar_btn"),
    UploadSelfieCtaBtn("ap_upload_selfie_cta_btn"),
    NextBtn("ap_next_btn"),
    RemixBottomBtn("ap_remix_bottom_btn"),
    Hashtag("ap_hashtag"),
    ProductListProductSaveBtn("ap_product_list_product_save_btn"),
    CollageEntityListEntity("ap_collage_entity_list_entity"),
    DualColumnFeedForYouListItemLikeBtn("ap_dual_column_feed_for_you_list_item_like_btn"),
    NavibarHome("ap_navibar_home"),
    NavibarStyle("ap_navibar_style"),
    NavibarProfile("ap_navibar_profile"),
    GalleryTabCollects("ap_gallery_tab_collects"),
    GalleryTabPosts("ap_gallery_tab_posts"),
    GalleryTabCollectsList("ap_gallery_tab_collects_list"),
    GalleryTabCollectsListItem("ap_gallery_tab_collects_list_item"),
    JoinWaitingListBtn("ap_join_waiting_list_btn"),
    SettingsBtn("ap_settings_btn"),
    HistoryBtn("ap_history_btn"),
    PortraitBtn("ap_portrait_btn"),
    ManageAvatarBtn("ap_manage_avatar_btn"),
    ManageAvatarGeneratingBtn("ap_manage_avatar_generating_btn"),
    GenerateBtn("ap_generate_btn"),
    ConfirmBtn("ap_confirm_btn"),
    SetFaceBtn("ap_set_face_btn"),
    SetBodyBtn("ap_set_body_btn"),
    TryOnHistoryListItem("ap_try_on_history_list_item"),
    TryOnHistoryListItemDeleteBtn("ap_try_on_history_list_item_delete_btn"),
    TryOnHistoryList("ap_try_on_history_list"),
    BdUserAccount("bd_user_account"),
    GoogleLoginBtn("ap_google_login_btn"),
    AppleLoginBtn("ap_apple_login_btn"),
    FollowBtn("ap_follow_btn"),
    CommentBtn("ap_comment_btn"),
    SendCommentBtn("ap_send_comment_btn"),
    UserInfo("ap_user_info"),
    GalleryTabPostsList("ap_gallery_tab_posts_list"),
    GalleryTabPostsListItem("ap_gallery_tab_posts_list_item"),
    InitializePostBtn("ap_initialize_post_btn"),
    InAppMessageBox("ap_in_app_message_box"),
    InAppMessageBoxBtn("ap_in_app_message_box_btn"),
    NoPostGetStartedBtn("ap_no_post_get_started_btn"),
    SubmitBtn("ap_submit_btn"),
    DeleteConfirmBtn("ap_delete_confirm_btn"),
    ChannelHistoryListItem("ap_channel_history_list_item"),
    ChannelHistoryList("ap_channel_history_list"),
    MenuAiClosetBtn("ap_menu_ai_closet_btn"),
    DislikeBtn("ap_dislike_btn"),
    Collage("ap_collage"),
    Popup("ap_popup"),
    SystemNotification("ap_system_notification"),
    ApAvatar("ap_avatar"),
    ApProductListScrollBtn("ap_product_list_scroll_btn"),
    ApFeedList("ap_feed_list"),
    ApFeedListItem("ap_feed_list_item"),
    ApOuterLikeBtn("ap_outer_like_btn"),
    BrandBtn("ap_brand_btn"),
    ShutterBtn("ap_shutter_btn"),
    CreateLookBtn("ap_create_look_btn"),
}

object EventItemListNameSerializer : KSerializer<EventItemListName> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("EventItemListName", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: EventItemListName) {
        encoder.encodeString(value.value)
    }

    override fun deserialize(decoder: Decoder): EventItemListName {
        val value = decoder.decodeString()
        return EventItemListName.entries.find { it.value == value }
            ?: throw IllegalArgumentException("Unknown EventItemListName value: $value")
    }
}