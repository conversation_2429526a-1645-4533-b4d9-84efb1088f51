package one.srp.core.analytics.providers

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.utils.processMetricEventToReqBody
import one.srp.core.network.api.MetricApi
import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Custom API Analytics provider implementation
 *
 * Integrates with custom analytics API to track events and user properties.
 * This provider handles the conversion of AnalyticsEvent to API-compatible format.
 */
@Singleton
class MetricApiAnalyticsProvider @Inject constructor(
    private val api: MetricApi,
) : AnalyticsProvider {
    override val providerName: String = "MetricApi"

    override var isInitialized = false

    // 失败重试机制
    private val consecutiveFailures = AtomicInteger(0)
    private val maxConsecutiveFailures = 3
    private val backoffDelayMs = 60000L // 1分钟

    override suspend fun initialize() {
        isInitialized = true
    }

    override suspend fun logEvent(event: MetricEvent) {
        // 检查连续失败次数
        if (consecutiveFailures.get() >= maxConsecutiveFailures) {
            Timber.d("Custom API Analytics 连续失败${consecutiveFailures.get()}次，暂时跳过发送")

            // 异步尝试重置计数器
            CoroutineScope(Dispatchers.IO).launch {
                kotlinx.coroutines.delay(backoffDelayMs)
                if (consecutiveFailures.get() >= maxConsecutiveFailures) {
                    Timber.d("尝试重置 Custom API Analytics 失败计数器")
                    consecutiveFailures.set(0)
                }
            }
            return
        }

        try {
            val reqBody = processMetricEventToReqBody(event)
            api.postApiMetric(reqBody)

            // 成功发送后重置失败计数器
            consecutiveFailures.set(0)
            Timber.d("Custom API Analytics event tracked: ${event.eventName}")
        } catch (e: Exception) {
            // 增加失败计数
            val failureCount = consecutiveFailures.incrementAndGet()
            Timber.w(
                e,
                "Failed to track Custom API Analytics event: ${event.eventName}, failure count: $failureCount"
            )

            // 如果连续失败次数达到阈值，记录警告
            if (failureCount >= maxConsecutiveFailures) {
                Timber.e("Custom API Analytics 连续失败达到阈值，将暂停发送${backoffDelayMs / 1000}秒")
            }
        }
    }
}
