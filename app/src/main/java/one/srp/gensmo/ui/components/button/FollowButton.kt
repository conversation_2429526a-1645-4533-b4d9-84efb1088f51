package one.srp.gensmo.ui.components.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.action.FollowStatus
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.tryon.CreateViewModel

@Composable
fun FeedFollowButton(
    modifier: Modifier = Modifier,
    isFollowing: Boolean = false,
    followStatus: FollowStatus? = null,
    onClick: () -> Unit = {},
) {
    val buttonText = when {
        !isFollowing -> stringResource(R.string.text_follow)
        followStatus == FollowStatus.Mutual -> stringResource(R.string.text_friends)
        followStatus == FollowStatus.Followed -> stringResource(R.string.text_following)
        else -> stringResource(R.string.text_following)
    }

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "FeedFollowButton")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickFollowWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            onClick()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("follow_button")
        }
    }

    OutlinedButton(
        onClick = { clickFollowWithCheck() },
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.White,
            contentColor = if (isFollowing) AppThemeColors.Gray700 else AppThemeColors.Red600,
            disabledContainerColor = AppThemeColors.Gray50,
            disabledContentColor = AppThemeColors.Gray700,
        ),
        border = BorderStroke(
            width = 1.dp,
            color = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.Red600
        ),
    ) {
        Text(
            buttonText,
            style = AppThemeTextStyle.Body12H
        )
    }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        onClick()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        onClick()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}

@Composable
fun ProfileFollowButton(
    modifier: Modifier = Modifier,
    isFollowing: Boolean = false,
    followStatus: FollowStatus? = null,
    onClick: () -> Unit = {},
) {
    val buttonText = when {
        !isFollowing -> stringResource(R.string.text_follow)
        followStatus == FollowStatus.Mutual -> stringResource(R.string.text_friends)
        followStatus == FollowStatus.Followed -> stringResource(R.string.text_following)
        else -> stringResource(R.string.text_following)
    }

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "ProfileFollowButton")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickFollowWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            onClick()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("follow_button")
        }
    }

    Button(
        onClick = { clickFollowWithCheck() },
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.Red600,
            contentColor = if (isFollowing) AppThemeColors.Gray700 else AppThemeColors.White,
            disabledContainerColor = AppThemeColors.Gray50,
            disabledContentColor = AppThemeColors.Gray700,
        ),
    ) {
        Text(
            buttonText,
            style = AppThemeTextStyle.Body12H
        )
    }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        onClick()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        onClick()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}