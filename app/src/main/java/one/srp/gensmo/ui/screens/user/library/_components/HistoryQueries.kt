package one.srp.gensmo.ui.screens.user.library._components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.PopupProperties
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.core.network.model.HistoryQueriesItem
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun HistoryQueryItem(
    query: HistoryQueriesItem,
    modifier: Modifier = Modifier,
    onDelete: (String) -> Unit = {},
    onOpenHistory: (String) -> Unit = {},
    onGenerateCollage: (String, String) -> Unit = { _, _ -> },
    onEdit: (String, String) -> Unit = { _, _ -> }
) {
    var showMenu by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onOpenHistory(query.taskId) },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (query.imageUrl.isNotEmpty()) {
                    AsyncImage(
                        model = query.imageUrl,
                        contentDescription = null,
                        modifier = Modifier
                            .size(48.dp)
                            .clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Crop
                    )
                }
                
                Text(
                    text = query.query,
                    style = AppThemeTextStyle.Body16H,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = formatDate(query.lastUpdated),
                    style = AppThemeTextStyle.Body12LightH.copy(
                        color = Color(0xFF767676)
                    )
                )
                
                Box {
                    IconButton(
                        onClick = { showMenu = true },
                        modifier = Modifier.size(24.dp)
                    ) {
                         Icon(
                            painter = painterResource(id = R.drawable.icon_more_actions),
                            contentDescription = "更多操作",
                            modifier = Modifier.fillMaxSize(),
                            tint = Color.Black
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false },
                        properties = PopupProperties(
                            focusable = true,
                            usePlatformDefaultWidth = false
                        ),
                        offset = DpOffset(0.dp, 8.dp),
                        modifier = Modifier
                            .width(160.dp)
                            .shadow(elevation = 12.dp, spotColor = Color(0x1A2F536D), ambientColor = Color(0x1A2F536D))
                            .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 4.dp))
                    ) {
                        DropdownMenuItem(
                            text = {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.icon_refresh),
                                        contentDescription = "重新生成图标",
                                        modifier = Modifier.size(20.dp),
                                        tint = Color.Black
                                    )
                                    Text(
                                        "Try again",
                                        style = AppThemeTextStyle.Body14LightH,
                                        color = Color.Black
                                    )
                                }
                            },
                            onClick = {
                                onGenerateCollage(query.query, query.imageUrl)
                                showMenu = false
                            },
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        )
                        DropdownMenuItem(
                            text = {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.icon_edit),
                                        contentDescription = "编辑图标",
                                        modifier = Modifier.size(20.dp),
                                        tint = Color.Black
                                    )
                                    Text(
                                        "Edit your input",
                                        style = AppThemeTextStyle.Body14LightH,
                                        color = Color.Black
                                    )
                                }
                            },
                            onClick = {
                                onEdit(query.query, query.imageUrl)
                                showMenu = false
                            },
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        )
                        DropdownMenuItem(
                            text = {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.icon_trashbin),
                                        contentDescription = "删除图标",
                                        modifier = Modifier.size(20.dp),
                                        tint = Color(0xFFEC221F)
                                    )
                                    Text(
                                        "Delete",
                                        style = AppThemeTextStyle.Body14LightH,
                                        color = Color(0xFFEC221F)
                                    )
                                }
                            },
                            onClick = {
                                onDelete(query.taskId)
                                showMenu = false
                            },
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

private fun formatDate(timestamp: Long): String {
    val date = Date(timestamp * 1000)
    val now = Date()
    val diff = now.time - date.time
    
    return when {
        diff < 24 * 60 * 60 * 1000 -> "Today"
        diff < 48 * 60 * 60 * 1000 -> "Yesterday"
        else -> SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(date)
    }
} 