package one.srp.gensmo.ui.screens.session.chat._components.message

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonPrimitive
import one.srp.core.network.model.AgentLoadingMessage
import one.srp.core.network.model.ProductContent
import timber.log.Timber
import one.srp.core.network.model.ChatJson

// 新增导入
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
// 新增导入
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import coil3.compose.AsyncImage
import one.srp.gensmo.R
// 新增导入（动画与透明度）
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.ui.draw.alpha

@Composable
fun AgentComponent(
    item: AgentLoadingMessage,
    modifier: Modifier = Modifier
) {
    // 记录内容实际高度（px），并将其转换为 dp 用于竖线高度
    val density = LocalDensity.current
    var contentHeightPx by remember { mutableStateOf(0) }

    // 统一左右结构的内边距
    val contentPadding = 12.dp

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 8.dp)
    ) {
        // 顶部工具信息行（独立 Row）
        val toolName = item.value.agentLoading.toolName
        val toolIcon = item.value.agentLoading.icon
        if (!toolName.isNullOrEmpty()) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 闪动动画（透明度循环）
                val infiniteTransition = rememberInfiniteTransition()
                val blinkAlpha by infiniteTransition.animateFloat(
                    initialValue = 1f,
                    targetValue = 0.4f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(durationMillis = 800, easing = LinearEasing),
                        repeatMode = RepeatMode.Reverse
                    )
                )

                if (!toolIcon.isNullOrEmpty()) {
                    AsyncImage(
                        model = toolIcon,
                        contentDescription = toolName,
                        modifier = Modifier
                            .width(16.dp)
                            .height(16.dp)
                            .clip(RoundedCornerShape(3.dp)),
                        contentScale = ContentScale.Crop
                    )
                } else {
                    androidx.compose.foundation.Image(
                        painter = painterResource(id = R.drawable.icon_clothes_default),
                        contentDescription = toolName,
                        modifier = Modifier
                            .width(16.dp)
                            .height(16.dp)
                            .clip(RoundedCornerShape(3.dp)),
                        contentScale = ContentScale.Crop
                    )
                }
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = toolName,
                    modifier = Modifier.alpha(blinkAlpha),
                    fontSize = 12.sp,
                    color = Color(0xFF666666),
                    fontWeight = FontWeight.Medium
                )
            }
            Spacer(modifier = Modifier.height(6.dp))
        }

        Row(
            verticalAlignment = Alignment.Top,
            modifier = Modifier
                .fillMaxWidth()
        ) {
            // 左侧竖线指示器 - 与内容顶部对齐，并扣除内容的上下内边距
            Box(
                modifier = Modifier
                    .padding(start = 6.dp, top = contentPadding, bottom = contentPadding)
                    .width(1.dp)
                    .height(with(density) { contentHeightPx.toDp() })
                    .background(Color(0xFFDFE1E3))
            )
            
            Spacer(modifier = Modifier.width(4.dp))
            
            // 内容容器
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(contentPadding)
                    .onGloballyPositioned { coords ->
                        contentHeightPx = coords.size.height
                    }
            ) {
                // 根据 contentType 显示不同的内容
                when (item.value.agentLoading.contentType.lowercase()) {
                    "text" -> {
                        val textContent = try {
                            item.value.agentLoading.content.jsonPrimitive.content
                        } catch (e: Exception) {
                            item.value.agentLoading.content.toString()
                        }
                        Text(
                            text = parseMarkdown(textContent),
                            style = TextStyle(
                                fontSize = 14.sp,
                                color = Color(0xFF333333),
                                lineHeight = 20.sp,
                                platformStyle = PlatformTextStyle(includeFontPadding = false),
                                lineHeightStyle = LineHeightStyle(
                                    alignment = LineHeightStyle.Alignment.Top,
                                    trim = LineHeightStyle.Trim.Both
                                )
                            )
                        )
                    }
                    "product" -> {
                        val productContent = try {
                            ChatJson.decodeFromJsonElement(
                                ProductContent.serializer(),
                                item.value.agentLoading.content
                            )
                        } catch (e: Exception) {
                            Timber.e(e, "Failed to parse ProductContent: ${item.value.agentLoading.content}")
                            null
                        }
                        
                        if (productContent != null) {
                            ProductListComponent(productContent)
                        } else {
                            Text(
                                text = "产品数据解析错误",
                                fontSize = 14.sp,
                                color = Color(0xFF666666),
                                lineHeight = 20.sp
                            )
                        }
                    }
                    "moodboard", "json" -> {
                        val moodboardContent = try {
                            Json.decodeFromJsonElement(
                                MoodboardContentWrapper.serializer(),
                                item.value.agentLoading.content
                            )
                        } catch (e: Exception) {
                            Timber.e(e, "Failed to parse MoodboardContentWrapper: ${item.value.agentLoading.content}")
                            null
                        }
                        
                        if (moodboardContent != null && moodboardContent.moodboards != null) {
                            MoodboardListComponent(moodboardContent.moodboards)
                        } else {
                            Text(
                                text = "Moodboard 数据解析错误",
                                fontSize = 14.sp,
                                color = Color(0xFF666666),
                                lineHeight = 20.sp
                            )
                        }
                    }
                    else -> {
                        Timber.d("Agent Loading Unknown Content Type (${item.value.agentLoading.contentType}): ${item.value.agentLoading.content}")
                        // 默认处理未知类型
                        val content = try {
                            item.value.agentLoading.content.jsonPrimitive.content
                        } catch (e: Exception) {
                            item.value.agentLoading.content.toString()
                        }
                        Text(
                            text = "未知内容类型 (${item.value.agentLoading.contentType}): ${parseMarkdown(content)}",
                            fontSize = 14.sp,
                            color = Color(0xFF666666),
                            lineHeight = 20.sp
                        )
                    }
                }
                
                // 工具信息已上移到最外层 Row
            }
        }
    }
}
