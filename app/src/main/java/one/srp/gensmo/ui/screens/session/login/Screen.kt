package one.srp.gensmo.ui.screens.session.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.mime.openUrl
import one.srp.gensmo.viewmodel.tryon.CreateViewModel

@Composable
fun SessionLoginScreen(
    navActions: NavActions = NavActions(),
    createViewModel: CreateViewModel = hiltViewModel(),
    refer: EventRefer = EventRefer.LoginFullScreen,
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)

    LaunchedEffect(Unit) {
        metric(SelectItem(itemListName = EventItemListName.Screen, method = EventMethod.PageView))
    }

    val context = LocalContext.current

    fun loginWithApple() {
        createViewModel.loginWithApple(
            context = context,
            onSuccess = {
                when (createViewModel.getLoginSource()) {
                    "default_avatar" -> navActions.navigateToTryOnModel(setFace = true)
                    "upload_selfie" -> createViewModel.updateCameraPicker(true)
                    else -> navActions.navigateToSessionChat(skip = true)
                }
            }
        )
        createViewModel.updateLoginDialog(false)
    }

    fun loginWithGoogle() {
        createViewModel.loginWithGoogle(
            context = context,
            onSuccess = {
                when (createViewModel.getLoginSource()) {
                    "default_avatar" -> navActions.navigateToTryOnModel(setFace = true)
                    "upload_selfie" -> createViewModel.updateCameraPicker(true)
                    else -> navActions.navigateToSessionChat(skip = true)
                }
            }
        )
        createViewModel.updateLoginDialog(false)
    }

    Scaffold { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(16.dp, 32.dp)
        ) {
            Row(modifier = Modifier) {
                Spacer(modifier = Modifier.weight(1f))

                TextButton(onClick = { navActions.back() }) {
                    Icon(Icons.Default.Close, null)
                }
            }

            Spacer(modifier = Modifier.height(40.dp))

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(30.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                Text(
                    stringResource(R.string.text_log_in_to_gensmo),
                    style = AppThemeTextStyle.Heading24D
                )

                Text(
                    stringResource(R.string.text_this_lets_you_enjoy_the_full_experience_while_we_keep_your_data_safe),
                    style = AppThemeTextStyle.Body13LightH.copy(AppThemeColors.Gray700),
                    modifier = Modifier.width(260.dp),
                    textAlign = TextAlign.Center
                )

                Image(
                    painterResource(R.drawable.image_collects_placeholder),
                    null,
                    modifier = Modifier
                        .fillMaxWidth(0.8f)
                        .aspectRatio(1f)
                )
            }

            Column(
                modifier = Modifier, verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {

                    OutlinedButton(
                        onClick = { loginWithGoogle() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(54.dp),
                        shape = MaterialTheme.shapes.medium
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxHeight()
                        ) {
                            Image(painterResource(R.drawable.icon_google), null)
                            Text(
                                stringResource(R.string.text_continue_with_google),
                                style = AppThemeTextStyle.Body16H
                            )
                        }
                    }

                    Button(
                        onClick = { loginWithApple() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(54.dp),
                        shape = MaterialTheme.shapes.medium,
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxHeight()
                        ) {
                            Icon(painterResource(R.drawable.icon_apple), null)
                            Text(
                                stringResource(R.string.text_continue_with_apple),
                                style = AppThemeTextStyle.Body16H
                            )
                        }
                    }
                }

                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        "By tapping Continue, you agree to our",
                        style = AppThemeTextStyle.Body11H.copy(
                            AppThemeColors.Gray600
                        )
                    )
                    Row {
                        Text(
                            "Terms", style = AppThemeTextStyle.Body11H.copy(
                                AppThemeColors.Gray600
                            ), modifier = Modifier.clickable {
                                openUrl(
                                    context, "https://gensmo.com/about/terms"
                                )
                            })
                        Text(
                            " and ", style = AppThemeTextStyle.Body11H.copy(
                                AppThemeColors.Gray600
                            )
                        )
                        Text(
                            "Privacy Policy", style = AppThemeTextStyle.Body11H.copy(
                                AppThemeColors.Gray600
                            ), modifier = Modifier.clickable {
                                openUrl(
                                    context, "https://gensmo.com/about/privacy"
                                )
                            })
                    }
                }
            }
        }
    }
}
