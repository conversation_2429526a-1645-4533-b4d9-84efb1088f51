package one.srp.gensmo.ui.components.camera

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.api.ProductApi
import one.srp.core.network.model.BrandItem
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class CameraProductViewModel @Inject constructor(
    private val productApi: ProductApi,
) : ViewModel() {
    private val _cameraProducts = MutableStateFlow<List<BrandItem>>(emptyList())
    val cameraProducts = _cameraProducts.asStateFlow()

    private val _loading = MutableStateFlow(false)
    val loading: StateFlow<Boolean> = _loading.asStateFlow()

    init {
        loadCameraProducts()
    }

    private fun loadCameraProducts() {
        viewModelScope.launch {
            _loading.value = true
            try {
                val response = productApi.getCameraProducts()
                if (response.isSuccessful) {
                    _cameraProducts.value = response.body()?.data?.brandProducts ?: emptyList()
                }

            } catch (e: Exception) {
                Timber.w(e)
            } finally {
                _loading.value = false
            }
        }
    }
}
