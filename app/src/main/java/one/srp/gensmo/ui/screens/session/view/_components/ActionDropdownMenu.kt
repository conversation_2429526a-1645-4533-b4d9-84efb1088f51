package one.srp.gensmo.ui.screens.session.view._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun ActionDropdownMenu(
    menu: @Composable ColumnScope.(() -> Unit) -> Unit,
    content: @Composable () -> Unit = {},
) {
    var expanded by remember { mutableStateOf(false) }

    fun onClose() {
        expanded = false
    }

    Box {
        IconButton(
            onClick = { expanded = !expanded },
            colors = IconButtonDefaults.iconButtonColors(
                MaterialTheme.colorScheme.background,
                MaterialTheme.colorScheme.onBackground
            ),
        ) {
            Icon(
                imageVector = Icons.Default.MoreHoriz,
                contentDescription = "more",
                modifier = Modifier
                    .aspectRatio(1f)
                    .padding(4.dp)
            )
        }


        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            containerColor = Color.White,
        ) {
            menu { onClose() }
        }

        content()
    }
}


@Composable
fun ShareButton(onClick: () -> Unit = {}) {
    DropdownMenuItem(
        text = { Text(stringResource(R.string.text_share), style = AppThemeTextStyle.Body13H) },
        onClick = onClick,
        leadingIcon = {
            Image(
                painter = painterResource(R.drawable.icon_share_line),
                contentDescription = null,
                modifier = Modifier.size(24.dp),
            )
        }
    )
}

@Composable
fun DownloadButton(onClick: () -> Unit = {}, text: String? = null) {
    DropdownMenuItem(
        text = {
            Text(
                text ?: stringResource(R.string.text_download),
                style = AppThemeTextStyle.Body13H
            )
        },
        onClick = onClick,
        leadingIcon = {
            Image(
                painter = painterResource(R.drawable.icon_download),
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
        }
    )
}

@Composable
fun FeedbackButton(onClick: () -> Unit = {}) {
    Box {
        DropdownMenuItem(
            text = {
                Text(
                    stringResource(R.string.text_feedback),
                    style = AppThemeTextStyle.Body13H
                )
            },
            onClick = onClick,
            leadingIcon = {
                Image(
                    painter = painterResource(R.drawable.icon_feedback),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
            }
        )
    }
}

@Composable
fun TryOnHistoryButton(onClick: () -> Unit = {}) {
    Box {
        DropdownMenuItem(
            text = {
                Text(
                    stringResource(R.string.text_view_try_ons),
                    style = AppThemeTextStyle.Body13H
                )
            },
            onClick = onClick,
            leadingIcon = {
                Image(
                    painter = painterResource(R.drawable.icon_tryon_history),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
            }
        )
    }
}

@Composable
fun DeletePostButton(onClick: () -> Unit = {}) {
    DropdownMenuItem(
        text = {
            Text(
                stringResource(R.string.text_delete_post),
                style = AppThemeTextStyle.Body16H
            )
        },
        onClick = onClick,
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
            )
        }
    )
}
