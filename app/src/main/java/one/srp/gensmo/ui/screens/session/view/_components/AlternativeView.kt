package one.srp.gensmo.ui.screens.session.view._components

import android.widget.Toast
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.launch
import one.srp.core.network.utils.JSON
import one.srp.core.network.model.MoodboardTryOnGarmentItem
import one.srp.core.network.model.MoodboardTryOnItems
import one.srp.core.network.model.TryOnParams
import one.srp.core.network.model.TryOnTaskTryOnItems
import one.srp.gensmo.data.remote.ProductService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.collage.MoodboardTryOnPanel
import one.srp.gensmo.ui.components.collage.TryOnPanelDrawer
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.loading.BaseLoading
import timber.log.Timber


@Composable
fun AlternativesView(
    open: Boolean,
    onClose: () -> Unit = {},
    moodboardId: String?,
    onCommit: (TryOnParams) -> Unit = {},
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    var loading by remember { mutableStateOf(false) }
    var tryOnProductResult by remember { mutableStateOf<TryOnTaskTryOnItems?>(null) }

    suspend fun getAlternatives(id: String) {
        try {
            loading = true
            val res = ProductService.api.getTryOnProduct(id)
            if (res.isSuccessful) {
                res.body()?.let {
                    tryOnProductResult = it
                } ?: run {
                    onClose()
                    Toast.makeText(context, "Get more looks failed.", Toast.LENGTH_SHORT).show()
                }
            } else {
                onClose()
                Toast.makeText(context, "Get more looks failed.", Toast.LENGTH_SHORT).show()
            }
            loading = false
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    LaunchedEffect(open, moodboardId) {
        if (!open) return@LaunchedEffect

        moodboardId?.let {
            if (tryOnProductResult == null) {
                getAlternatives(it)
            }
        }
    }

    fun commit(selectedItems: List<MoodboardTryOnGarmentItem>) {
        onClose()
        tryOnProductResult?.let { it ->
            coroutineScope.launch {
                val modelId = UserDataStoreManager.getModelInfo().second

                val selectedProducts =
                    selectedItems.mapNotNull { selected ->
                        listOfNotNull(
                            it.box1,
                            it.box2
                        ).flatMap { boxItems ->
                            boxItems.flatMap { boxItem ->
                                listOfNotNull(
                                    boxItem.top,
                                    boxItem.bottom,
                                    boxItem.fullBody
                                ).flatten()
                            }
                        }.find { p -> p.globalId == selected.globalId }
                    }

                val imageType =
                    selectedItems.filter { it.itemType == "user_image" }

                val params =
                    TryOnParams(
                        modelId = modelId,
                        isAsync = true,
                        moodboardId = moodboardId,
                        internalImageList = selectedItems.mapNotNull { p ->
                            p.imageUrl ?: p.mainImage?.link
                        },
                        products = selectedProducts,
                        userImage = imageType.firstOrNull()?.imageUrl,
                        userImageTag = imageType.firstOrNull()?.garmentType,
                    )

                onCommit(params)
            }
        }
    }

    TryOnPanelDrawer(open = open, onClose = { onClose() }) {
        if (loading) {
            BaseLoading(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.5f)
            )
        } else {
            tryOnProductResult?.let {
                MoodboardTryOnPanel(
                    items = JSON.decodeFromString<MoodboardTryOnItems>(
                        JSON.encodeToString(TryOnTaskTryOnItems.serializer(), it)
                    ),
                    onClose = { onClose() },
                    onTryOn = { commit(it) }
                )
            } ?: run {
                Box(modifier = Modifier.fillMaxHeight(0.5f)) {
                    BaseError(onClick = { onClose() })
                }
            }
        }
    }
}
