package one.srp.gensmo.ui.screens.recommend.hashtag._components.text

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * A composable that renders markdown text with basic formatting support.
 * Supports headers, lists, links, bold, and italic text.
 * Handles malformed markdown gracefully with plain text fallback.
 */
@Composable
fun MarkdownText(
    markdown: String,
    modifier: Modifier = Modifier,
    scrollable: Boolean = true
) {
    val parsedContent = remember(markdown) { parseMarkdown(markdown) }
    
    val contentModifier = if (scrollable) {
        modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    } else {
        modifier
            .fillMaxWidth()
            .padding(16.dp)
    }
    
    Column(modifier = contentModifier) {
        parsedContent.forEach { element ->
            when (element) {
                is MarkdownElement.Header -> {
                    Text(
                        text = element.text,
                        style = getHeaderStyle(element.level),
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
                is MarkdownElement.ListItem -> {
                    Text(
                        text = "• ${element.text}",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp, bottom = 4.dp)
                    )
                }
                is MarkdownElement.NumberedListItem -> {
                    Text(
                        text = "${element.number}. ${element.text}",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp, bottom = 4.dp)
                    )
                }
                is MarkdownElement.Paragraph -> {
                    val annotatedString = buildAnnotatedString {
                        parseInlineMarkdown(element.text, this)
                    }
                    
                    Text(
                        text = annotatedString,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }
                is MarkdownElement.PlainText -> {
                    Text(
                        text = element.text,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }
            }
        }
    }
}

/**
 * Sealed class representing different markdown elements
 */
private sealed class MarkdownElement {
    data class Header(val text: String, val level: Int) : MarkdownElement()
    data class ListItem(val text: String) : MarkdownElement()
    data class NumberedListItem(val text: String, val number: Int) : MarkdownElement()
    data class Paragraph(val text: String) : MarkdownElement()
    data class PlainText(val text: String) : MarkdownElement()
}

/**
 * Parses markdown text into a list of MarkdownElement objects
 */
private fun parseMarkdown(markdown: String): List<MarkdownElement> {
    if (markdown.isBlank()) return emptyList()
    
    return try {
        val lines = markdown.lines()
        val elements = mutableListOf<MarkdownElement>()
        var currentParagraph = StringBuilder()
        
        for (line in lines) {
            val trimmedLine = line.trim()
            
            when {
                // Headers
                trimmedLine.startsWith("###") -> {
                    finalizeParagraph(currentParagraph, elements)
                    elements.add(MarkdownElement.Header(trimmedLine.removePrefix("###").trim(), 3))
                }
                trimmedLine.startsWith("##") -> {
                    finalizeParagraph(currentParagraph, elements)
                    elements.add(MarkdownElement.Header(trimmedLine.removePrefix("##").trim(), 2))
                }
                trimmedLine.startsWith("#") -> {
                    finalizeParagraph(currentParagraph, elements)
                    elements.add(MarkdownElement.Header(trimmedLine.removePrefix("#").trim(), 1))
                }
                // Bullet lists
                trimmedLine.startsWith("- ") || trimmedLine.startsWith("* ") -> {
                    finalizeParagraph(currentParagraph, elements)
                    val text = trimmedLine.substring(2).trim()
                    elements.add(MarkdownElement.ListItem(text))
                }
                // Numbered lists
                trimmedLine.matches(Regex("^\\d+\\. .*")) -> {
                    finalizeParagraph(currentParagraph, elements)
                    val parts = trimmedLine.split(". ", limit = 2)
                    if (parts.size == 2) {
                        val number = parts[0].toIntOrNull() ?: 1
                        elements.add(MarkdownElement.NumberedListItem(parts[1], number))
                    }
                }
                // Empty line
                trimmedLine.isEmpty() -> {
                    finalizeParagraph(currentParagraph, elements)
                }
                // Regular text
                else -> {
                    if (currentParagraph.isNotEmpty()) {
                        currentParagraph.append(" ")
                    }
                    currentParagraph.append(trimmedLine)
                }
            }
        }
        
        // Finalize any remaining paragraph
        finalizeParagraph(currentParagraph, elements)
        
        elements
    } catch (e: Exception) {
        // Fallback to plain text if parsing fails
        listOf(MarkdownElement.PlainText(markdown))
    }
}

/**
 * Finalizes the current paragraph and adds it to elements if not empty
 */
private fun finalizeParagraph(paragraph: StringBuilder, elements: MutableList<MarkdownElement>) {
    if (paragraph.isNotEmpty()) {
        elements.add(MarkdownElement.Paragraph(paragraph.toString()))
        paragraph.clear()
    }
}

/**
 * Parses inline markdown formatting (bold, italic, links) within text
 */
private fun parseInlineMarkdown(text: String, builder: AnnotatedString.Builder) {
    try {
        var currentIndex = 0
        val linkRegex = Regex("\\[([^\\]]+)\\]\\(([^)]+)\\)")
        val boldRegex = Regex("\\*\\*([^*]+)\\*\\*")
        val italicRegex = Regex("\\*([^*]+)\\*")
        
        // Find all matches and sort by position
        val allMatches = mutableListOf<MatchInfo>()
        
        linkRegex.findAll(text).forEach { match ->
            allMatches.add(MatchInfo(match.range.first, match.range.last + 1, MatchType.LINK, match))
        }
        
        boldRegex.findAll(text).forEach { match ->
            allMatches.add(MatchInfo(match.range.first, match.range.last + 1, MatchType.BOLD, match))
        }
        
        italicRegex.findAll(text).forEach { match ->
            allMatches.add(MatchInfo(match.range.first, match.range.last + 1, MatchType.ITALIC, match))
        }
        
        allMatches.sortBy { it.start }
        
        // Process matches in order
        for (matchInfo in allMatches) {
            // Add text before match
            if (currentIndex < matchInfo.start) {
                builder.append(text.substring(currentIndex, matchInfo.start))
            }
            
            when (matchInfo.type) {
                MatchType.LINK -> {
                    val linkText = matchInfo.match.groupValues[1]
                    val linkUrl = matchInfo.match.groupValues[2]
                    try {
                        builder.withLink(
                            LinkAnnotation.Url(linkUrl)
                        ) {
                            withStyle(
                                SpanStyle(
                                    color = Color.Blue,
                                    textDecoration = TextDecoration.Underline
                                )
                            ) {
                                append(linkText)
                            }
                        }
                    } catch (e: Exception) {
                        // Fallback to plain text if URL is malformed
                        builder.append(linkText)
                    }
                }
                MatchType.BOLD -> {
                    val boldText = matchInfo.match.groupValues[1]
                    builder.withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                        append(boldText)
                    }
                }
                MatchType.ITALIC -> {
                    val italicText = matchInfo.match.groupValues[1]
                    builder.withStyle(SpanStyle(fontStyle = FontStyle.Italic)) {
                        append(italicText)
                    }
                }
            }
            
            currentIndex = matchInfo.end
        }
        
        // Add remaining text
        if (currentIndex < text.length) {
            builder.append(text.substring(currentIndex))
        }
    } catch (e: Exception) {
        // Fallback to plain text if inline parsing fails
        builder.append(text)
    }
}

/**
 * Data class to hold match information for sorting
 */
private data class MatchInfo(
    val start: Int,
    val end: Int,
    val type: MatchType,
    val match: MatchResult
)

/**
 * Enum for different types of inline markdown
 */
private enum class MatchType {
    LINK, BOLD, ITALIC
}

/**
 * Returns the appropriate text style for header levels
 */
@Composable
private fun getHeaderStyle(level: Int): TextStyle {
    return when (level) {
        1 -> MaterialTheme.typography.headlineLarge.copy(
            fontWeight = FontWeight.Bold,
            fontSize = 24.sp
        )
        2 -> MaterialTheme.typography.headlineMedium.copy(
            fontWeight = FontWeight.Bold,
            fontSize = 20.sp
        )
        3 -> MaterialTheme.typography.headlineSmall.copy(
            fontWeight = FontWeight.Bold,
            fontSize = 18.sp
        )
        else -> MaterialTheme.typography.bodyLarge.copy(
            fontWeight = FontWeight.Bold
        )
    }
}