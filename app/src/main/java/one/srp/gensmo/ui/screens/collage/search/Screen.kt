package one.srp.gensmo.ui.screens.collage.search

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.rememberAsyncImagePainter
import coil3.request.ImageRequest
import coil3.size.Scale
import one.srp.gensmo.R
import one.srp.core.network.model.SearchInspoItem
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import one.srp.gensmo.viewmodel.search.SearchEvent
import timber.log.Timber

@Composable
fun CollageSearchScreen(
    navActions: NavActions,
    query: String = "",
    imageUrl: String? = null,
    budget: String? = null,
    stylesList: String? = null,
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
) {
    val status by collageSearchViewModel.searchStatus.collectAsState()
    
    // 只在首次进入时执行一次
    LaunchedEffect(Unit) {
        // 解码参数
        val decodedQuery = java.net.URLDecoder.decode(query, "UTF-8")
        val decodedImageUrl = imageUrl?.let { 
            if (it.isNotEmpty()) java.net.URLDecoder.decode(it, "UTF-8") else null 
        }
        val decodedBudget = budget?.let {
            if (it.isNotEmpty()) java.net.URLDecoder.decode(it, "UTF-8") else null
        }
        val decodedStylesList = stylesList?.let {
            if (it.isNotEmpty()) java.net.URLDecoder.decode(it, "UTF-8") else null
        }
        
        Timber.d("CollageSearchScreen query: $decodedQuery")
        Timber.d("CollageSearchScreen imageUrl: $decodedImageUrl")
        Timber.d("CollageSearchScreen budget: $decodedBudget")
        Timber.d("CollageSearchScreen stylesList: $decodedStylesList")

        if ((decodedQuery.isNotEmpty() && decodedQuery != "") || !decodedImageUrl.isNullOrEmpty()) {
            collageSearchViewModel.updateQuery(decodedQuery)
            collageSearchViewModel.updateImageURI(decodedImageUrl)
            collageSearchViewModel.updateBudget(decodedBudget)
            collageSearchViewModel.updateStylesList(decodedStylesList)
            collageSearchViewModel.search()
        }
    }

    LaunchedEffect(Unit) {
        collageSearchViewModel.searchFlow.collect { event ->
            when (event) {
                is SearchEvent.Created -> {
                    collageSearchViewModel.updateRemixProduct(null)
                    navActions.navigateFromSearchToCollageTask(event.taskId)
                }
                is SearchEvent.NavigateToLogin -> {
                    navActions.navigateFromCollageSearchToLogin(query = collageSearchViewModel.query.value, imageUrl = collageSearchViewModel.imageURI.value)
                }
            }
        }
    }

    when (status) {
        TaskStatus.Fail -> {
            Column(
                modifier = Modifier
                    .statusBarsPadding()
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Failed to create collage",
                    fontSize = 20.sp,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Please try again later",
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(24.dp))
                androidx.compose.material3.Button(
                    onClick = { navActions.back() }
                ) {
                    Text("Return")
                }
            }
        }
        else -> {
            LoadingPage(navActions, collageSearchViewModel)
        }
    }
}

@Composable
fun LoadingPage(
    navActions: NavActions = NavActions(),
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
) {
    val query by collageSearchViewModel.query.collectAsState()
    val imageURI by collageSearchViewModel.imageURI.collectAsState()
    val inspo by collageSearchViewModel.inspo.collectAsState()
    val processingText by collageSearchViewModel.processingText.collectAsState()

    Box(modifier = Modifier.fillMaxSize()) {
        // 添加背景图片
        Image(
            painter = painterResource(id = R.drawable.home_bg),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds
        )
        
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            TopBar(immersive = true, onBack = { 
                collageSearchViewModel.cancelSearch()
                navActions.back() 
            })

            Box(
                modifier = Modifier
                    .weight(0.6f)  // 合并两个Box的weight
                    .fillMaxWidth()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .zIndex(2f)  // 使用zIndex修饰符
                ) {
                    LoadingItems(
                        query = query, imageURI = imageURI, inspo = inspo
                    )
                }

                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .zIndex(1f),  // 使用zIndex修饰符
                    contentAlignment = Alignment.Center  // 将contentAlignment放在Box构造函数中
                ) {
                    val infiniteTransition = rememberInfiniteTransition(label = "scale")
                    val scale by infiniteTransition.animateFloat(
                        initialValue = 0.8f,
                        targetValue = 1.2f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(2000, easing = LinearEasing),
                            repeatMode = RepeatMode.Reverse
                        ),
                        label = "scale"
                    )

                    Image(
                        painter = painterResource(id = R.drawable.icon_circle),
                        contentDescription = "Loading circle",
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 40.dp)
                            .graphicsLayer { 
                                scaleX = scale
                                scaleY = scale
                            }
                    )
                }
            }

            Box(
                modifier = Modifier
                    .weight(0.4f)
                    .fillMaxWidth(),
                contentAlignment = Alignment.BottomCenter
            ) {
                Column(
                    modifier = Modifier.padding(bottom = 48.dp)
                ) {
                    LoadingHint(text = processingText)
                    Spacer(modifier = Modifier.padding(vertical = 8.dp))
                    LoadingProgress()
                }
            }
        }
    }
}

@Composable
fun LoadingItems(
    query: String? = null,
    imageURI: String? = null,
    inspo: List<SearchInspoItem>? = null,
) {
    val infiniteTransition = rememberInfiniteTransition(label = "rotation")
    val angle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(24000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    val radius = 100.dp  // 圆的半径，可以根据需要调整

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // 使用极坐标计算每个项目的位置
        val items = listOfNotNull(
            query?.let { queryText ->
                Box(
                    modifier = Modifier
                        .widthIn(max = 220.dp)
                        .offset(x = radius * kotlin.math.cos(Math.toRadians(angle.toDouble())).toFloat(),
                               y = radius * kotlin.math.sin(Math.toRadians(angle.toDouble())).toFloat())
                        .background(
                            Color.White, 
                            shape = RoundedCornerShape(percent = 50)
                        )
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = queryText,
                        color = Color.Black,
                        style = androidx.compose.ui.text.TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                        ),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
                null
            },
            imageURI?.let {
                Box(
                    modifier = Modifier
                        .size(60.dp)
                        .offset(x = radius * kotlin.math.cos(Math.toRadians((angle + 90f).toDouble())).toFloat(),
                               y = radius * kotlin.math.sin(Math.toRadians((angle + 90f).toDouble())).toFloat())
                        .clip(RoundedCornerShape(10.dp))  // 添加圆角
                ) {
                    Image(
                        painter = rememberAsyncImagePainter(
                            ImageRequest.Builder(LocalContext.current)
                                .data(data = it)
                                .apply {
                                    scale(Scale.FILL)
                                }.build()
                        ),
                        contentDescription = "Selected image",
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                null
            },
            inspo?.getOrNull(0)?.let { inspoItem ->
                // 当 query 与 inspo 的 showQuery 一致时显示 emoji
                if (query == inspoItem.searchQuery) {
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .offset(x = radius * kotlin.math.cos(Math.toRadians((angle + 180f).toDouble())).toFloat(),
                                   y = radius * kotlin.math.sin(Math.toRadians((angle + 180f).toDouble())).toFloat())
                            .clip(androidx.compose.foundation.shape.CircleShape)
                            .background(Color.White)
                            .padding(8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = inspoItem.emoji ?: "",
                            fontSize = 20.sp
                        )
                    }
                    null
                } else null
            }
        )

        items.forEachIndexed { index, item ->
            val itemAngle = angle + (360f / items.size) * index
            val xOffset = radius * kotlin.math.cos(Math.toRadians(itemAngle.toDouble())).toFloat()
            val yOffset = radius * kotlin.math.sin(Math.toRadians(itemAngle.toDouble())).toFloat()

            Box(
                modifier = Modifier
                    .offset(x = xOffset, y = yOffset)
                    .widthIn(max = 220.dp)
                    .background(
                        Color.White, 
                        // 将圆角设置为很大的值，这样会自动变成半圆形
                        shape = RoundedCornerShape(percent = 50)
                    )
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = item.toString(),  // 将 item 显式转换为 String
                    fontSize = 16.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
    }
}

@Composable
fun LoadingHint(text: String? = null) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        val gradientColors = listOf(Color(0xFF0224FF), Color(0xFFBB62F1))
        val brush = Brush.horizontalGradient(gradientColors)
        
        Text(
            text = text ?: "Generating an AI collage for this search...", 
            textAlign = TextAlign.Center, 
            modifier = Modifier.fillMaxWidth(),
            style = androidx.compose.ui.text.TextStyle(
                brush = brush
            )
        )
    }
}

@Composable
fun LoadingProgress() {
    // 创建动画进度值
    val infiniteTransition = rememberInfiniteTransition(label = "progress")
    val progress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = 30000, // 一分钟 = 60000毫秒
                easing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f) // 缓动函数，使进度先快后慢
            ),
            repeatMode = RepeatMode.Restart
        ),
        label = "progress"
    )
    
    // 定义渐变色
    val gradientBrush = Brush.horizontalGradient(
        colors = listOf(
            Color(0xFF0224FF), // 蓝色
            Color(0xFFB862F1)  // 紫色
        )
    )
    
    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 背景进度条 - 圆角
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 40.dp)
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp))
                .background(Color.LightGray.copy(alpha = 0.3f))
        )
        
        // 前景渐变进度条 - 圆角
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 40.dp)
                .height(4.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(progress)
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(brush = gradientBrush)
            )
        }
    }
}