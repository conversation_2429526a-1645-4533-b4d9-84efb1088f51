package one.srp.gensmo.ui.screens.user.profile

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.BrandItem
import one.srp.core.network.model.BrandProductItem
import one.srp.core.network.model.ChatMessageRole
import one.srp.core.network.model.SearchParams
import one.srp.core.network.model.SearchQueryMessage
import one.srp.core.network.model.SearchQueryMessageWrapper
import one.srp.core.network.model.TryOnParams
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.navigate.BottomNavigationBar
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._components.ProfileAssets
import one.srp.gensmo.ui.screens.user.profile._components.ProfileHeader
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.search.SearchExtensionViewModel
import timber.log.Timber
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserProfileScreen(
    navActions: NavActions,
    viewModel: UserProfileViewModel = hiltViewModel(),
    onClearFeed: () -> Unit = {},
    onClearTryOn: () -> Unit = {},
    openTryOn: (TryOnParams) -> Unit = {},
    createSession: (SearchQueryMessage) -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    val searchExtensionViewModel: SearchExtensionViewModel = hiltViewModel()

    LaunchedEffect(Unit) {
        searchExtensionViewModel.getImaginationQuery()
    }

    var searchPanelVisible by remember { mutableStateOf(false) }
    val openSearch = { searchPanelVisible = true }
    val closeSearch = { searchPanelVisible = false }
    var placeholder by remember { mutableStateOf("Describe the style or occasion you want to explore...") }
    fun onSearch(
        q: String,
        img: String,
        stylesList: String?,
        budget: String?,
        brand: BrandItem? = null,
        brandProduct: BrandProductItem? = null,
        isProModeEnabled: Boolean = false,
    ) {
        if (q.trim().isNotEmpty()) {
            Timber.d("onSearch: $q, $img, $stylesList, $budget")
            createSession(
                SearchQueryMessage(
                    sessionId = "default",
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = SearchQueryMessageWrapper(
                        searchQuery = SearchParams(
                            query = q,
                            imageUrl = img,
                            debugLevel = 0,
                            budget = budget ?: "",
                            isAsync = true,
                            route = "",
                            isPresetQuery = false,
                            moodboardVersion = "v2",
                            inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                                ?.filter { it.isNotEmpty() } ?: emptyList()),
                            brand = brand?.brand,
                            specifiedProduct = brandProduct?.product,
                            isAgent = isProModeEnabled,
                        )
                    )
                )
            )
        }
    }
    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf("") }
    var capturedBrandItem by remember { mutableStateOf<BrandItem?>(null) }
    var capturedBrandProduct by remember { mutableStateOf<BrandProductItem?>(null) }

    // Metric helper for profile page
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Profile)
    LaunchedEffect(Unit) {
        // 埋点：profile 页面全局曝光
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView
            )
        )
    }



    Scaffold(bottomBar = {
        BottomNavigationBar(
            navActions = navActions,
            refer = EventRefer.Profile,
            onActionClick = { openSearch() }
        )
    }) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            ProfileHeader(
                navActions = navActions,
                viewModel = viewModel,
                onFollowersClick = {
                    // 获取当前用户ID并导航到followers页面
                    coroutineScope.launch {
                        viewModel.getCurrentUserId()?.let { userId ->
                            navActions.navigateToFollowers(userId)
                        }
                    }
                },
                onFollowingClick = {
                    // 获取当前用户ID并导航到following页面
                    coroutineScope.launch {
                        viewModel.getCurrentUserId()?.let { userId ->
                            navActions.navigateToFollowing(userId)
                        }
                    }
                }
            )
            ProfileAssets(
                modifier = Modifier,
                navActions = navActions,
                onClearFeed = onClearFeed,
                onClearTryOn = onClearTryOn,
                openTryOn = openTryOn
            )
        }
    }

    SearchPanel(
        placeholderText = placeholder,
        isVisible = searchPanelVisible && !showCameraPicker,
        onDismiss = { closeSearch() },
        viewModel = searchExtensionViewModel,
        imageUrl = capturedImageUri,
        searchQueryText = capturedSearchText,
        onCameraRequest = { showCameraPicker = true },
        onSearch = { q, img, stylesList, budget, isProModeEnabled ->
            onSearch(q, img, stylesList, budget, capturedBrandItem, capturedBrandProduct, isProModeEnabled)
        },
        onUpdateQuery = { query -> capturedSearchText = query },
        navActions = navActions
    )
    if (showCameraPicker) {
        CameraPicker(
            onPhotoTaken = { uri, searchQueryWords ->
                capturedImageUri = uri.toString()
                capturedSearchText += searchQueryWords
                showCameraPicker = false
            },
            onMiss = {
                showCameraPicker = false
                capturedImageUri = null
            },
            onSelectBrand = { q, b, bp ->
//                capturedBrandItem = b
//                capturedBrandProduct = bp
//                capturedImageUri = bp.showImage
//                capturedSearchText += q
                showCameraPicker = false
                onSearch(q, bp.showImage?:"", null, null, b, bp, false)
            }
        )
    }
}
