package one.srp.gensmo.ui.components.action

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.gensmo.data.remote.CommunityService
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.viewmodel.tryon.CreateViewModel

@Composable
fun FollowAction(
    initialFollowStatus: FollowStatus? = null,
    userId: String,
    onFollow: (Boolean, FollowStatus) -> Unit = { _, _ -> },
    content: @Composable (Bo<PERSON>an, FollowStatus?, (() -> Unit)) -> Unit,
) {
    // 从 initialFollowStatus 推导出初始的 isFollowing 状态
    val initialIsFollowing = when (initialFollowStatus) {
        FollowStatus.Followed, FollowStatus.Mutual -> true
        FollowStatus.FollowedBack, FollowStatus.NotFollowed -> false
        null -> false
    }
    
    var isFollowing by remember(initialIsFollowing) { mutableStateOf(initialIsFollowing) }
    var followStatus by remember(initialFollowStatus) { mutableStateOf(initialFollowStatus) }
    var hasInitialized by remember { mutableStateOf(initialFollowStatus != null) }
    var isProcessing by remember { mutableStateOf(false) } // 添加处理状态

    LaunchedEffect(userId) {
        // 如果已经有初始状态，跳过 API 调用
        if (hasInitialized) return@LaunchedEffect
        
        runCatching {
            val res = CommunityService.api.checkFollowStatus(userId)
            if (res.isSuccessful) {
                val status = res.body()?.status
                val newFollowStatus = when (status) {
                    FollowStatus.Followed.value -> FollowStatus.Followed
                    FollowStatus.Mutual.value -> FollowStatus.Mutual
                    FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                    FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                    else -> null
                }
                followStatus = newFollowStatus
                
                // 只有在首次初始化时才更新 isFollowing，避免与 initialState 冲突
                if (!hasInitialized) {
                    when (status) {
                        FollowStatus.Followed.value, FollowStatus.Mutual.value -> {
                            isFollowing = true
                        }
                        FollowStatus.FollowedBack.value, FollowStatus.NotFollowed.value -> {
                            isFollowing = false
                        }
                        else -> {
                            // 如果 API 返回的状态无法识别，保持 initialState
                            isFollowing = initialIsFollowing
                        }
                    }
                    hasInitialized = true
                }
            } else {
                // API 调用失败时，保持初始状态
                if (!hasInitialized) {
                    isFollowing = initialIsFollowing
                    hasInitialized = true
                }
            }
        }.onFailure {
            // 网络错误时，保持初始状态
            if (!hasInitialized) {
                isFollowing = initialIsFollowing
                hasInitialized = true
            }
        }
    }

    val coroutineScope = rememberCoroutineScope()
    fun clickFollow() {
        if (isProcessing) return // 防止重复点击
        
        val previousState = isFollowing // 保存操作前的状态
        val newState = !isFollowing // 计算新的目标状态
        isProcessing = true
        
        // 立即更新 UI 状态，显示正在处理
        isFollowing = newState

        coroutineScope.launch {
            try {
                if (newState) {
                    val response = CommunityService.api.followUser(userId)
                    if (response.isSuccessful) {
                        // 根据服务器返回的实际状态更新 followStatus
                        val status = response.body()?.status
                        val newFollowStatus = when (status) {
                            FollowStatus.Followed.value -> FollowStatus.Followed
                            FollowStatus.Mutual.value -> FollowStatus.Mutual
                            FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                            else -> FollowStatus.Followed // 默认值
                        }
                        followStatus = newFollowStatus
                        
                        // API 调用成功后，通知父组件状态变化
                        onFollow(newState, newFollowStatus)
                    } else {
                        // 操作失败，回滚状态
                        isFollowing = previousState
                        followStatus = initialFollowStatus
                    }
                } else {
                    val response = CommunityService.api.unfollowUser(userId)
                    if (response.isSuccessful) {
                        // 根据服务器返回的实际状态更新 followStatus
                        val status = response.body()?.status
                        val newFollowStatus = when (status) {
                            FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                            FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                            else -> FollowStatus.NotFollowed // 默认值
                        }
                        followStatus = newFollowStatus
                        
                        // API 调用成功后，通知父组件状态变化
                        onFollow(newState, newFollowStatus)
                    } else {
                        // 操作失败，回滚状态
                        isFollowing = previousState
                        followStatus = initialFollowStatus
                    }
                }
            } catch (e: Exception) {
                // 网络错误，回滚状态
                isFollowing = previousState
                followStatus = initialFollowStatus
            } finally {
                isProcessing = false
            }
        }
    }

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "FollowAction")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickFollowWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            clickFollow()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    content(isFollowing, followStatus) { clickFollowWithCheck() }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        clickFollow()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        clickFollow()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}

enum class FollowStatus(val value: String) {
    NotFollowed("notFollowed"),
    Followed("followed"),
    Mutual("mutual"),
    FollowedBack("followedBack"),
}
