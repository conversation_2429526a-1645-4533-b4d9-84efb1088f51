package one.srp.gensmo.ui.navigation

import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import timber.log.Timber

class NavActions(private val navController: NavHostController? = null) {
    val currentRoute: String? get() = navController?.currentDestination?.route

    // 添加一个标志位来防止短时间内的重复操作
    private var isBackOperationInProgress = false

    fun back() {
        if (navController != null && !isBackOperationInProgress) {
            try {
                isBackOperationInProgress = true

                // 使用公开API检查导航状态
                val canNavigateBack = try {
                    val currentEntry = navController.currentBackStackEntry
                    val previousEntry = navController.previousBackStackEntry

                    // 多重安全检查: 只要 previousEntry 存在，就认为可以返回
                    currentEntry != null &&
                            previousEntry != null &&
                            currentEntry.lifecycle.currentState.isAtLeast(androidx.lifecycle.Lifecycle.State.CREATED)
                } catch (e: Exception) {
                    Timber.w(e, "Error checking navigation state, assuming cannot navigate back")
                    false
                }

                if (canNavigateBack) {
                    val currentRoute = navController.currentDestination?.route
                    val previousRoute = navController.previousBackStackEntry?.destination?.route

                    Timber.d(
                        "Attempting to navigate back from: %s to: %s",
                        currentRoute,
                        previousRoute
                    )

                    // 使用最安全的popBackStack方法，添加全面的异常处理
                    val navigationSuccess = try {
                        navController.popBackStack()
                    } catch (e: IndexOutOfBoundsException) {
                        Timber.e(
                            e,
                            "IndexOutOfBoundsException during popBackStack - attempting navigateUp fallback"
                        )
                        try {
                            navController.navigateUp()
                        } catch (fallbackException: Exception) {
                            Timber.e(fallbackException, "NavigateUp fallback also failed")
                            false
                        }
                    } catch (e: IllegalStateException) {
                        Timber.e(
                            e,
                            "IllegalStateException during popBackStack - navigation state corrupted"
                        )
                        false
                    } catch (e: Exception) {
                        Timber.e(e, "Unexpected exception during popBackStack")
                        false
                    }

                    if (navigationSuccess) {
                        Timber.d(
                            "Successfully navigated back from %s to %s",
                            currentRoute,
                            previousRoute
                        )
                    } else {
                        Timber.w("Navigation back failed for route: %s", currentRoute)
                        // 如果常规返回失败，尝试导航到根页面
                        try {
                            if (currentRoute != NavRoutes.Feed.Recommend.route) {
                                navController.navigate(NavRoutes.Feed.Recommend.route) {
                                    popUpTo(0) { inclusive = false }
                                    launchSingleTop = true
                                }
                                Timber.d("Fallback navigation to feed succeeded")
                            }
                        } catch (fallbackException: Exception) {
                            Timber.e(fallbackException, "Fallback navigation to feed also failed")
                        }
                    }
                } else {
                    // 记录无法返回的具体原因
                    val currentRoute = navController.currentDestination?.route
                    Timber.d(
                        "Cannot navigate back, attempting to go to home screen from %s",
                        currentRoute ?: "unknown"
                    )

                    // 如果无法返回但又不在主页，直接尝试导航到主页
                    if (currentRoute != null && currentRoute != NavRoutes.Feed.Recommend.route) {
                        try {
                            navController.navigate(NavRoutes.Feed.Recommend.route) {
                                popUpTo(0) { inclusive = true } // 清空栈，只保留主页
                                launchSingleTop = true
                            }
                            Timber.d("Emergency navigation to feed succeeded")
                        } catch (emergencyException: Exception) {
                            Timber.e(emergencyException, "Emergency navigation to feed failed")
                        }
                    }
                }

            } catch (e: Exception) {
                Timber.e(e, "Critical error in navigation back operation")
                // 最后的紧急处理 - 尝试最基本的返回操作
                try {
                    if (navController.previousBackStackEntry != null) {
                        navController.navigateUp()
                        Timber.d("Emergency navigateUp succeeded")
                    } else {
                        // 如果连 navigateUp 都不行，尝试导航到根页面
                        navController.navigate(NavRoutes.Feed.Recommend.route) {
                            popUpTo(0) { inclusive = true } // 清空栈
                            launchSingleTop = true
                        }
                        Timber.d("Final emergency navigation to feed succeeded")
                    }
                } catch (emergencyException: Exception) {
                    Timber.e(emergencyException, "All navigation fallback attempts failed")
                }
            } finally {
                // 使用Handler确保防抖逻辑在导航状态更新后执行
                Handler(Looper.getMainLooper()).postDelayed({
                    isBackOperationInProgress = false
                }, 1000)
            }
        } else {
            if (navController == null) {
                Timber.w("Cannot navigate back - navController is null")
            }
            if (isBackOperationInProgress) {
                Timber.d("Back operation already in progress, ignoring duplicate request")
            }
        }
    }

    fun navigateToUserProfile(skip: Boolean = false) {
        if (navController?.currentDestination?.route != NavRoutes.User.Profile.route) {
            navController?.navigate(NavRoutes.User.Profile.route) {
                launchSingleTop = true
                restoreState = true

                currentRoute?.let {
                    if (skip) {
                        popUpTo(it) { inclusive = true }
                    }
                }
            }
        }
    }

    fun navigateToUserSettings() {
        navController?.navigate(NavRoutes.User.Settings.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToUserAccount() {
        navController?.navigate(NavRoutes.User.Account.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToUserAccountEdit() {
        navController?.navigate(NavRoutes.User.AccountEdit.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToUserLogin() {
        navController?.navigate(NavRoutes.User.Login.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToSessionLogin() {
        navController?.navigate(NavRoutes.Session.Login.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToOnboardLogin() {
        navController?.navigate(NavRoutes.Onboard.Login.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToOnboardPick() {
        navController?.navigate(NavRoutes.Onboard.Pick.route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToOnboardResult(tags: List<String> = emptyList()) {
        val route = if (tags.isEmpty()) {
            "onboard/result/"
        } else {
            val tagsParam = tags.joinToString(",")
            "onboard/result/${java.net.URLEncoder.encode(tagsParam, "UTF-8")}"
        }
        navController?.navigate(route) {
            launchSingleTop = true
            restoreState = true
        }
    }

    fun navigateToFeedRecommend() {
        if (navController?.currentDestination?.route != NavRoutes.Feed.Recommend.route) {
            navController?.navigate(NavRoutes.Feed.Recommend.route) {
                launchSingleTop = true
                popUpTo(0) {
                    saveState = true
                    inclusive = false // 不清除根页面
                }
                restoreState = true
            }
            Timber.d("成功导航到主页面")
        } else {
            Timber.d("已经在主页面，无需导航")
        }
    }

    fun navigateToFeedDetail(id: String, refer: String? = null) {
        navController?.navigate(NavRoutes.Feed.Detail.createRoute(id, refer))
    }

    fun navigateToTryOnDetail(id: String, refer: String? = null) {
        navController?.navigate(NavRoutes.TryOn.Detail.createRoute(id, refer))
    }

    fun navigateToCamera(mode: String? = null) {
        navController?.navigate(NavRoutes.Camera.createRoute(mode))
    }

    fun navigateToLogin(query: String? = "", imageUrl: String? = "") {
        navController?.navigate(NavRoutes.Login.createRoute(query = query, imageUrl = imageUrl)) {
            launchSingleTop = true
        }
    }

    fun navigateFromCollageSearchToLogin(query: String? = "", imageUrl: String? = "") {
        navController?.navigate(
            NavRoutes.Login.createRoute(
                query = query ?: "", imageUrl = imageUrl ?: ""
            )
        ) {
            popUpTo(NavRoutes.Collage.Search.route) {
                inclusive = true
            }
            launchSingleTop = true
        }
    }

    fun navigateToCollageSearch(
        query: String = "",
        imageUrl: String? = null,
        budget: String? = null,
        stylesList: String? = null,
    ) {
        navController?.navigate(
            NavRoutes.Collage.Search.createRoute(
                query = query,
                imageUrl = imageUrl,
                budget = budget,
                stylesList = stylesList
            )
        )
    }

    fun navigateFromLoginToCollageSearch(query: String? = "", imageUrl: String? = "") {
        navController?.navigate(
            NavRoutes.Collage.Search.createRoute(
                query = query ?: "", imageUrl = imageUrl ?: ""
            )
        ) {
            popUpTo(NavRoutes.Login.route) {
                inclusive = true
            }
        }
    }

    fun navigateToCollageTask(id: String) {
        navController?.navigate(NavRoutes.Collage.Task.createRoute(id))
    }

    fun navigateToHistory() {
        navController?.navigate(NavRoutes.History.route)
    }

    fun navigateToUserLibrary(selectedTab: Int = 0) {
        navController?.navigate(NavRoutes.User.Library.createRoute(selectedTab))
    }

    fun navigateToPreference(source: String? = null) {
        navController?.navigate(NavRoutes.Preference.createRoute(source))
    }

    fun navigateToSaved() {
        navController?.navigate(NavRoutes.Saved.route)
    }

    fun navigateFromPreferenceToCollageSearch(query: String? = null, imageUrl: String? = null) {
        navController?.navigate(
            NavRoutes.Collage.Search.createRoute(
                query = query ?: "", imageUrl = imageUrl ?: ""
            )
        ) {
            popUpTo(NavRoutes.Preference.route) {
                inclusive = true
            }
        }
    }

    fun navigateFromSearchToCollageTask(id: String) {
        navController?.let { controller ->
            try {
                Timber.d("Navigating from search to collage task: $id")

                val collageSearchRouteInBackStack = try {
                    controller.getBackStackEntry(NavRoutes.Collage.Search.route)
                    true
                } catch (e: Exception) {
                    Timber.d("Collage search route not in back stack: ${e.message}")
                    false
                }

                val targetRoute = NavRoutes.Collage.Task.createRoute(id)
                Timber.d("Target route: $targetRoute")

                controller.navigate(targetRoute) {
                    if (collageSearchRouteInBackStack) {
                        popUpTo(NavRoutes.Collage.Search.route) {
                            inclusive = true
                        }
                        Timber.d("Popped up to and including collage search route")
                    } else {
                        val fallbackRoute =
                            controller.currentDestination?.route ?: NavRoutes.Feed.Recommend.route
                        popUpTo(fallbackRoute) {
                            inclusive = true
                        }
                        Timber.d("Popped up to fallback route: $fallbackRoute")
                    }
                    launchSingleTop = true
                }
            } catch (e: Exception) {
                Timber.e(
                    e,
                    "Error navigating from search to collage task, falling back to direct navigation"
                )
                // 如果出现异常，尝试直接导航
                try {
                    controller.navigate(NavRoutes.Collage.Task.createRoute(id)) {
                        launchSingleTop = true
                    }
                } catch (fallbackError: Exception) {
                    Timber.e(fallbackError, "Fallback navigation also failed")
                }
            }
        }
    }

    fun navigateToTryOnTask(id: String, skip: Boolean = false) {
        navController?.navigate(NavRoutes.TryOn.Task.createRoute(id)) {
            currentRoute?.let {
                if (skip) {
                    popUpTo(it) { inclusive = true }
                }
            }
        }
    }

    fun navigateToCloset() {
        if (navController?.currentDestination?.route != NavRoutes.Closet.route) {
            navController?.navigate(NavRoutes.Closet.route) {
                launchSingleTop = true
                restoreState = true
            }
        }
    }

    fun navigateToTryOnCreate(source: String? = null) {
        navController?.navigate(NavRoutes.TryOn.Create.routeWithSource(source ?: ""))
    }

    fun navigateToTryOnModel(setFace: Boolean? = false, userPreference: String? = null) {
        navController?.navigate(
            NavRoutes.TryOn.Model.createRoute(
                setFace?.toString() ?: "",
                userPreference ?: ""
            )
        )
    }

    fun navigateToSessionChat(skip: Boolean = false) {
        if (navController?.currentDestination?.route != NavRoutes.Session.Chat.route) {
            navController?.navigate(NavRoutes.Session.Chat.route) {
                launchSingleTop = true
                restoreState = true

                currentRoute?.let {
                    if (skip) {
                        popUpTo(it) { inclusive = true }
                    }
                }
            }
        }
    }

    fun navigateToSessionCollageTaskView(id: String, index: Int = 0) {
        navController?.navigate(NavRoutes.Session.View.CollageTask.createRoute(id, index))
    }

    fun navigateToSessionTryOnTaskView(id: String) {
        navController?.navigate(NavRoutes.Session.View.TryOnTask.createRoute(id))
    }

    fun navigateToUserCheckIn() {
        navController?.navigate(NavRoutes.User.CheckIn.route)
    }

    fun navigateToActivity(activityUrl: String) {
        val upgradedUrl = try {
            val uri = Uri.parse(activityUrl)
            if (uri.scheme == "http") uri.buildUpon().scheme("https").build().toString() else activityUrl
        } catch (e: Exception) {
            activityUrl
        }
        navController?.navigate(NavRoutes.Activity.createRoute(upgradedUrl)) {
            launchSingleTop = true
        }
    }

    fun navigateToProductWeb(url: String) {
        val upgradedUrl = try {
            val uri = Uri.parse(url)
            if (uri.scheme == "http") uri.buildUpon().scheme("https").build().toString() else url
        } catch (e: Exception) {
            url
        }
        navController?.navigate(NavRoutes.ProductWeb.createRoute(upgradedUrl)) {
            launchSingleTop = true
        }
    }

    fun navigateToUserPublicProfile(userId: String) {
        navController?.navigate(NavRoutes.User.PublicProfile.createRoute(userId))
    }

    fun navigateToFollowers(userId: String) {
        navController?.navigate(NavRoutes.User.Followers.createRoute(userId))
    }

    fun navigateToFollowing(userId: String) {
        navController?.navigate(NavRoutes.User.Following.createRoute(userId))
    }

    fun navigateToPostEditor(id: String? = null, type: String) {
        navController?.navigate(NavRoutes.Detail.PostEditor.createRoute(id, type))
    }

    fun navigateToHashtagDetail(hashtag: String) {
        navController?.navigate(NavRoutes.Detail.Hashtag.createRoute(hashtag))
    }

    fun navigateToNotification() {
        if (navController?.currentDestination?.route != NavRoutes.Notification.route) {
            navController?.navigate(NavRoutes.Notification.route) {
                launchSingleTop = true
                restoreState = true
            }
        }
    }

    fun popBackStack() {
        navController?.popBackStack()
    }
}

fun navigateDeeplink(navController: NavController, link: Uri) {
    // 确保导航操作在主线程中执行
    Handler(Looper.getMainLooper()).post {
        // gensmo.com/app-entry 现在由 AppsFlyer 统一处理
        // 这里只处理其他类型的深链接

        // 检查是否是 web 链接（https://开头）
        if (link.scheme == "https") {
            val navActions = NavActions(navController as? NavHostController)
            navActions.navigateToActivity(link.toString())
            return@post
        }

        val fullPath = "${link.host}${link.path}"
        when (fullPath) {
            "webview" -> {
                val encodedUrl = link.getQueryParameter("url")
                encodedUrl?.let { encoded ->
                    try {
                        val decodedUrl = Uri.decode(encoded)
                        val navActions = NavActions(navController as? NavHostController)
                        navActions.navigateToActivity(decodedUrl)
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to decode and navigate to webview URL")
                    }
                }
            }

            "tryon2" -> {
                val id = link.getQueryParameter("fetchTryonTaskId")
                id?.let {
                    try {
                        val targetRoute = NavRoutes.TryOn.Task.createRoute(it)
                        val currentRoute = navController.currentDestination?.route

                        // 检查是否已经在目标页面
                        if (currentRoute != targetRoute) {
                            navController.navigate(targetRoute) {
                                launchSingleTop = true
                                // 如果当前是启动页面，清除栈中重复页面
                                if (currentRoute == NavRoutes.Feed.Recommend.route) {
                                    popUpTo(NavRoutes.Feed.Recommend.route) {
                                        inclusive = false  // 保留 feed 页面作为返回目标
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to navigate to try-on task")
                    }
                }
            }

            "feed/comboDetail" -> {
                val id = link.getQueryParameter("feed_id")
                val type = link.getQueryParameter("feed_type")
                id?.let {
                    val targetRoute = if (type == "try_on") {
                        NavRoutes.TryOn.Detail.createRoute(it)
                    } else {
                        NavRoutes.Feed.Detail.createRoute(it)
                    }

                    val currentRoute = navController.currentDestination?.route

                    // 检查是否已经在目标页面
                    if (currentRoute != targetRoute) {
                        try {
                            navController.navigate(targetRoute) {
                                launchSingleTop = true
                                // 如果当前是启动页面，清除栈中重复页面
                                if (currentRoute == NavRoutes.Feed.Recommend.route) {
                                    popUpTo(NavRoutes.Feed.Recommend.route) {
                                        inclusive = false
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Timber.e(e, "Failed to navigate to feed detail")
                        }
                    }
                }
            }

            "search" -> {
                val taskId = link.getQueryParameter("task_id")

                if (taskId != null) {
                    try {
                        val targetRoute = NavRoutes.Collage.Task.createRoute(taskId)
                        val currentRoute = navController.currentDestination?.route

                        // 检查是否已经在目标页面
                        if (currentRoute != targetRoute) {
                            Handler(Looper.getMainLooper()).postDelayed({
                                try {
                                    val currentRouteAfterDelay =
                                        navController.currentDestination?.route

                                    navController.navigate(targetRoute) {
                                        launchSingleTop = true
                                        // 如果当前是启动页面，清除栈中重复页面
                                        if (currentRouteAfterDelay == NavRoutes.Feed.Recommend.route) {
                                            popUpTo(NavRoutes.Feed.Recommend.route) {
                                                inclusive = false  // 保留 feed 页面作为返回目标
                                            }
                                        }
                                    }

                                } catch (e: Exception) {
                                    Timber.e(e, "Delayed navigation failed")
                                }
                            }, 500) // 500ms延时
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to navigate to collage task")
                    }
                }
            }

            "topic/detail" -> {
                val topic = link.getQueryParameter("topic")
                topic?.let {
                    try {
                        val navActions = NavActions(navController as? NavHostController)
                        // 去掉 topic 参数中的 # 符号
                        val cleanTopic = it.removePrefix("#")
                        navActions.navigateToHashtagDetail(cleanTopic)
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to navigate to hashtag detail")
                    }
                }
            }

            "user/profile" -> {
                val userId = link.getQueryParameter("userId")
                userId?.let {
                    try {
                        val navActions = NavActions(navController as? NavHostController)
                        navActions.navigateToUserPublicProfile(it)
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to navigate to user profile")
                    }
                }
            }

            else -> {}
        }
    }
}
