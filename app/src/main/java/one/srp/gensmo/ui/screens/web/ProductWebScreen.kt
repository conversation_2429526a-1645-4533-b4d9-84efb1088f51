package one.srp.gensmo.ui.screens.web

import android.annotation.SuppressLint
import android.view.ViewGroup
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import one.srp.gensmo.ui.navigation.NavActions
import android.webkit.WebView
import android.webkit.WebSettings
import android.webkit.WebViewClient
import androidx.activity.compose.BackHandler
import androidx.core.view.ViewCompat
import android.graphics.Rect
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.size
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.platform.LocalDensity
import kotlin.math.roundToInt
import androidx.compose.foundation.layout.offset

@SuppressLint("SetJavaScriptEnabled")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductWebScreen(
    navActions: NavActions,
    url: String
) {
    Box(modifier = Modifier.fillMaxSize()) {
        val context = androidx.compose.ui.platform.LocalContext.current
        val webView = remember {
            WebView(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                settings.javaScriptEnabled = true
                settings.domStorageEnabled = true
                settings.useWideViewPort = true
                settings.loadWithOverviewMode = true
                settings.cacheMode = WebSettings.LOAD_DEFAULT
                settings.setSupportZoom(true)
                settings.builtInZoomControls = true
                settings.displayZoomControls = false
                webViewClient = object : WebViewClient() {}
                // 允许系统边缘返回手势
                ViewCompat.setSystemGestureExclusionRects(this, emptyList<Rect>())
            }
        }

        // 浮动按钮偏移（像素）
        val density = LocalDensity.current
        val initialOffsetPx = with(density) { 16.dp.toPx() }
        val offsetX = remember { androidx.compose.runtime.mutableStateOf(initialOffsetPx) }
        val offsetY = remember { androidx.compose.runtime.mutableStateOf(initialOffsetPx) }

        Column(modifier = Modifier.fillMaxSize()) {
            AndroidView(
                factory = { webView },
                modifier = Modifier
                    .fillMaxSize()
                    .windowInsetsPadding(WindowInsets.statusBars),
                update = {
                    val finalUrl = try {
                        val uri = android.net.Uri.parse(url)
                        if (uri.scheme == "http") uri.buildUpon().scheme("https").build().toString() else url
                    } catch (e: Exception) { url }
                    webView.loadUrl(finalUrl)
                    // 再次确保未排除系统边缘手势
                    ViewCompat.setSystemGestureExclusionRects(webView, emptyList<Rect>())
                }
            )
        }

        // 可拖拽浮动返回按钮（覆盖在内容之上）
        Surface(
            modifier = Modifier
                .size(48.dp)
                .offset { IntOffset(offsetX.value.roundToInt(), offsetY.value.roundToInt()) }
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        change.consume()
                        offsetX.value += dragAmount.x
                        offsetY.value += dragAmount.y
                    }
                },
            color = Color.Black.copy(alpha = 0.5f),
            shape = CircleShape
        ) {
            IconButton(onClick = { navActions.back() }) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        }

        BackHandler {
            if (webView.canGoBack()) {
                webView.goBack()
            } else {
                navActions.back()
            }
        }
    }
} 