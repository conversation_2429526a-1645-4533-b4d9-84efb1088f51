package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.ProductItem
import one.srp.core.network.model.SavedItem
import one.srp.core.network.model.TryOnParams
import one.srp.gensmo.data.remote.ProductService
import one.srp.gensmo.data.remote.RemixService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.product.panel.info.ProductInfoDrawer
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import timber.log.Timber

@Composable
fun ProfileAssets(
    modifier: Modifier = Modifier,
    navActions: NavActions = NavActions(),
    onClearFeed: () -> Unit = {},
    onClearTryOn: () -> Unit = {},
    openTryOn: (TryOnParams) -> Unit = {},
) {
    // 初始化埋点 helper，页面级 refer 为 Profile
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Profile)
    val tabs = listOf("Posts", "Saved")
    var selectedTab by remember { mutableIntStateOf(0) }
    fun selectTab(index: Int) {
        selectedTab = index
    }

    Column {
        TabRow(
            selectedTabIndex = selectedTab,
            modifier = Modifier.fillMaxWidth(),
            containerColor = Color.Transparent,
            divider = {},
            indicator = { tabPositions ->
                if (selectedTab < tabPositions.size) {
                    BoxWithConstraints(contentAlignment = Alignment.BottomCenter) {
                        val paddingH = (this.maxWidth - 110.dp) / tabPositions.size / 2
                        TabRowDefaults.SecondaryIndicator(
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTab])
                                .padding(horizontal = paddingH),
                        )
                    }
                }
            },
        ) {
            tabs.forEachIndexed { index, tabLabel ->
                Tab(selected = selectedTab == index, onClick = {
                    selectTab(index)
                    // 埋点：切换收藏和帖子Tab
                    if (index == 1) {
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.GalleryTabCollects,
                                method = EventMethod.SwitchSection,
                                actionType = EventActionType.EnterProfileCollects
                            )
                        )
                    } else {
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.GalleryTabPosts,
                                method = EventMethod.SwitchSection,
                                actionType = EventActionType.EnterProfilePosts
                            )
                        )
                    }
                }, text = {
                    if (selectedTab == index) {
                        Text(
                            text = tabLabel, style = AppThemeTextStyle.Body16H
                        )
                    } else {
                        Text(
                            text = tabLabel,
                            style = AppThemeTextStyle.Body16LightH.copy(AppThemeColors.Gray700)
                        )
                    }
                })
            }
        }

        when (selectedTab) {
            1 -> Box {
                val coroutineScope = rememberCoroutineScope()
                var selectedSaved by remember { mutableStateOf<SavedItem?>(null) }
                var selectedProduct by remember { mutableStateOf<ProductItem?>(null) }
                suspend fun openProductPanel(item: SavedItem) {
                    selectedSaved = item
                    item.collectionId?.let { collectionId ->
                        try {
                            val res =
                                ProductService.api.getProduct(collectionId)
                            if (res.isSuccessful) {
                                res.body()?.let { product ->
                                    selectedProduct = product
                                }
                            }
                        } catch (e: Exception) {
                            Timber.e(e)
                        }
                    }
                }

                fun closeProductPanel() {
                    selectedSaved = null
                    selectedProduct = null
                }

                val reportRemix = { id: String ->
                    coroutineScope.launch {
                        try {
                            withContext(Dispatchers.IO) {
                                RemixService.api.reportFeedRemix(id)
                            }
                        } catch (e: Exception) {
                            // 如果发生401错误或其他异常，静默处理
                            // 因为这是一个统计接口，失败不影响主要功能
                            Timber.e(e)
                        }
                    }
                }

                val tryOnProduct = { product: ProductItem ->
                    selectedSaved?.let {
                        coroutineScope.launch {
                            val model = UserDataStoreManager.getModelInfo()
                            val params = if (!model.second.isNullOrEmpty()) {
                                TryOnParams(
                                    modelId = model.second,
                                    products = listOf(product),
                                    moodboardId = it.moodboardId,
                                    taskId = it.tryOnTaskId,
                                    internalImageList = product.mainImage?.link?.let { listOf(it) },
                                )
                            } else {
                                TryOnParams(
                                    products = listOf(product),
                                    moodboardId = it.moodboardId,
                                    taskId = it.tryOnTaskId,
                                    internalImageList = product.mainImage?.link?.let { listOf(it) },
                                )
                            }
                            openTryOn(params)
                        }
                    }
                }

                SavedView(modifier = modifier, onItemClick = {
                    when (it.collectionType) {
                        CollectionType.Collage.value -> {
                            // 埋点：收藏列表项点击进入collage详情页
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.GalleryTabCollectsListItem,
                                    method = EventMethod.Click,
                                    actionType = EventActionType.EnterCollageDetail,
                                    items = listOf(
                                        EventItem(
                                            itemCategory = EventItemCategory.GeneralCollage,
                                            itemId = it.moodboardId,
                                            itemName = it.detailTitle ?: it.reasoning
                                        )
                                    )
                                )
                            )
                            it.moodboardId?.let { id ->
                                onClearFeed()
                                navActions.navigateToFeedDetail(id)
                            }
                        }

                        CollectionType.CollageFromSearch.value -> {
                            // 埋点：收藏列表项点击进入collage详情页 (from search)
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.GalleryTabCollectsListItem,
                                    method = EventMethod.Click,
                                    actionType = EventActionType.EnterCollageDetail,
                                    items = listOf(
                                        EventItem(
                                            itemCategory = EventItemCategory.GeneralCollage,
                                            itemId = it.moodboardId,
                                            itemName = it.detailTitle ?: it.reasoning
                                        )
                                    )
                                )
                            )
                            it.moodboardId?.let { id ->
                                onClearFeed()
                                navActions.navigateToFeedDetail(id)
                            }
                        }

                        CollectionType.TryOn.value -> {
                            // 埋点：收藏列表项点击进入try-on详情页
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.GalleryTabCollectsListItem,
                                    method = EventMethod.Click,
                                    actionType = EventActionType.EnterTryOnDetail,
                                    items = listOf(
                                        EventItem(
                                            itemCategory = EventItemCategory.TryOnTask,
                                            itemId = it.collectionId,
                                            itemName = it.reasoning
                                        )
                                    )
                                )
                            )
                            it.collectionId?.let { id ->
                                if (it.isFeed == true) {
                                    onClearFeed()
                                    navActions.navigateToTryOnDetail(id)
                                } else {
                                    onClearTryOn()
                                    navActions.navigateToTryOnTask(id)
                                }
                            }
                        }

                        CollectionType.Product.value -> {
                            // 埋点：收藏列表项点击进入商品详情页
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.GalleryTabCollectsListItem,
                                    method = EventMethod.Click,
                                    actionType = EventActionType.EnterProductDetail,
                                    items = listOf(
                                        EventItem(
                                            itemCategory = EventItemCategory.Product,
                                            itemId = it.collectionId,
                                            itemName = it.detailTitle ?: it.reasoning
                                        )
                                    )
                                )
                            )
                            coroutineScope.launch {
                                openProductPanel(it)
                            }
                        }

                        else -> {}
                    }
                })

                ProductInfoDrawer(
                    open = selectedProduct != null,
                    onClose = { closeProductPanel() },
                    item = selectedProduct,
                    onRemix = {
                        selectedProduct?.let { product ->
                            selectedSaved?.let {
                                reportRemix(it.moodboardId ?: "")
                                navActions.navigateToCollageSearch(
                                    query = "Style with this",
                                    imageUrl = product.mainImage?.link ?: ""
                                )
                            }
                            selectedProduct = null
                        }
                    },
                    onTryOn = {
                        selectedProduct?.let { product ->
                            tryOnProduct(product)
                            selectedProduct = null
                        }
                    },
                    tryOnEnabled = selectedProduct?.tags?.cateTag in listOf(
                        "coat",
                        "top",
                        "bottom",
                        "full-body",
                        "ootd"
                    ),
                    onOpenProductWeb = { url -> navActions.navigateToProductWeb(url) }
                )
            }

            0 -> PostsView(
                modifier = modifier,
                targetUserId = runBlocking { UserDataStoreManager.getUserId() },
                onItemClick = { item ->
                    onClearFeed()

                    item.tryOnTaskId?.let { id ->
                        navActions.navigateToTryOnDetail(id, "feed")
                    } ?: run {
                        if (item.moodboardId.isNotBlank()) {
                            navActions.navigateToFeedDetail(item.moodboardId, "feed")
                        }
                    }
                },
                onPostClick = {
                    navActions.navigateToPostEditor(type = "collage")
                }
            )

            else -> {}
        }
    }
}

