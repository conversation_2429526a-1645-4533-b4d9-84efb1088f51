package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.HomePageInfo
import one.srp.gensmo.data.repository.utils.BaseState
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun SearchExtension(
    navActions: NavActions,
    homeInfo: BaseState<HomePageInfo>? = null,
    onClickItem: () -> Unit = {},
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Home)
    val scrollState = rememberScrollState()

    when (homeInfo) {
        null -> {}
        is BaseState.Loading -> {}
        is BaseState.Error -> {}
        is BaseState.Success -> {
            Row(
                modifier = Modifier
                    .horizontalScroll(scrollState)
                    .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                homeInfo.data.topFeature
                    ?.filter { it.fixedTitle != "Get a look" }
                    ?.map {
                        SearchExtensionCard(
                            text = it.fixedTitle ?: it.title ?: "",
                            icon = it.fixedIcon ?: it.icon,
                            onClick = {
                                onClickItem()

                                when (it.router) {
                                    "gensmo://camera?mode=styling" -> navActions.navigateToCamera("STYLE THIS PIECE")
                                    "gensmo://camera?mode=similar" -> navActions.navigateToCamera("FIND SIMILAR")
                                    "gensmo://camera?mode=decoration" -> navActions.navigateToCamera(
                                        "DECOR"
                                    )
                                }
                            },
                        )
                    }
            }
        }
    }
}

@Composable
fun SearchExtensionCard(text: String, icon: String? = null, onClick: () -> Unit = {}) {
    Card(colors = CardDefaults.cardColors(Color.White, Color.Gray), onClick = { onClick() }) {
        Row(
            modifier = Modifier
                .shadow(
                    elevation = 50.dp,
                    spotColor = Color(0x14000000),
                    ambientColor = Color(0x14000000)
                )
                .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 4.dp))
                .padding(start = 16.dp, top = 8.dp, end = 16.dp, bottom = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.Start)
        ) {
            AsyncImage(model = icon, contentDescription = null, modifier = Modifier.size(18.dp))

            Text(
                text = text,
                overflow = TextOverflow.Companion.Ellipsis,
                maxLines = 2,
                style = AppThemeTextStyle.Body11H
            )
        }
    }
}
