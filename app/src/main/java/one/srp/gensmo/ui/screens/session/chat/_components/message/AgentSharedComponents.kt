package one.srp.gensmo.ui.screens.session.chat._components.message

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import kotlinx.serialization.json.Json
import one.srp.core.network.model.AgentProductItem
import one.srp.core.network.model.MoodboardContent
import one.srp.core.network.model.MoodboardTryOnItems
import one.srp.core.network.model.ProductContent
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.ui.components.collage.MoodboardRenderer
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio

// 简单的 Markdown 解析函数
fun parseMarkdown(text: String): String {
    return text
        .replace("**", "") // 移除粗体标记
        .replace("*", "")  // 移除斜体标记
        .replace("`", "")  // 移除代码标记
        .replace("#", "")  // 移除标题标记
        .replace("##", "") // 移除二级标题标记
        .replace("###", "") // 移除三级标题标记
        .replace("####", "") // 移除四级标题标记
        .replace("#####", "") // 移除五级标题标记
        .replace("######", "") // 移除六级标题标记
        .replace("~~", "")  // 移除删除线标记
        .replace("> ", "")  // 移除引用标记
        .replace("- ", "")  // 移除列表标记
        .replace("1. ", "") // 移除有序列表标记
        .trim()
}

// Moodboard 内容数据结构 - 匹配实际的 JSON 格式
@OptIn(kotlinx.serialization.ExperimentalSerializationApi::class)
@kotlinx.serialization.Serializable
@kotlinx.serialization.json.JsonIgnoreUnknownKeys
data class MoodboardContentWrapper(
    @kotlinx.serialization.SerialName("user_query")
    val userQuery: String? = null,
    @kotlinx.serialization.SerialName("strategy_count")
    val strategyCount: Int? = null,
    @kotlinx.serialization.SerialName("variations_per_strategy")
    val variationsPerStrategy: Int? = null,
    @kotlinx.serialization.SerialName("total_variants")
    val totalVariants: Int? = null,
    val status: String? = null,
    @kotlinx.serialization.SerialName("task_id")
    val taskId: String? = null,
    val moodboards: List<AgentMoodboardEntity>? = null
)

// Agent 专用的 Moodboard 实体，严格匹配后端返回的原始数据结构
@OptIn(kotlinx.serialization.ExperimentalSerializationApi::class)
@kotlinx.serialization.Serializable
@kotlinx.serialization.json.JsonIgnoreUnknownKeys
data class AgentMoodboardEntity(
    val svg: String? = null,
    val label: String? = null,
    @kotlinx.serialization.SerialName("variation_summary")
    val variationSummary: String? = null,
    @kotlinx.serialization.SerialName("styling_rationale")
    val stylingRationale: String? = null,
    val background: String? = null,
    val id: String,
    val content: String,
    @kotlinx.serialization.SerialName("variant_label")
    val variantLabel: String? = null,
    @kotlinx.serialization.SerialName("products_used")
    val productsUsed: Int? = null,
    @kotlinx.serialization.SerialName("task_id")
    val taskId: String? = null,
    @kotlinx.serialization.SerialName("strategy_name")
    val strategyName: String? = null,
    val products: List<ProductItem>? = null,
    @kotlinx.serialization.SerialName("all_try_on_items")
    val allTryOnItems: MoodboardTryOnItems? = null,
    var isFavorited: Boolean? = null,
    var parsedContent: MoodboardContent? = null
)

@Composable
fun MoodboardListComponent(
    moodboards: List<AgentMoodboardEntity>,
    modifier: Modifier = Modifier
) {
    
    val moodboardList = remember(moodboards) {
        moodboards.mapNotNull { moodboard ->
            
            val parsedContent = moodboard.parsedContent
                ?: try {
                    Json.decodeFromString<MoodboardContent>(moodboard.content)
                } catch (e: Exception) {
                    null
                }
            
            if (parsedContent != null) {
                moodboard.copy(parsedContent = parsedContent)
            } else {
                null
            }
        }
    }
    
    if (moodboardList.isNotEmpty()) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(moodboardList) { moodboard ->
                moodboard.parsedContent?.let { content ->
                    MoodboardCard(content = content)
                }
            }
        }
    } else {
        Text(
            text = "暂无 Moodboard",
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )
    }
}

@Composable
fun MoodboardCard(
    content: MoodboardContent,
    modifier: Modifier = Modifier
) {
    
    val ratio = content.width / (content.height ?: (content.width * 1.5f))

    Card(
        modifier = modifier
            .width(200.dp)
            .aspectRatio(ratio),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(ratio)
        ) {
            MoodboardRenderer(
                modifier = Modifier.fillMaxSize(),
                item = content,
                offscreen = true
            )
        }
    }
}

@Composable
fun ProductListComponent(
    productContent: ProductContent,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        if (productContent.success) {
            if (productContent.products.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                ) {
                    items(productContent.products) { product ->
                        ProductCard(product = product)
                    }
                }
            } else {
                Text(
                    text = "暂无产品",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
        } else {
            Text(
                text = productContent.message ?: "产品加载失败",
                fontSize = 14.sp,
                color = Color(0xFF666666)
            )
        }
    }
}

@Composable
fun ProductCard(
    product: AgentProductItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .width(64.dp)
            .height(96.dp)
    ) {
        // 产品图片 - 使用指定的样式
        var isLoading by remember { mutableStateOf(true) }
        
        Box(
            modifier = Modifier
                .shadow(
                    elevation = 4.dp, 
                    spotColor = Color(0x1A2F536D), 
                    ambientColor = Color(0x1A2F536D)
                )
                .border(
                    width = 0.35422.dp, 
                    color = Color(0xFFF0F0F0), 
                    shape = RoundedCornerShape(size = 4.dp)
                )
                .padding(0.35422.dp)
                .width(64.dp)
                .height(64.dp)
                .background(
                    color = Color(0xFFFFFFFF), 
                    shape = RoundedCornerShape(size = 4.dp)
                )
                .clip(RoundedCornerShape(4.dp))
        ) {
            AsyncImage(
                model = product.imageUrl,
                contentDescription = product.title,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                onState = { state ->
                    isLoading = state is coil3.compose.AsyncImagePainter.State.Loading
                }
            )
            
            // 自定义占位符 - 在加载时显示
            if (isLoading) {
                androidx.compose.foundation.Image(
                    painter = painterResource(id = R.drawable.icon_clothes_default),
                    contentDescription = null,
                    modifier = Modifier
                        .size(32.dp)
                        .align(androidx.compose.ui.Alignment.Center),
                    contentScale = ContentScale.Fit
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 产品信息区域 - 64x28（上方间隔 4dp），浅灰色背景，包含品牌图标和价格
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(28.dp)
                .background(
                    color = Color(0xFFF5F5F5),
                    shape = RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp)
                )
                .padding(horizontal = 4.dp),
            verticalAlignment = androidx.compose.ui.Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧：品牌图标
            if (!product.brandIcon.isNullOrEmpty()) {
                AsyncImage(
                    model = product.brandIcon,
                    placeholder = painterResource(id = R.drawable.icon_bag),
                    contentDescription = product.brand,
                    modifier = Modifier
                        .size(16.dp)
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(2.dp)
                        )
                        .padding(1.dp),
                    contentScale = ContentScale.Fit
                )
            } else {
                // 如果没有品牌图标，显示品牌名称
                Text(
                    text = product.brand,
                    fontSize = 8.sp,
                    color = Color(0xFF666666),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(2.dp)
                        )
                        .padding(horizontal = 3.dp, vertical = 1.dp)
                )
            }
            
            // 右侧：价格
            Text(
                text = product.price,
                fontSize = 9.sp,
                color = Color(0xFF666666),
                fontWeight = FontWeight.Medium
            )
        }
    }
}

 