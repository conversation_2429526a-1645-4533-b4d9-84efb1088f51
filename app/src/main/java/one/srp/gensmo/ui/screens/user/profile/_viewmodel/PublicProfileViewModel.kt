package one.srp.gensmo.ui.screens.user.profile._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.remote.CommunityService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.action.FollowStatus
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PublicProfileViewModel @Inject constructor() : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _userName = MutableStateFlow("")
    val userName: StateFlow<String> = _userName.asStateFlow()

    private val _userAvatar = MutableStateFlow<String?>(null)
    val userAvatar: StateFlow<String?> = _userAvatar.asStateFlow()

    private val _isFollowing = MutableStateFlow(false)
    val isFollowing: StateFlow<Boolean> = _isFollowing.asStateFlow()

    private val _followStatus = MutableStateFlow<FollowStatus?>(null)
    val followStatus: StateFlow<FollowStatus?> = _followStatus.asStateFlow()

    private val _currentUserId = MutableStateFlow<String?>(null)
    val currentUserId: StateFlow<String?> = _currentUserId.asStateFlow()

    // 添加当前登录用户ID的状态
    private val _loggedInUserId = MutableStateFlow<String?>(null)
    val loggedInUserId: StateFlow<String?> = _loggedInUserId.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // 用户统计数据
    private val _followingCount = MutableStateFlow(0)
    val followingCount: StateFlow<Int> = _followingCount.asStateFlow()

    private val _followerCount = MutableStateFlow(0)
    val followerCount: StateFlow<Int> = _followerCount.asStateFlow()

    private val _totalLikes = MutableStateFlow(0)
    val totalLikes: StateFlow<Int> = _totalLikes.asStateFlow()

    init {
        // 在初始化时获取当前登录用户ID
        loadLoggedInUserId()
    }

    private fun loadLoggedInUserId() {
        viewModelScope.launch {
            try {
                val userId = UserDataStoreManager.getUserId()
                _loggedInUserId.value = userId
                Timber.d("当前登录用户ID: $userId")
            } catch (e: Exception) {
                Timber.e(e, "获取当前登录用户ID失败")
            }
        }
    }

    fun loadUserProfile(userId: String) {
        _currentUserId.value = userId
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                // Load user profile
                val profileResponse = UserService.api.getUserProfile(userId)
                if (profileResponse.isSuccessful) {
                    val profile = profileResponse.body()

                    // Extract user info from profile
                    profile?.let {
                        _userAvatar.value = it.profilePicture
                        // Note: The API doesn't seem to return username, 
                        // we might need to get it from another source or use userId
                        _userName.value = it.userName ?: "User" // Placeholder
                        
                        // 设置用户统计数据
                        _followingCount.value = it.followingCount ?: 0
                        _followerCount.value = it.followerCount ?: 0
                        _totalLikes.value = it.totalLikes ?: 0
                        
                        // 从API响应中直接获取关注状态
                        if (_loggedInUserId.value != userId) {
                            val status = it.followStatus
                            _followStatus.value = when (status) {
                                FollowStatus.Followed.value -> FollowStatus.Followed
                                FollowStatus.Mutual.value -> FollowStatus.Mutual
                                FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                                FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                                else -> null
                            }
                            
                            // 设置关注状态
                            _isFollowing.value = status == FollowStatus.Followed.value || status == FollowStatus.Mutual.value
                        }
                    }
                } else {
                    _error.value = "Failed to load user profile"
                    Timber.e("Failed to load user profile: ${profileResponse.code()}")
                }

            } catch (e: Exception) {
                _error.value = "Error loading profile: ${e.message}"
                Timber.e(e, "Error loading user profile")
            } finally {
                _isLoading.value = false
            }
        }
    }



    fun toggleFollow() {
        val userId = _currentUserId.value ?: return

        // 立即更新UI状态，提供即时反馈
        val previousIsFollowing = _isFollowing.value
        val previousFollowStatus = _followStatus.value
        val newIsFollowing = !previousIsFollowing

        // 立即更新状态
        _isFollowing.value = newIsFollowing

        viewModelScope.launch {
            try {
                if (newIsFollowing) {
                    // Follow
                    val response = CommunityService.api.followUser(userId)
                    if (response.isSuccessful) {
                        // 直接从响应中获取关注状态
                        val status = response.body()?.status
                        _followStatus.value = when (status) {
                            FollowStatus.Followed.value -> FollowStatus.Followed
                            FollowStatus.Mutual.value -> FollowStatus.Mutual
                            FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                            else -> FollowStatus.Followed // 默认值
                        }
                    } else {
                        // API调用失败，回滚状态
                        _isFollowing.value = previousIsFollowing
                        _followStatus.value = previousFollowStatus
                        Timber.e("Failed to follow user: ${response.code()}")
                    }
                } else {
                    // Unfollow
                    val response = CommunityService.api.unfollowUser(userId)
                    if (response.isSuccessful) {
                        // 直接从响应中获取关注状态
                        val status = response.body()?.status
                        _followStatus.value = when (status) {
                            FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                            FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                            else -> FollowStatus.NotFollowed // 默认值
                        }
                    } else {
                        // API调用失败，回滚状态
                        _isFollowing.value = previousIsFollowing
                        _followStatus.value = previousFollowStatus
                        Timber.e("Failed to unfollow user: ${response.code()}")
                    }
                }
            } catch (e: Exception) {
                // 网络错误，回滚状态
                _isFollowing.value = previousIsFollowing
                _followStatus.value = previousFollowStatus
                Timber.e(e, "Error toggling follow status")
            }
        }
    }
}
