package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import one.srp.core.network.model.CommentResponse
import one.srp.core.network.model.FeedItem
import one.srp.gensmo.R
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.utils.datetime.formatTimestampAgo
import one.srp.utils.datetime.parseIsoToMillis

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommentPanelDrawer(
    open: Boolean = false,
    onClose: () -> Unit,
    content: @Composable () -> Unit,
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    if (open) {
        ModalBottomSheet(
            onDismissRequest = { onClose() },
            sheetState = sheetState,
            containerColor = Color.White,
        ) {
            content()
        }
    }
}

@Composable
fun CommentContainer(
    feedItem: FeedItem,
    onUserClick: (String) -> Unit = {},
    viewModel: DetailCommentViewModel = hiltViewModel(),
) {
    val scrollState = rememberScrollState()

    val commentList = viewModel.commentList.collectAsState().value

    LaunchedEffect(feedItem) {
        viewModel.getCommentList(feedItem.tryOnTaskId ?: feedItem.moodboardId)
    }

    var targetItem by remember { mutableStateOf<CommentResponse?>(null) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

    fun selectTarget(item: CommentResponse) {
        targetItem = item
        focusRequester.requestFocus()
    }

    val coroutineScope = rememberCoroutineScope()
    var replyLoading by remember { mutableStateOf(false) }
    fun reply(content: String, item: CommentResponse? = null) {
        replyLoading = true
        coroutineScope.launch {
            viewModel.postComment(
                content = content,
                postId = feedItem.tryOnTaskId ?: feedItem.moodboardId,
                userId = runBlocking { UserDataStoreManager.getUserId() ?: "" },
                parentCommentId = item?.id,
            )
            feedItem.commentCount = (feedItem.commentCount ?: 0) + 1
            targetItem = null
            replyLoading = false
            focusManager.clearFocus()
        }
    }

    fun clickUser(item: CommentResponse) {
        item.userProfile?.uid?.let { uid ->
            onUserClick(uid)
        }
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.8f)
            .padding(horizontal = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Text(
                if (commentList.isEmpty()) "Comment" else "${commentList.flatMap { listOf(it) + it.replies }.size} comments",
                style = AppThemeTextStyle.Body14H.copy(AppThemeColors.Gray500)
            )
        }

        if (commentList.isEmpty()) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
            ) {
                Image(painterResource(R.drawable.image_empty_comment_list), null)
                Text(
                    "Be the first to comment!", style = AppThemeTextStyle.Body16H.copy(
                        AppThemeColors.Gray600
                    )
                )
            }
        } else {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                commentList.map { comment ->
                    CommentGroup(
                        comment,
                        onReply = { selectTarget(it) },
                        onUserClick = { clickUser(it) })
                }
            }
        }

        CommentInput(
            targetUser = targetItem?.userProfile?.name,
            loading = replyLoading,
            onConfirm = { content -> reply(content, targetItem) },
            focusRequester = focusRequester,
        )
    }

}

@Composable
private fun CommentGroup(
    item: CommentResponse,
    onReply: (CommentResponse) -> Unit = {},
    onUserClick: (CommentResponse) -> Unit = {},
) {
    Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
        CommentItem(item, onReply = { onReply(item) }, onUserClick = { onUserClick(item) })

        Column(
            modifier = Modifier.padding(start = 36.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            item.replies.map {
                CommentItem(it, onReply = { onReply(it) }, onUserClick = { onUserClick(it) })
            }
        }
    }
}

@Composable
private fun CommentItem(
    item: CommentResponse,
    onReply: () -> Unit = {},
    onUserClick: () -> Unit = {},
) {
    val timeStr = runCatching { formatTimestampAgo(parseIsoToMillis(item.createdAt)) }.getOrNull()

    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
        Box(
            modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.surfaceVariant)
                .clickable { onUserClick() }) {
            AsyncImage(
                model = item.userProfile?.avatar,
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                error = painterResource(id = R.drawable.icon_unregister)
            )
        }

        Column(verticalArrangement = Arrangement.spacedBy(8.dp), modifier = Modifier.weight(1f)) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                modifier = Modifier.fillMaxWidth(),
            ) {
                Text(
                    item.userProfile?.name ?: "",
                    style = AppThemeTextStyle.Body12H.copy(
                        AppThemeColors.Gray600,
                    ),
                    modifier = Modifier.clickable { onUserClick() },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                timeStr?.let {
                    Text(
                        timeStr, style = AppThemeTextStyle.Body11H.copy(AppThemeColors.Gray500)
                    )
                }
            }

            Column {
                item.parentUserProfile?.name?.let { pn ->
                    val anno = buildAnnotatedString {
                        withStyle(SpanStyle(color = Color.Blue)) {
                            append("@$pn")
                        }

                        append(" ")

                        append(item.content)
                    }

                    Text(
                        text = anno,
                        style = AppThemeTextStyle.Body13H.copy(AppThemeColors.Gray700),
                        maxLines = 4,
                        overflow = TextOverflow.Ellipsis,
                    )
                } ?: run {
                    Text(
                        text = item.content,
                        style = AppThemeTextStyle.Body13H.copy(AppThemeColors.Gray700),
                        maxLines = 4,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }

            Row {
                Text(
                    stringResource(R.string.text_reply), style = AppThemeTextStyle.Body12H.copy(
                        AppThemeColors.Gray600
                    ), modifier = Modifier.clickable { onReply() })
            }
        }
    }
}

@Composable
private fun CommentInput(
    targetUser: String? = null,
    loading: Boolean = false,
    onConfirm: suspend (String) -> Unit = {},
    focusRequester: FocusRequester = remember { FocusRequester() },
    viewModel: UserProfileViewModel = hiltViewModel(),
) {
    val userAvatar = viewModel.userAvatar.collectAsState()

    var input by remember { mutableStateOf("") }

    val coroutineScope = rememberCoroutineScope()
    fun commit() {
        coroutineScope.launch(Dispatchers.IO) {
            onConfirm(input)
            input = ""
        }
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.padding(vertical = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            AsyncImage(
                model = when (userAvatar.value) {
                    "icon_random_thumb" -> R.drawable.icon_random_thumb
                    "icon_unregister" -> R.drawable.icon_unregister
                    else -> userAvatar.value
                },
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                error = painterResource(id = R.drawable.icon_unregister)
            )
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier
                .border(1.dp, Color.LightGray, MaterialTheme.shapes.extraLarge)
                .padding(end = 8.dp)
        ) {
            TextField(
                value = input,
                onValueChange = { input = it },
                placeholder = {
                    Text(
                        targetUser?.let { "Reply to $targetUser" } ?: run { "What do you think?" },
                        style = AppThemeTextStyle.Body14H.copy(AppThemeColors.Gray500)
                    )
                },
                modifier = Modifier
                    .weight(1f)
                    .focusRequester(focusRequester),
                singleLine = true,
                textStyle = AppThemeTextStyle.Body14H,
                colors = TextFieldDefaults.colors(
                    unfocusedContainerColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                    focusedIndicatorColor = Color.Transparent
                )
            )


            if (input.isNotBlank()) {
                Button(
                    onClick = { commit() },
                    modifier = Modifier
                        .height(36.dp)
                        .width(54.dp),
                    shape = MaterialTheme.shapes.large,
                    contentPadding = PaddingValues(6.dp),
                    enabled = !loading,
                ) {
                    if (loading) {
                        BaseLoading(
                            strokeWidth = 2.dp,
                            modifier = Modifier.size(16.dp),
                            color = AppThemeColors.White
                        )
                    } else {
                        Icon(
                            painterResource(R.drawable.icon_arrow_up),
                            null,
                        )
                    }
                }
            }
        }
    }
}

