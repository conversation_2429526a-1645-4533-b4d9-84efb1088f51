package one.srp.gensmo.ui.screens.detail.collage

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColorInt
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.BrandItem
import one.srp.core.network.model.BrandProductItem
import one.srp.core.network.model.ChatMessageRole
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.FeedCoverItem
import one.srp.core.network.model.FeedCoverItemType
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.MoodboardContent
import one.srp.core.network.model.MoodboardContentBlock
import one.srp.core.network.model.MoodboardTryOnItems
import one.srp.core.network.model.ProductItem
import one.srp.core.network.model.SearchParams
import one.srp.core.network.model.SearchQueryMessage
import one.srp.core.network.model.SearchQueryMessageWrapper
import one.srp.core.network.model.TryOnParams
import one.srp.core.network.utils.JSON
import one.srp.gensmo.R
import one.srp.gensmo.data.remote.RemixService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.action.FollowAction
import one.srp.gensmo.ui.components.action.FollowStatus
import one.srp.gensmo.ui.components.button.FeedFollowButton
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.collage.CollageSharePanel
import one.srp.gensmo.ui.components.collage.MoodboardRenderer
import one.srp.gensmo.ui.components.collage.MoodboardTryOnPanel
import one.srp.gensmo.ui.components.collage.SharePanelDrawer
import one.srp.gensmo.ui.components.collage.TryOnPanelDrawer
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.detail._components.CommentContainer
import one.srp.gensmo.ui.screens.detail._components.CommentPanelDrawer
import one.srp.gensmo.ui.screens.detail._components.DetailDesc
import one.srp.gensmo.ui.screens.detail._components.DetailSimilar
import one.srp.gensmo.ui.screens.detail._components.EventBanner
import one.srp.gensmo.ui.screens.detail._components.ProductAction
import one.srp.gensmo.ui.screens.detail._components.ProductView
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.screens.session.view._components.ActionDropdownMenu
import one.srp.gensmo.ui.screens.session.view._components.CarouselContainer
import one.srp.gensmo.ui.screens.session.view._components.DeletePostButton
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel
import one.srp.gensmo.viewmodel.tryon.CreateViewModel
import timber.log.Timber
import java.util.UUID
import kotlin.math.max

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedDetailScreen(
    navActions: NavActions = NavActions(),
    feedItem: FeedItem? = null,
    moodboardId: String? = null,
    feedType: String? = null,
    openTryOn: (TryOnParams) -> Unit = {},
    onClear: () -> Unit = {},
    viewModel: FeedDetailViewModel = hiltViewModel(),
    onUpdateRemixProduct: (ProductItem?) -> Unit = {},
    createSession: (SearchQueryMessage) -> Unit = {},
    refer: EventRefer = EventRefer.FeedDetail,
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)
    LaunchedEffect(Unit) {
        MetricData.logEventAF("af_feed_detail_view")
        metric(SelectItem(EventItemListName.Screen, method = EventMethod.PageView))
    }

    DisposableEffect(Unit) {
        onDispose {
            onClear()
        }
    }

    // 拦截系统返回按钮，确保使用我们的安全返回逻辑
    BackHandler {
        navActions.back()
    }

    val coroutineScope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    val density = LocalDensity.current

    // 删除确认状态
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    var deleteConfirmationLoading by remember { mutableStateOf(false) }
    val sheetState = rememberModalBottomSheetState()

    val result by viewModel.searchResult.collectAsState()
    // 如果 refer = "similar"，只使用 API 结果；否则先使用 feedItem，当 getCollageDetail 执行完毕后，result 会覆盖 feedItem
    val item = if (feedType == "similar") {
        result // 只使用 API 结果，不使用 feedItem
    } else {
        result ?: feedItem
    }

    LaunchedEffect(moodboardId) {
        moodboardId?.let {
            viewModel.getCollageDetail(it)
        }
    }

    // 删除帖子函数
    fun deletePost() {
        item?.let { feedItem ->
            deleteConfirmationLoading = true

            val documentId = feedItem.moodboards?.id ?: return
            val title = ""
            val description = ""
            val type = "collage"

            metric(
                SelectItem(
                    itemListName = EventItemListName.DeleteConfirmBtn,
                    method = EventMethod.Click,
                    actionType = EventActionType.Delete,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.GeneralCollage,
                            itemId = documentId,
                            itemName = title
                        )
                    )
                )
            )

            viewModel.deletePost(
                feedType = type,
                documentId = documentId,
                title = title,
                description = description,
                onSuccess = {
                    coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                        if (!sheetState.isVisible) {
                            showDeleteConfirmation = false
                            deleteConfirmationLoading = false
                            navActions.back()
                        }
                    }
                },
                onFail = { error ->
                    // 可以在这里显示错误提示
                    coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                        if (!sheetState.isVisible) {
                            showDeleteConfirmation = false
                            deleteConfirmationLoading = false
                        }
                    }
                }
            )
        }
    }

    val jsonContent = remember(item) {
        item?.moodboards?.let {
            it.parsedContent ?: JSON.decodeFromString<MoodboardContent>(it.content)
        } ?: run { null }
    }

    var selectedBlock by remember { mutableStateOf<MoodboardContentBlock?>(null) }
    // Click handler for moodboard blocks: report metric and select block
    val onBlockClick: (MoodboardContentBlock) -> Unit = { block ->
        // 将 block 转换为对应的 ProductItem，以使用 product.globalId 作为埋点参数
        val product = item?.moodboards?.products?.firstOrNull { it.globalId == block.globalId }
        val eventItem = EventItem(
            itemCategory = EventItemCategory.Product,
            itemId = product?.globalId ?: block.id.orEmpty(),
            itemName = product?.title ?: block.content.content,
            index = -1
        )
        metric(
            SelectItem(
                itemListName = EventItemListName.CollageEntityListEntity,
                method = EventMethod.Click,
                actionType = EventActionType.Default,
                items = listOf(eventItem)
            )
        )
        selectedBlock = block
    }

    var shareOpen by remember { mutableStateOf(false) }
    var tryOnOpen by remember { mutableStateOf(false) }
    var tryOnItems by remember { mutableStateOf<MoodboardTryOnItems?>(null) }

    var searchPanelVisible by remember { mutableStateOf(false) }
    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf("") }
    var capturedBrandItem by remember { mutableStateOf<BrandItem?>(null) }
    var capturedBrandProduct by remember { mutableStateOf<BrandProductItem?>(null) }

    val reportRemix = { id: String ->
        coroutineScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    RemixService.api.reportFeedRemix(id)
                }
            } catch (e: Exception) {
                // 如果发生401错误或其他异常，静默处理
                // 因为这是一个统计接口，失败不影响主要功能
                Timber.e(e)
            }
        }
    }
    val remixFeed = {
        item?.let {
            metric(
                SelectItem(
                    itemListName = EventItemListName.RemixBottomBtn,
                    method = EventMethod.Click,
                    actionType = EventActionType.InitializeSearch
                )
            )
            reportRemix(it.moodboardId)
            capturedImageUri = it.imageUrl
            capturedSearchText = it.query ?: "Style with this"
            searchPanelVisible = true
        }
    }

    fun onSearch(
        q: String,
        img: String,
        stylesList: String?,
        budget: String?,
        brand: BrandItem? = null,
        brandProduct: BrandProductItem? = null,
        isProModeEnabled: Boolean = false,
    ) {
        if (q.trim().isNotEmpty()) {
            Timber.d("onSearch: $q, $img, $stylesList, $budget")
            createSession(
                SearchQueryMessage(
                    sessionId = "default",
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = SearchQueryMessageWrapper(
                        searchQuery = SearchParams(
                            query = q,
                            imageUrl = img,
                            debugLevel = 0,
                            budget = budget ?: "",
                            isAsync = true,
                            route = "",
                            isPresetQuery = false,
                            moodboardVersion = "v2",
                            inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                                ?.filter { it.isNotEmpty() } ?: emptyList()),
                            brand = brand?.brand,
                            specifiedProduct = brandProduct?.product,
                            isAgent = isProModeEnabled,
                        ),
                    )
                )
            )
        }
    }

    val remixProduct = { product: ProductItem ->
        item?.let {
            reportRemix(it.moodboardId)
            onUpdateRemixProduct(product)

            onSearch(
                "Complete the look",
                product.mainImage?.link ?: "",
                null,
                null,
                null,
                null,
                false
            )
        }
    }

    val openProductLink = { product: ProductItem ->
        product.link?.let { link ->
            val uri = link.toUri()
            val linkValid = uri.scheme != null && uri.host != null
            if (linkValid) {
                try {
                    metric(
                        SelectItem(
                            method = EventMethod.Click,
                            itemListName = EventItemListName.ProductListProduct,
                            actionType = EventActionType.ProductExternalJump,
                            items = listOf(
                                EventItem(
                                    itemId = product.id,
                                    itemName = product.title,
                                    itemCategory = EventItemCategory.Product,
                                ),
                                EventItem(
                                    itemCategory = EventItemCategory.GeneralCollage,
                                    itemId = item?.moodboardId.orEmpty(),
                                    itemName = item?.detailTitle.orEmpty()
                                )
                            )
                        )
                    )
                    MetricData.logEventAF("af_product_external_jump")
                    navActions.navigateToProductWeb(link)
                } catch (err: Exception) {
                    Timber.e(err)
                }
            }
        }
    }

    val tryOnProduct = { product: ProductItem, shouldShowTryOnPanel: Boolean ->
        item?.let {
            coroutineScope.launch {
                val model = UserDataStoreManager.getModelInfo()
                // 检查是否有可用的试穿选项
                tryOnItems = item.moodboards?.allTryOnItems

                if (shouldShowTryOnPanel && (tryOnItems?.box1 != null || tryOnItems?.box2 != null)) {
                    tryOnOpen = true

                    metric(
                        SelectItem(
                            EventItemListName.TryOnBtn,
                            method = EventMethod.Click,
                            actionType = EventActionType.TryOnSelect,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.GeneralCollage,
                                    itemId = item.moodboardId,
                                    itemName = item.detailTitle
                                )
                            )
                        )
                    )
                } else {
                    val isFeedValue = feedType == "feed"
                    val params = TryOnParams(
                        modelId = (model.second ?: ""),
                        products = listOf(product),
                        moodboardId = item.moodboardId,
                        taskId = item.taskId,
                        internalImageList = product.mainImage?.link?.let { listOf(it) },
                        isFeed = isFeedValue,
                    )

                    metric(
                        SelectItem(
                            EventItemListName.TryOnBtn,
                            method = EventMethod.Click,
                            actionType = if (model.second.isNullOrBlank()) EventActionType.TryOnNoAvatar else EventActionType.TryOn,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.GeneralCollage,
                                    itemId = item.moodboardId,
                                    itemName = item.detailTitle
                                )
                            )
                        )
                    )

                    openTryOn(params)
                }
            }
        }
    }

    var liked by remember(item) { mutableStateOf(item?.isLiked == true) }
    fun clickLike() {
        coroutineScope.launch {
            item?.moodboardId?.let { moodboardId ->
                val newLike = !liked
                liked = newLike
                item.isLiked = newLike
                item.likedCount = (item.likedCount ?: 0).let { count ->
                    if (newLike) count + 1 else max(count - 1, 0)
                }
                // 埋点：帖子详情页点赞按键点击
                metric(
                    SelectItem(
                        itemListName = EventItemListName.LikeBtn,
                        method = EventMethod.Click,
                        actionType = if (newLike) EventActionType.Like else EventActionType.CancelLike,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.GeneralCollage,
                                itemId = moodboardId,
                                itemName = item.detailTitle
                            )
                        )
                    )
                )
                try {
                    if (newLike) {
                        UserService.api.likePost(moodboardId, CollectionType.Collage)
                    } else {
                        UserService.api.unlikePost(moodboardId, CollectionType.Collage)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    liked = !newLike
                    item.isLiked = !newLike
                    item.likedCount = (item.likedCount ?: 0).let { count ->
                        if (newLike) max(count - 1, 0) else count + 1
                    }
                }
            }
        }
    }

    var sharedCount by remember(item) { mutableIntStateOf(item?.sharedCount ?: 0) }
    fun clickShare() {
        // 埋点：帖子详情页分享按键点击，带上帖子的 ID
        metric(
            SelectItem(
                itemListName = EventItemListName.ShareBtn,
                method = EventMethod.Click,
                actionType = EventActionType.Default,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.GeneralCollage,
                        itemId = item?.moodboardId.orEmpty(),
                        itemName = item?.detailTitle.orEmpty()
                    )
                )
            )
        )
        shareOpen = true

        coroutineScope.launch {
            item?.let { item ->
                item.moodboardId.let {
                    try {
                        UserService.api.sharePost(it, CollectionType.Collage)
                    } catch (e: Exception) {
                        Timber.e(e)
                    }
                }

                item.sharedCount = (item.sharedCount ?: 0) + 1
                sharedCount = (item.sharedCount ?: 0) + 1
            }
        }
    }

    var saved by remember(item) { mutableStateOf(item?.isFavorited == true) }
    fun clickSave(newSaved: Boolean) {
        item?.moodboardId?.let { moodboardId ->
            // 埋点：帖子详情页保存按键点击，带上帖子的 ID
            metric(
                SelectItem(
                    itemListName = EventItemListName.SaveBtn,
                    method = EventMethod.Click,
                    actionType = if (newSaved) EventActionType.Save else EventActionType.Unsave,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.GeneralCollage,
                            itemId = moodboardId,
                            itemName = item.detailTitle.orEmpty()
                        )
                    )
                )
            )
        }
    }

    // 相似帖子的事件处理函数
    fun genEventItemByFeedType(item: FeedItem): List<EventItem> {
        val baseItem = when {
            item.tryOnTaskId?.isNotBlank() == true -> EventItem(
                itemCategory = EventItemCategory.TryOnCollage,
                itemId = item.tryOnTaskId,
                itemName = item.reasoning
            )

            else -> EventItem(
                itemCategory = EventItemCategory.GeneralCollage,
                itemId = item.moodboardId,
                itemName = item.reasoning
            )
        }

        val resultList = mutableListOf(baseItem)

        // 添加推荐项目
        if (item.recommendId?.isNotEmpty() == true) {
            resultList.add(
                EventItem(
                    itemCategory = EventItemCategory.Reco,
                    itemId = item.recommendId,
                    itemName = item.reasoning
                )
            )
        }

        return resultList
    }

    fun clickSimilarItem(similarItem: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApFeedListItem,
                method = EventMethod.Click,
                actionType = EventActionType.EnterFeedDetail,
                items = genEventItemByFeedType(similarItem)
            )
        )
        // 导航到相似内容的详情页
        if (similarItem.tryOnTaskId?.isNotBlank() == true) {
            navActions.navigateToTryOnDetail(similarItem.tryOnTaskId ?: "", "similar")
        } else {
            navActions.navigateToFeedDetail(similarItem.moodboardId, "similar")
        }
    }

    fun onSimilarItemView(similarItem: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApFeedList,
                method = EventMethod.TrueViewTrigger,
                items = genEventItemByFeedType(similarItem)
            )
        )
    }

    fun clickSimilarLike(similarItem: FeedItem, newLike: Boolean) {
        coroutineScope.launch {
            similarItem.moodboardId.let { moodboardId ->
                val eventItems = genEventItemByFeedType(similarItem)
                metric(
                    SelectItem(
                        itemListName = EventItemListName.ApOuterLikeBtn,
                        method = EventMethod.Click,
                        actionType = if (newLike) EventActionType.Like else EventActionType.CancelLike,
                        items = eventItems
                    )
                )

                try {
                    val type =
                        if (similarItem.tryOnTaskId?.isNotBlank() == true) CollectionType.TryOn else CollectionType.Collage

                    if (newLike) {
                        UserService.api.likePost(moodboardId, type)
                    } else {
                        UserService.api.unlikePost(moodboardId, type)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 回滚状态
                    similarItem.isLiked = !newLike
                    similarItem.likedCount = (similarItem.likedCount ?: 0).let { count ->
                        if (newLike) max(count - 1, 0) else count + 1
                    }
                }
            }
        }
    }

    fun onSimilarLoadMore(list: List<FeedItem>) {
        // 生成基础事件项目
        fun genBaseEventItemByFeedType(item: FeedItem): EventItem {
            return when {
                item.tryOnTaskId?.isNotBlank() == true -> EventItem(
                    itemCategory = EventItemCategory.TryOnCollage,
                    itemId = item.tryOnTaskId,
                    itemName = item.reasoning
                )

                else -> EventItem(
                    itemCategory = EventItemCategory.GeneralCollage,
                    itemId = item.moodboardId,
                    itemName = item.reasoning
                )
            }
        }

        val baseItems = list.map { item -> genBaseEventItemByFeedType(item) }
        val items = mutableListOf<EventItem>()

        // 添加一个单独的 reco 项目到最前面
        val firstItemWithRecommendId = list.firstOrNull { it.recommendId?.isNotEmpty() == true }
        if (firstItemWithRecommendId != null) {
            items.add(
                EventItem(
                    itemCategory = EventItemCategory.Reco,
                    itemId = firstItemWithRecommendId.recommendId,
                    itemName = firstItemWithRecommendId.reasoning
                )
            )
        }

        // 添加基础项目
        items.addAll(baseItems)

        // 上报列表展现事件
        metric(
            ViewItemList(
                itemListName = EventItemListName.ApFeedList,
                items = items
            )
        )
    }

    var productSaveTrigger by remember { mutableIntStateOf(0) }
    var productViewOffset by remember { mutableFloatStateOf(0f) }

    val carouselItemList = remember(item) {
        val list =
            item?.coverImageList?.filter { it.imageType == FeedCoverItemType.COLLAGE.value || (it.hiddenInFront == false && !it.coverUrl.isNullOrBlank()) }
        if (!list.isNullOrEmpty()) {
            list
        } else {
            listOf(FeedCoverItem(imageType = FeedCoverItemType.COLLAGE.value))
        }
    }

    val uid = item?.createdUserId ?: item?.userId
    fun gotoPublicProfile(userId: String? = null) {
        userId?.let {
            metric(
                SelectItem(
                    itemListName = EventItemListName.UserInfo,
                    method = EventMethod.Click,
                    actionType = EventActionType.EnterProfileOtherUser,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.User,
                            itemId = userId,
                            itemName = item?.userInfo?.userName,
                        ),
                        EventItem(
                            itemCategory = EventItemCategory.GeneralCollage,
                            itemId = item?.moodboardId,
                            itemName = item?.detailTitle
                        )
                    )
                )
            )

            navActions.navigateToUserPublicProfile(userId)
        }
    }

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "TryOnTaskView")
    val uiState by createViewModel.uiState.collectAsState()

    var commentOpen by remember { mutableStateOf(false) }
    fun clickComment() {
        if (createViewModel.isUserLoggedIn()) {
            commentOpen = true
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    fun closeComment() {
        commentOpen = false
    }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        commentOpen = true
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        commentOpen = true
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }

    Scaffold(
        topBar = {
            TopBar(
                immersive = true,
                onBack = { navActions.back() },
                modifier = Modifier.background(Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(end = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Row(
                        modifier = Modifier.clickable { gotoPublicProfile(uid) },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        item?.userInfo?.userImageUrl?.let { url ->
                            AsyncImage(
                                model = url,
                                contentDescription = "user avatar",
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape),
                                contentScale = ContentScale.Crop
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        item?.userInfo?.userName?.let { name ->
                            Text(
                                text = name, style = AppThemeTextStyle.Body11H
                            )
                        }
                    }

                    Spacer(modifier = Modifier.weight(1f))

                    item?.userInfo?.let { userInfo ->
                        uid?.let { uid ->
                            val selfId = runBlocking { UserDataStoreManager.getUserId() }

                            if (uid == selfId) {
                                // 删除按钮 dropdown menu
                                ActionDropdownMenu(
                                    menu = { onClose ->
                                        DeletePostButton(
                                            onClick = {
                                                showDeleteConfirmation = true

                                                metric(
                                                    SelectItem(
                                                        itemListName = EventItemListName.DeleteBtn,
                                                        method = EventMethod.Click,
                                                        items = listOf(
                                                            EventItem(
                                                                itemCategory = EventItemCategory.GeneralCollage,
                                                                itemId = item.tryOnTaskId,
                                                                itemName = item.detailTitle
                                                            )
                                                        )
                                                    )
                                                )

                                                onClose()
                                            }
                                        )
                                    }
                                )
                            } else {
                                val initialFollowStatus = when (userInfo.followStatus) {
                                    FollowStatus.Followed.value -> FollowStatus.Followed
                                    FollowStatus.Mutual.value -> FollowStatus.Mutual
                                    FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                                    FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                                    else -> null
                                }
                                FollowAction(
                                    initialFollowStatus = initialFollowStatus,
                                    userId = uid,
                                    onFollow = { state, newFollowStatus ->
                                        item.userInfo?.followStatus = newFollowStatus.value

                                        metric(
                                            SelectItem(
                                                itemListName = EventItemListName.FollowBtn,
                                                method = EventMethod.Click,
                                                actionType = if (state) EventActionType.Follow else EventActionType.Unfollow,
                                                items = listOf(
                                                    EventItem(
                                                        itemCategory = EventItemCategory.User,
                                                        itemId = uid,
                                                        itemName = userInfo.userName,
                                                    ),
                                                    EventItem(
                                                        itemCategory = EventItemCategory.GeneralCollage,
                                                        itemId = item.moodboardId,
                                                        itemName = item.detailTitle
                                                    )
                                                )
                                            )
                                        )
                                    },
                                ) { isFollowing, followStatus, clickFollow ->
                                    FeedFollowButton(
                                        modifier = Modifier
                                            .height(36.dp)
                                            .width(100.dp),
                                        onClick = { clickFollow() },
                                        isFollowing = isFollowing,
                                        followStatus = followStatus,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
    ) { paddingValues ->
        Box(modifier = Modifier.fillMaxSize()) {
            item?.let {
                val containerBg = jsonContent?.background?.let { bg ->
                    if (bg.startsWith("#")) Color(bg.trim().toColorInt()) else Color.White
                } ?: Color.White

                Column(
                    modifier = Modifier
                        .verticalScroll(scrollState)
                        .padding(
                            top = paddingValues.calculateTopPadding(),
                            bottom = paddingValues.calculateBottomPadding()
                        )
                ) {
                    Box {
                        CarouselContainer(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.7f)
                                .clipToBounds(),
                            containerModifier = Modifier.aspectRatio(0.75f),
                            items = carouselItemList,
                            indicator = true,
                        ) { it ->
                            when (it.imageType) {
                                FeedCoverItemType.COLLAGE.value -> {
                                    Box(
                                        contentAlignment = Alignment.Center,
                                        modifier = Modifier
                                            .background(containerBg)
                                            .fillMaxSize()
                                    ) {
                                        item.moodboards?.let {
                                            MoodboardRenderer(
                                                modifier = Modifier.fillMaxHeight(),
                                                item = jsonContent,
                                                onItemClick = { onBlockClick(it) },
                                                animate = true,
                                                interactive = true,
                                                boundary = "h",
                                                showTags = true,
                                            )
                                        }

                                        item.communityInfo?.let { commInfo ->
                                            EventBanner(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .align(Alignment.BottomCenter),
                                                item = commInfo,
                                                onClick = {
                                                    navActions.navigateToHashtagDetail(
                                                        commInfo.hashtag
                                                    )
                                                }
                                            )
                                        }
                                    }
                                }

                                else -> {
                                    Box(
                                        contentAlignment = Alignment.Center,
                                        modifier = Modifier.fillMaxSize(),
                                    ) {
                                        AsyncImage(
                                            it.coverUrl,
                                            null,
                                            contentScale = ContentScale.FillHeight,
                                            modifier = Modifier.fillMaxHeight(),
                                        )

                                        item.communityInfo?.let { commInfo ->
                                            EventBanner(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .align(Alignment.BottomCenter),
                                                item = commInfo,
                                                onClick = {
                                                    navActions.navigateToHashtagDetail(
                                                        commInfo.hashtag
                                                    )
                                                }
                                            )
                                        }
                                    }

                                }
                            }
                        }


                    }

                    SaveActionContainer(
                        type = CollectionType.Collage,
                        id = item.moodboardId,
                        initialState = saved,
                        onPreAction = {
                            clickSave(it)
                        },
                        onAction = {
                            item.isFavorited = it
                        }
                    ) { state, onClick ->
                        DetailDesc(
                            item = item,
                            modifier = Modifier.padding(bottom = 16.dp),
                            liked = liked,
                            onLikeClick = { clickLike() },
                            sharedCount = sharedCount,
                            onShareClick = { clickShare() },
                            saved = state,
                            onSaveClick = { onClick() },
                            onCommentClick = { clickComment() },
                            navActions = navActions
                        )
                    }


                    item.moodboards?.products?.let { products ->
                        LaunchedEffect(Unit) {
                            metric(
                                ViewItemList(
                                    refer = refer,
                                    itemListName = EventItemListName.CollageEntityList,
                                    items = products.mapIndexed { index, it ->
                                        EventItem(
                                            itemId = it.id,
                                            itemName = it.brand,
                                            itemCategory = EventItemCategory.Product,
                                            index = index
                                        )
                                    })
                            )
                        }
                        Box(
                            modifier = Modifier.onGloballyPositioned { coordinates ->
                                productViewOffset = coordinates.positionInParent().y
                            }
                        ) {
                            ProductView(
                                refer = refer,
                                renderTrigger = productSaveTrigger,
                                products = products,
                                onProductClick = { product -> openProductLink(product) }
                            )
                        }
                    }

                    SharePanelDrawer(
                        open = shareOpen,
                        onClose = { shareOpen = false },
                    ) {
                        CollageSharePanel(item = item.moodboards)
                    }

                    item.moodboards?.let {
                        ProductAction(
                            selectedBlock,
                            moodboard = it,
                            onClose = { selectedBlock = null },
                            onRemix = { product -> remixProduct(product) },
                            onTryOn = { product -> tryOnProduct(product, false) },
                            onOpenProductWeb = { url -> navActions.navigateToProductWeb(url) },
                        )
                    }

                    // More like this section
                    DetailSimilar(
                        itemId = item.moodboardId,
                        onItemClick = { clickSimilarItem(it) },
                        onItemView = { onSimilarItemView(it) },
                        onLikeClick = { item, newLike -> clickSimilarLike(item, newLike) },
                        onLoadMore = { onSimilarLoadMore(it) },
                        feedDetailViewModel = viewModel
                    )

                    // ProductInfoDrawer removed - products now open external links directly
                }
            } ?: run {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = paddingValues.calculateBottomPadding()),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }

            // 全局悬浮按钮组
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 40.dp)
            ) {
                // 胶囊形状的容器
                Row(
                    modifier = Modifier
                        .height(40.dp)
                        .width(214.dp)
                        .clip(MaterialTheme.shapes.large)
                        .background(Color.White)
                        .shadow(8.dp, MaterialTheme.shapes.large)
                        .clickable { /* 整体点击处理 */ },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Try On 按钮 - 只在 isTryOn 为 true 时显示
                    if (item?.isTryOn == true) {
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                                .background(Color.White.copy(alpha = 0.9f))
                                .clickable {
                                    item.moodboards?.products?.firstOrNull()?.let { product ->
                                        tryOnProduct(product, true)
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painterResource(R.drawable.icon_try_on_star),
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )

                                Text(
                                    text = stringResource(R.string.text_try_on),
                                    style = AppThemeTextStyle.Body14H,
                                    color = Color.Black
                                )
                            }
                        }

                        // 分隔线
                        Box(
                            modifier = Modifier
                                .width(1.dp)
                                .height(32.dp)
                                .background(Color.Gray.copy(alpha = 0.3f))
                        )
                    }

                    // 价格按钮 - 始终显示
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .background(Color.Black)
                            .clickable {
                                // 埋点：价格按钮点击滚动到产品列表
                                metric(
                                    SelectItem(
                                        itemListName = EventItemListName.ApProductListScrollBtn,
                                        method = EventMethod.Click,
                                        actionType = EventActionType.ProductListScroll,
                                        items = listOf(
                                            EventItem(
                                                itemCategory = EventItemCategory.GeneralCollage,
                                                itemId = item?.moodboardId.orEmpty(),
                                                itemName = item?.detailTitle.orEmpty()
                                            )
                                        )
                                    )
                                )

                                // 滚动到Products部分
                                coroutineScope.launch {
                                    if (productViewOffset > 0) {
                                        // 减去悬浮按钮高度(40dp) + 底部边距(40dp) + 额外空间(20dp) = 100dp
                                        val offsetPaddingPx = with(density) { 100.dp.toPx() }
                                        val offsetWithPadding =
                                            (productViewOffset - offsetPaddingPx).coerceAtLeast(0f)
                                        scrollState.animateScrollTo(offsetWithPadding.toInt())
                                    } else {
                                        // 如果位置还没计算出来，滚动到页面底部
                                        scrollState.animateScrollTo(scrollState.maxValue)
                                    }
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.icon_bag_line),
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = Color.White
                            )

                            Text(
                                text = stringResource(R.string.text_shop),
                                style = AppThemeTextStyle.Body14H,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }

        // 添加 TryOnPanelDrawer
        tryOnItems?.let {
            TryOnPanelDrawer(open = tryOnOpen, onClose = { tryOnOpen = false }) {
                MoodboardTryOnPanel(
                    items = it,
                    onClose = { tryOnOpen = false },
                    onTryOn = { selectedItems ->
                        tryOnOpen = false
                        item?.let { item ->
                            coroutineScope.launch {
                                val model = UserDataStoreManager.getModelInfo()
                                val selectedProducts = selectedItems.mapNotNull { selected ->
                                    item.moodboards?.products?.find { product -> product.globalId == selected.itemId }
                                }

                                val imageType = selectedItems.filter { it.itemType == "user_image" }

                                val isFeedValue = feedType == "feed"

                                val params = TryOnParams(
                                    modelId = model.second ?: "",
                                    moodboardId = item.moodboardId,
                                    taskId = item.taskId,
                                    internalImageList = selectedItems.mapNotNull { p -> p.imageUrl },
                                    products = selectedProducts,
                                    userImage = imageType.firstOrNull()?.imageUrl,
                                    userImageTag = imageType.firstOrNull()?.garmentType,
                                    isFeed = isFeedValue,
                                )

                                metric(
                                    SelectItem(
                                        EventItemListName.TryOnBtn,
                                        method = EventMethod.Click,
                                        actionType = if (model.second.isNullOrBlank()) EventActionType.TryOnNoAvatar else EventActionType.TryOn,
                                        items = listOf(
                                            EventItem(
                                                itemCategory = EventItemCategory.GeneralCollage,
                                                itemId = item.moodboardId,
                                                itemName = item.detailTitle
                                            )
                                        )
                                    )
                                )

                                openTryOn(params)
                            }
                        }
                    })
            }
        }

        // 添加SearchPanel组件
        SearchPanel(
            isVisible = searchPanelVisible && !showCameraPicker,
            onDismiss = {
                searchPanelVisible = false
                capturedImageUri = null
                capturedSearchText = ""
            },
            viewModel = hiltViewModel(),
            imageUrl = capturedImageUri,
            searchQueryText = capturedSearchText,
            onCameraRequest = {
                showCameraPicker = true
            },
            onSearch = { q, img, stylesList, budget, isProModeEnabled ->
                onSearch(
                    q,
                    img,
                    stylesList,
                    budget,
                    capturedBrandItem,
                    capturedBrandProduct,
                    isProModeEnabled
                )
            },
            onUpdateQuery = { query ->
                capturedSearchText = query
            },
            navActions = navActions,
            placeholderText = stringResource(R.string.text_describe_an_occasion_vibe_or_something_you_want_help_with)
        )

        // 添加CameraPicker组件
        if (showCameraPicker) {
            CameraPicker(
                onPhotoTaken = { uri, searchQueryWords ->
                    capturedImageUri = uri.toString()
                    capturedSearchText += searchQueryWords
                    showCameraPicker = false
                },
                onMiss = {
                    showCameraPicker = false
                    capturedImageUri = null
                },
                onSelectBrand = { q, b, bp ->
//                    capturedBrandItem = b
//                    capturedBrandProduct = bp
//                    capturedImageUri = bp.showImage
//                    capturedSearchText += q
                    showCameraPicker = false
                    onSearch(q, bp.showImage ?: "", null, null, b, bp, false)
                }
            )
        }

        // 删除确认弹窗
        if (showDeleteConfirmation) {
            ModalBottomSheet(
                onDismissRequest = { showDeleteConfirmation = false },
                sheetState = sheetState,
                dragHandle = null,
            ) {
                Column(modifier = Modifier.background(MaterialTheme.colorScheme.surface)) {
                    Column(
                        modifier = Modifier
                            .background(MaterialTheme.colorScheme.background)
                            .fillMaxWidth()
                            .padding(top = 8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                    ) {
                        Text(
                            stringResource(R.string.text_are_you_sure_you_want_to_delete_this_post),
                            style = AppThemeTextStyle.Body11H.copy(
                                AppThemeColors.Gray700
                            )
                        )

                        TextButton(
                            modifier = Modifier.fillMaxWidth(), onClick = {
                                deletePost()
                            }
                        ) {
                            if (deleteConfirmationLoading) {
                                BaseLoading(strokeWidth = 2.dp, modifier = Modifier.size(16.dp))
                            } else {
                                Text(
                                    stringResource(R.string.text_delete_post),
                                    style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Red500)
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Column(
                        modifier = Modifier
                            .background(MaterialTheme.colorScheme.background)
                            .fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        TextButton(
                            modifier = Modifier.fillMaxWidth(), onClick = {
                                coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                                    if (!sheetState.isVisible) {
                                        showDeleteConfirmation = false
                                    }
                                }
                            }) {
                            Text(
                                stringResource(R.string.text_cancel),
                                style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray700)
                            )
                        }
                    }
                }
            }
        }


        item?.let {
            CommentPanelDrawer(open = commentOpen, onClose = { closeComment() }) {
                CommentContainer(feedItem = item, onUserClick = { gotoPublicProfile(it) })
            }
        }
    }
}
