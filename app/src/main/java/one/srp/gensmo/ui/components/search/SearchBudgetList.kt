package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import one.srp.core.network.model.EditorTag
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun SearchBudgetList(
    budgetOptions: Map<String, EditorTag>,
    selectedBudget: EditorTag?,
    modifier: Modifier = Modifier,
    onBudgetClick: (EditorTag) -> Unit = {}
) {
    Column(
        modifier = modifier
    ) {
        Text(
            text = "Price preferences",
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            style = AppThemeTextStyle.Heading16D
        )

        Row(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            budgetOptions.values.forEach { budget ->
                BudgetCard(
                    budget = budget,
                    isSelected = budget == selectedBudget,
                    onClick = { onBudgetClick(budget) }
                )
            }
        }
    }
}

@Composable
private fun BudgetCard(
    budget: EditorTag,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier
            .offset(x = 0.dp, y = 0.dp)
            .shadow(
                elevation = 6.dp,
                spotColor = Color(0x14000000),
                ambientColor = Color(0x14000000)
            )
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color.Black else Color(0xFFF5F5F5),
                shape = RoundedCornerShape(4.dp)
            )
            .height(40.dp)
            .background(
                color = Color(0xFFFFFFFF),
                shape = RoundedCornerShape(4.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .height(20.dp)
                    .background(
                        color = Color(0xFF434343),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .padding(2.18182.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = budget.icon,
                    style = AppThemeTextStyle.Body12H,
                    color = Color.White
                )
            }
            Text(
                text = budget.text,
                style = AppThemeTextStyle.Body12H,
                maxLines = 1
            )
        }
    }
}
