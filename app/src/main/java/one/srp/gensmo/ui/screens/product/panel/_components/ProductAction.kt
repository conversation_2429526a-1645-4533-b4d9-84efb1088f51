package one.srp.gensmo.ui.screens.product.panel._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowOutward
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun ProductAction(
    item: ProductItem,
    onRemix: () -> Unit = {},
    onShopNow: () -> Unit = {},
    shopNowEnabled: Boolean = true,
    refer: EventRefer? = null,
) {
    // Initialize metric helper for product detail refer
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = refer?.let { metricViewModel.compatMetric((it)) } ?: run { {} }

    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier.padding(20.dp, 8.dp)
    ) {
        Button(
            onClick = {
                // 埋点：商品详情页重新混合按钮点击
                metric(
                    SelectItem(
                        itemListName = EventItemListName.RemixBtn,
                        method = EventMethod.Click,
                        actionType = EventActionType.CollageGen,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.Product,
                                itemId = item.globalId,
                                itemName = item.title
                            )
                        )
                    )
                )
                onRemix()
            },
            modifier = Modifier
                .weight(1f)
                .height(48.dp),
            shape = MaterialTheme.shapes.medium,
            colors = ButtonDefaults.buttonColors(
                MaterialTheme.colorScheme.secondary, MaterialTheme.colorScheme.onSecondary
            ),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 12.dp)
        ) {
            Text(stringResource(R.string.text_complete_the_look), style = AppThemeTextStyle.Body16H)
        }

        if (shopNowEnabled) {
            Button(
                onClick = { onShopNow() },
                modifier = Modifier
                    .weight(1f)
                    .height(48.dp),
                shape = MaterialTheme.shapes.medium
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(stringResource(R.string.text_shop_now), style = AppThemeTextStyle.Body16H)
                    Icon(
                        Icons.Default.ArrowOutward,
                        null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}
