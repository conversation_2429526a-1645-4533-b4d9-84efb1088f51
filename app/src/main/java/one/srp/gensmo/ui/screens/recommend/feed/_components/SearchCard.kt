package one.srp.gensmo.ui.screens.recommend.feed._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.ui.theme.appThemeShapes

@Composable
fun SearchCard(
    modifier: Modifier = Modifier,
    text: String = stringResource(R.string.text_describe_the_style_or_occasion_you_want_to_explore),
    onClick: () -> Unit = {},
) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .height(44.dp)
            .padding(horizontal = 8.dp)
            .then(modifier),
        colors = ButtonDefaults.buttonColors(Color.White, Color.Black),
        contentPadding = PaddingValues(horizontal = 10.dp),
        shape = appThemeShapes.large,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxSize(),
        ) {
            Icon(
                painterResource(R.drawable.icon_tryon_search),
                null,
                modifier = Modifier.size(24.dp)
            )

            Text(
                text = text,
                textAlign = TextAlign.Justify,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                style = AppThemeTextStyle.Body14LightH.copy(color = AppThemeColors.Gray500),
            )
        }
    }
}
