package one.srp.gensmo.ui.screens.session.chat._components.message

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import one.srp.core.network.model.AgentResMessage
import one.srp.core.network.model.ChatJson
import one.srp.core.network.model.ChatMessage
import one.srp.core.network.model.MoodboardEntity
import one.srp.core.network.model.SearchItem
import one.srp.core.network.model.SearchParams
import one.srp.core.network.model.SearchResMessage
import one.srp.core.network.model.SearchResMessageWrapper
import one.srp.core.network.model.SearchInspoRes
import one.srp.gensmo.ui.screens.session.chat._components.SessionNavigation
import timber.log.Timber
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel
import kotlinx.serialization.SerialName

@Serializable
data class AgentResponseContent(
    @SerialName("user_query") val userQuery: String? = null,
    @SerialName("strategy_count") val strategyCount: Int? = null,
    @SerialName("variations_per_strategy") val variationsPerStrategy: Int? = null,
    @SerialName("total_variants") val totalVariants: Int? = null,
    val moodboards: List<MoodboardEntity>? = null,
)

@Composable
fun AgentResComponent(
    item: AgentResMessage,
    modifier: Modifier = Modifier,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    val sharedNavViewModel: SharedNavViewModel = hiltViewModel()
    val decodedRes: SearchItem = try {
        // 先解析外层结构，获取 content 字段
        val agentResJson = item.value.agentRes.jsonObject
        val contentElement = agentResJson["content"]
        val actionElement = agentResJson["action"]?.jsonPrimitive?.content
        
        if (contentElement != null) {
            // 解析 content 内容
            val content = ChatJson.decodeFromJsonElement(AgentResponseContent.serializer(), contentElement)
            
            // 转换为 SearchItem 格式
            SearchItem(
                taskId = item.messageId, // 使用 messageId 作为 taskId
                moodboards = content.moodboards,
                query = content.userQuery,
                reasoning = null,
                searchProductList = null,
                image = null,
                imageUrl = null,
                imageDescription = null,
                status = if (actionElement == "end") "completed" else actionElement,
                displayText = null,
                errorMessage = null,
                imageWidth = null,
                imageHeight = null,
                useUserPreference = null,
                inFeedWhiteList = null,
                userImageTag = null,
            )
        } else {
            throw IllegalArgumentException("No content field found in agentRes")
        }
    } catch (e: Exception) {
        // 调试日志 - 记录解析失败的原因
        Timber.w(e)
        
        // 兜底：后端缺少 taskId 等必填时，构建最小可用对象
        SearchItem(
            taskId = item.messageId,
            moodboards = emptyList(),
            query = null,
            reasoning = null,
            searchProductList = null,
            image = null,
            imageUrl = null,
            imageDescription = null,
            status = null,
            displayText = null,
            errorMessage = null,
            imageWidth = null,
            imageHeight = null,
            useUserPreference = null,
            inFeedWhiteList = null,
            userImageTag = null,
        )
    }

    // 优先使用缓存中的最新状态（如收藏状态），否则使用解码结果
    val agentRes = sharedNavViewModel.getSearchItem(decodedRes.taskId) ?: decodedRes

    val fallbackQuery = agentRes.query?.takeIf { it.isNotBlank() }
        ?: agentRes.reasoning?.takeIf { it.isNotBlank() }
        ?: ""

    val defaultParams = SearchParams(
        debugLevel = 0,
        query = fallbackQuery,
        budget = "",
        isAsync = true,
        route = "",
        isPresetQuery = false,
        moodboardVersion = "v2",
        imageUrl = "",
        messageId = item.messageId,
        useOnlineImageSeg = false,
        inspoLabel = emptyList(),
        specifiedProduct = null,
        brand = "",
        isAgent = true,
    )

    val wrapped = SearchResMessage(
        sessionId = item.sessionId,
        messageId = item.messageId,
        role = item.role,
        visible = item.visible,
        value = SearchResMessageWrapper(
            searchQuery = item.value.searchQuery ?: defaultParams,
            searchRes = agentRes,
            inspo = item.value.inspo ?: SearchInspoRes(queryExtensionList = emptyList(), inspoTitle = null),
        )
    )
    SearchResComponent(wrapped, onMessage, onNavigate)
}

 