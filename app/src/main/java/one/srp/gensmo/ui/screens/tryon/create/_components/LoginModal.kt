package one.srp.gensmo.ui.screens.tryon.create._components

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import one.srp.gensmo.R
import androidx.compose.ui.text.style.TextDecoration
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.res.stringResource
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.IconButton
import androidx.compose.material3.Icon


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginModal(
    onDismiss: () -> Unit,
    isLoading: Boolean = false,
    titleText: String? = null,
    subtitleText: String? = null,
    showCloseButton: Boolean = false,
    onGoogleLogin: () -> Unit,
    onAppleLogin: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        sheetState = rememberModalBottomSheetState(),
        dragHandle = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                if (showCloseButton) {
                    IconButton(onClick = onDismiss, modifier = Modifier.align(Alignment.CenterStart)) {
                        Icon(imageVector = Icons.Filled.Close, contentDescription = null, tint = Color(0xFF333333))
                    }
                }
                BottomSheetDefaults.DragHandle(modifier = Modifier.align(Alignment.Center))
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
        ) {
            Box(modifier = Modifier.fillMaxWidth()) {
                // 移除内容区域中的关闭按钮，避免与标题重叠
                Text(
                    text = titleText ?: stringResource(id = R.string.text_log_in_to_continue),
                    style = AppThemeTextStyle.Heading20D,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    textAlign = TextAlign.Center,
                    color = Color(0xFF333333)
                )
            }
            Text(
                text = subtitleText ?: stringResource(id = R.string.text_this_helps_us_keep_your_data_safe_as_we_create_your_ai_avatar),
                style = AppThemeTextStyle.Body12H,
                color = Color(0xFF333333),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            Button(
                onClick = { onGoogleLogin() },
                modifier = Modifier
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFF4924EF).copy(alpha = 0.1f),
                                Color(0xFF8BE6F6).copy(alpha = 0.1f),
                                Color(0xFFF3B0BD).copy(alpha = 0.1f)
                            )
                        ),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .then(
                        Modifier.background(
                            color = Color(0xFFFFF8FA).copy(alpha = 0.5f),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                    ),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    contentColor = Color.Black
                ),
                enabled = !isLoading,
                contentPadding = PaddingValues(start = 24.dp, top = 16.dp, end = 24.dp, bottom = 16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_google),
                        contentDescription = stringResource(id = R.string.text_google_icon),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(stringResource(id = R.string.text_continue_with_google), style = AppThemeTextStyle.Body16H)
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = { onAppleLogin() },
                modifier = Modifier
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFF4924EF).copy(alpha = 0.1f),
                                Color(0xFF8BE6F6).copy(alpha = 0.1f),
                                Color(0xFFF3B0BD).copy(alpha = 0.1f)
                            )
                        ),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .then(
                        Modifier.background(
                            color = Color(0xFFFFF8FA).copy(alpha = 0.5f),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                    ),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    contentColor = Color.Black
                ),
                enabled = !isLoading,
                contentPadding = PaddingValues(start = 24.dp, top = 16.dp, end = 24.dp, bottom = 16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_apple),
                        contentDescription = stringResource(id = R.string.text_apple_icon),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(stringResource(id = R.string.text_continue_with_apple), style = AppThemeTextStyle.Body16H)
                }
            }

            val context = LocalContext.current
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 24.dp, bottom = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.text_by_tapping_continue),
                    style = AppThemeTextStyle.Body11H
                )
                
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(id = R.string.text_you_agree_to_our),
                        style = AppThemeTextStyle.Body11H
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(
                        text = stringResource(id = R.string.text_terms),
                        style = AppThemeTextStyle.Body11H.copy(
                            color = Color(0xFF333333),
                            textDecoration = TextDecoration.Underline
                        ),
                        modifier = Modifier.clickable {
                            val intent = Intent(Intent.ACTION_VIEW, "https://gensmo.com/about/terms".toUri())
                            context.startActivity(intent)
                        }
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(
                        text = stringResource(id = R.string.text_and),
                        style = AppThemeTextStyle.Body11H
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(
                        text = stringResource(id = R.string.text_privacy_policy),
                        style = AppThemeTextStyle.Body11H.copy(
                            color = Color(0xFF333333),
                            textDecoration = TextDecoration.Underline
                        ),
                        modifier = Modifier.clickable {
                            val intent = Intent(Intent.ACTION_VIEW, "https://gensmo.com/about/privacy".toUri())
                            context.startActivity(intent)
                        }
                    )
                }
            }
        }
    }
}
