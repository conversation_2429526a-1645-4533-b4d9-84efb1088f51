package one.srp.gensmo.ui.screens.onboard.result

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColorInt
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.network.model.MoodboardContent
import one.srp.core.network.utils.JSON
import one.srp.gensmo.MainApplication
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.collage.MoodboardRenderer

import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.onboard.OnboardResultViewModel
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.saveable.rememberSaveable
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.viewmodel.user.LoginUiState
import one.srp.gensmo.viewmodel.user.LoginViewModel
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventActionType

@Composable
fun OnboardResultScreen(
    navActions: NavActions = NavActions(),
    tags: List<String> = emptyList(),
    viewModel: OnboardResultViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val context = LocalContext.current
    val oneSignalManager = (context.applicationContext as MainApplication).oneSignalManager

    // 登录相关状态
    val loginViewModel: LoginViewModel = hiltViewModel()
    val loginUiState by loginViewModel.uiState.collectAsState()
    var showLoginModal by rememberSaveable { mutableStateOf(false) }

    // 记录当前选中页索引
    var selectedIndex by rememberSaveable { mutableStateOf(0) }
    val titleCandidates = remember {
        listOf(
            "First Collage, Who Dis",
            "New Feed, New Feel",
            "Testing My Palette",
            "My Style Loading",
            "Look And Decode Me",
            "Color Story, Page One"
        )
    }

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.OpenCollage)
    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView,
            )
        )
    }

    var viewListReported by rememberSaveable { mutableStateOf(false) }
    LaunchedEffect(uiState.isLoading, uiState.moodboards) {
        if (!uiState.isLoading && uiState.moodboards.isNotEmpty() && !viewListReported) {
            val items = uiState.moodboards.flatMapIndexed { index, it ->
                listOf(
                    EventItem(
                        itemCategory = EventItemCategory.CollageGenTask,
                        itemId = it.taskId,
                        itemName = it.reasoning,
                    ),
                    EventItem(
                        itemCategory = EventItemCategory.GeneralCollage,
                        itemId = it.moodboards?.id ?: it.moodboardId,
                        itemName = it.reasoning,
                        index = index,
                    ),
                )
            }
            metric(
                ViewItemList(
                    itemListName = EventItemListName.CollageList,
                    items = items,
                )
            )
            viewListReported = true
        }
    }

    // 监听错误状态并显示Snackbar
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(error)
            viewModel.clearError()
        }
    }

    // 页面加载时获取moodboard数据
    LaunchedEffect(tags) {
        if (tags.isNotEmpty()) {
            viewModel.fetchMoodboards(tags)
        }
    }

    // 是否保存并发布（仅 UI 选项）
    var saveAndPost by rememberSaveable { mutableStateOf(true) }

    fun complete(forcePublish: Boolean = false) {
        // 如需保存并发布，且已登录或登录成功后（forcePublish=true），则调用发布
        val isLoggedIn = loginUiState is LoginUiState.LoggedIn
        val shouldPublish = saveAndPost && (isLoggedIn || forcePublish)
        if (shouldPublish) {
            if (uiState.moodboards.isNotEmpty() && selectedIndex in uiState.moodboards.indices) {
                val item = uiState.moodboards[selectedIndex]
                val documentId = item.moodboardId
                // 上报：点击进入 Home 同时触发 Post 行为，携带当前选中的 general_collage id
                metric(
                    SelectItem(
                        itemListName = EventItemListName.HomeEntryBtn,
                        method = EventMethod.Click,
                        actionType = EventActionType.Post,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.GeneralCollage,
                                itemId = documentId,
                            )
                        )
                    )
                )
                val title = item.reasoning?.takeIf { it.isNotBlank() } ?: titleCandidates.random()
                // description 暂留空串
                viewModel.publishMoodboard(documentId = documentId, title = title, description = "")
            }
        }
        oneSignalManager.requestNotificationPermission(context)
        navActions.navigateToFeedRecommend()
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Scaffold(
            topBar = {
                TopBar(
                    transparent = true,
                    navIconEnable = false,
                )
            },
            bottomBar = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                        .padding(16.dp)
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Button(
                            onClick = {
                                metric(
                                    SelectItem(
                                        itemListName = EventItemListName.HomeEntryBtn,
                                        method = EventMethod.Click,
                                        actionType = EventActionType.EnterHome,
                                    )
                                )
                                val isLoggedIn = loginUiState is LoginUiState.LoggedIn
                                if (saveAndPost && !isLoggedIn) {
                                    showLoginModal = true
                                } else {
                                    complete()
                                }
                            },
                            enabled = !uiState.isLoading && loginUiState !is LoginUiState.Loading,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(54.dp),
                            shape = MaterialTheme.shapes.medium,
                        ) {
                            if (uiState.isLoading || loginUiState is LoginUiState.Loading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(20.dp),
                                    color = Color.White,
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text(stringResource(R.string.text_enter_gensmo), style = AppThemeTextStyle.Body16H)
                            }
                        }
                        // 完成状态：非加载时展示可选项
                        if (!uiState.isLoading) {
                            Spacer(modifier = Modifier.height(12.dp))
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                                modifier = Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .clickable { saveAndPost = !saveAndPost }
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Filled.CheckCircle,
                                    contentDescription = null,
                                    tint = if (saveAndPost) Color(0xFF22C55E) else Color(0xFFBDBDBD)
                                )
                                Spacer(modifier = Modifier.size(8.dp))
                                Text(
                                    text = stringResource(R.string.text_save_and_post_this_vibe_on_gensmo),
                                    style = AppThemeTextStyle.Body13LightH.copy(color = Color(0xFF9E9E9E))
                                )
                            }
                        }
                    }
                }
            },
            snackbarHost = {
                SnackbarHost(hostState = snackbarHostState)
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .padding(paddingValues)
                    .padding(horizontal = 16.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Spacer(modifier = Modifier.height(16.dp))
                if (uiState.isLoading) {
                    Text("Your look is generating...", style = AppThemeTextStyle.Heading24D)
                } else {
                    Text("Choose the vibe that feels closest", style = AppThemeTextStyle.Heading24D)
                }

                Spacer(modifier = Modifier.height(24.dp))

                when {
                    uiState.isLoading -> {
                        // 简单显示loading图片，支持横滑查看
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                                .height(400.dp)
                        ) {
                            OnboardLoadingMoodboardCard()
                        }
                    }
                    uiState.moodboards.isNotEmpty() -> {
                        val pagerState = rememberPagerState(pageCount = { uiState.moodboards.size })
                        LaunchedEffect(pagerState.currentPage) {
                            selectedIndex = pagerState.currentPage
                        }
                        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                            HorizontalPager(
                                 state = pagerState,
                                 modifier = Modifier.fillMaxWidth(),
                                 contentPadding = PaddingValues(horizontal = 24.dp),
                                 pageSpacing = 14.dp
                             ) { index ->
                                val item = uiState.moodboards[index]
                                item.moodboards?.let { moodboard ->
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth(),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        val isCurrent = pagerState.currentPage == index
                                        val cardModifier = if (isCurrent) {
                                            Modifier.fillMaxWidth().border(3.dp, Color.Black)
                                        } else {
                                            Modifier.fillMaxWidth()
                                        }
                                        MoodboardCard(
                                            moodboard = moodboard,
                                            modifier = cardModifier
                                        )
                                    }
                                }
                            }
                            // 指示器
                            Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
                                PagerDotsIndicator(count = uiState.moodboards.size, current = pagerState.currentPage)
                            }
                        }
                    }
                    tags.isEmpty() -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text("No tags selected")
                        }
                    }
                    else -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text("No moodboards available")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }
        }

        // 登录中的全屏遮罩 Loading（覆盖页面内容，但不影响 ModalBottomSheet 的层级）
        if (loginUiState is LoginUiState.Loading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                BaseLoading(color = Color.White)
            }
        }
    }

    if (showLoginModal) {
        LoginModal(
            onDismiss = {
                showLoginModal = false
                complete()
            },
            isLoading = loginUiState is LoginUiState.Loading,
            titleText = stringResource(R.string.text_log_in_to_save_and_post_this_vibe),
            subtitleText = stringResource(R.string.text_this_lets_you_enjoy_the_full_experience_while_we_keep_your_data_safe),
            showCloseButton = true,
            onGoogleLogin = {
                showLoginModal = false
                loginViewModel.setOnLoginSuccess {
                    complete(true)
                }
                loginViewModel.startLogin(context)
            },
            onAppleLogin = {
                showLoginModal = false
                loginViewModel.setOnLoginSuccess {
                    complete(true)
                }
                loginViewModel.startAppleLogin(context)
            }
        )
    }
}

@Composable
private fun OnboardLoadingMoodboardCard(
    modifier: Modifier = Modifier
) {
    // 仅限定高度为 400dp，宽度按比例自适应；由父容器提供横向滚动
    Image(
        painter = painterResource(R.drawable.image_session_collage_loading_bg),
        contentDescription = null,
        contentScale = ContentScale.FillHeight,
        modifier = modifier.height(400.dp)
    )
}

@Composable
private fun MoodboardCard(
    moodboard: one.srp.core.network.model.MoodboardEntity,
    modifier: Modifier = Modifier
) {
    val parsedContent = remember(moodboard) {
        try {
            moodboard.parsedContent ?: JSON.decodeFromString<MoodboardContent>(moodboard.content)
        } catch (e: Exception) {
            null
        }
    }

    parsedContent?.let { content ->
        val backgroundColor = remember(content) {
            content.background?.let { bg ->
                if (bg.startsWith("#")) {
                    try {
                        Color(bg.trim().toColorInt())
                    } catch (e: Exception) {
                        Color.White
                    }
                } else Color.White
            } ?: Color.White
        }

        // 固定 3:4 宽高比
        val ratio = 3f / 4f
        Card(
            modifier = modifier
                .fillMaxWidth()
                .aspectRatio(ratio),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(backgroundColor)
            ) {
                MoodboardRenderer(
                    modifier = Modifier.fillMaxSize(),
                    item = content,
                    offscreen = true,
                    animate = false,
                    interactive = false
                )
            }
        }
    }
}

@Composable
private fun PagerDotsIndicator(count: Int, current: Int, modifier: Modifier = Modifier) {
	if (count <= 1) return
	Row(modifier = modifier, horizontalArrangement = Arrangement.spacedBy(4.dp)) {
		repeat(count) { index ->
			val isSelected = index == current
			Box(
				modifier = Modifier
					.size(width = if (isSelected) 12.dp else 8.dp, height = 6.dp)
					.background(color = if (isSelected) Color.Black else Color.LightGray, shape = RoundedCornerShape(3.dp))
			)
		}
	}
}
