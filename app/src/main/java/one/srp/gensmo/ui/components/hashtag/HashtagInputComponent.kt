package one.srp.gensmo.ui.components.hashtag

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.network.model.HashtagItem
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.hashtag.HashtagInputViewModel

/**
 * A reusable hashtag input component that provides hashtag suggestions and selection functionality.
 *
 * @param selectedHashtags List of currently selected hashtags (supports default/initial hashtags)
 * @param onHashtagsChanged Callback when the selected hashtags change
 * @param modifier Modifier for the component
 * @param placeholder Placeholder text for the input field
 * @param maxHashtags Maximum number of hashtags allowed
 * @param viewModel ViewModel for managing hashtag input state
 */
@Composable
fun HashtagInputComponent(
    selectedHashtags: List<HashtagItem>,
    onHashtagsChanged: (List<HashtagItem>) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "# Add hashtags...",
    maxHashtags: Int = 10,
    viewModel: HashtagInputViewModel = hiltViewModel(),
) {
    val uiState by viewModel.uiState.collectAsState()
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    var isFocused by remember { mutableStateOf(false) }

    // Initialize ViewModel with default hashtags on first composition
    LaunchedEffect(Unit) {
        if (selectedHashtags.isNotEmpty() && uiState.selectedHashtags.isEmpty()) {
            viewModel.setInitialHashtags(selectedHashtags)
        }
    }

    // Sync external selected hashtags with ViewModel when they change
    LaunchedEffect(selectedHashtags) {
        if (selectedHashtags != uiState.selectedHashtags) {
            viewModel.setInitialHashtags(selectedHashtags)
        }
    }

    // Notify parent when selected hashtags change in ViewModel
    LaunchedEffect(uiState.selectedHashtags) {
        if (uiState.selectedHashtags != selectedHashtags) {
            onHashtagsChanged(uiState.selectedHashtags)
        }
    }

    // Clear error when user starts typing
    LaunchedEffect(uiState.inputText) {
        if (uiState.error != null && uiState.inputText.isNotEmpty()) {
            viewModel.clearError()
        }
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Selected hashtags display at the top
        AnimatedVisibility(
            visible = uiState.selectedHashtags.isNotEmpty(),
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            LazyRow(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(
                    items = uiState.selectedHashtags,
                    key = { it.hashtag }
                ) { hashtag ->
                    HashtagChip(
                        hashtag = hashtag,
                        onRemove = { viewModel.removeHashtag(hashtag) }
                    )
                }
            }
        }

        // Input field container
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column {
                // Input field with gray background
                TextField(
                    value = uiState.inputText,
                    onValueChange = { text ->
                        if (uiState.selectedHashtags.size < maxHashtags) {
                            viewModel.updateInputText(text)
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester)
                        .onFocusChanged { focusState ->
                            isFocused = focusState.isFocused
                            if (!focusState.isFocused && uiState.inputText.isNotBlank()) {
                                viewModel.addCustomHashtag(uiState.inputText, maxHashtags)
                            }
                        },

                    placeholder = {
                        Text(
                            text = if (uiState.selectedHashtags.size >= maxHashtags) {
                                "Maximum $maxHashtags hashtags reached"
                            } else {
                                placeholder
                            },
                            style = AppThemeTextStyle.Body16H.copy(
                                color = Color(0xFF999999)
                            )
                        )
                    },
                    singleLine = true,
                    enabled = uiState.selectedHashtags.size < maxHashtags,
                    colors = TextFieldDefaults.colors(
                        unfocusedContainerColor = Color(0xFFF5F5F5),
                        focusedContainerColor = Color(0xFFF5F5F5),
                        unfocusedIndicatorColor = Color.Transparent,
                        focusedIndicatorColor = Color.Transparent,
                        disabledContainerColor = Color(0xFFF5F5F5),
                        disabledIndicatorColor = Color.Transparent
                    ),
                    textStyle = AppThemeTextStyle.Body16H.copy(
                        color = Color(0xFF000000)
                    ),
                    shape = RoundedCornerShape(8.dp),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (uiState.inputText.isNotBlank()) {
                                viewModel.addCustomHashtag(uiState.inputText, maxHashtags)
                            }
                        }
                    )
                )

                // Suggestions dropdown
                AnimatedVisibility(
                    visible = isFocused && uiState.suggestions.isNotEmpty() && uiState.inputText.isNotBlank(),
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    SuggestionsDropdown(
                        suggestions = uiState.suggestions.take(5),
                        onSuggestionClick = { hashtag ->
                            viewModel.selectHashtag(hashtag, maxHashtags)
                            focusManager.clearFocus()
                        }
                    )
                }

                // Loading indicator
                AnimatedVisibility(
                    visible = uiState.isLoadingSuggestions && isFocused && uiState.inputText.isNotBlank(),
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    LoadingIndicator()
                }

                // Error message
                AnimatedVisibility(
                    visible = uiState.error != null,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    uiState.error?.let { error ->
                        ErrorMessage(
                            error = error,
                            onRetry = { viewModel.retrySuggestions() },
                            onDismiss = { viewModel.clearError() }
                        )
                    }
                }
            }
        }
    }
}

/**
 * Suggestions dropdown container
 */
@Composable
private fun SuggestionsDropdown(
    suggestions: List<HashtagItem>,
    onSuggestionClick: (HashtagItem) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(
                color = Color.White,
                shape = RoundedCornerShape(12.dp)
            )
            .border(
                width = 1.dp,
                color = Color(0xFFE8E8E8),
                shape = RoundedCornerShape(12.dp)
            )
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp)
        ) {
            items(
                items = suggestions,
                key = { it.hashtag }
            ) { hashtag ->
                HashtagSuggestionItem(
                    hashtag = hashtag,
                    onClick = { onSuggestionClick(hashtag) }
                )
            }
        }
    }
}

/**
 * Individual suggestion item in the dropdown
 */
@Composable
private fun HashtagSuggestionItem(
    hashtag: HashtagItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.weight(1f)
        ) {
            // Hashtag icon
            if (hashtag.iconUrl != null) {
                AsyncImage(
                    model = hashtag.iconUrl,
                    contentDescription = "Hashtag icon",
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            color = Color(0xFFE0E0E0),
                            shape = RoundedCornerShape(4.dp)
                        ),
                    placeholder = painterResource(id = R.drawable.icon_hashtag_button),
                    error = painterResource(id = R.drawable.icon_hashtag_button)
                )
            } else {
                // Default hashtag icon
                Icon(
                    painter = painterResource(id = R.drawable.icon_hashtag_button),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = Color(0xFF666666)
                )
            }

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = hashtag.hashtag,
                    style = AppThemeTextStyle.Body16H.copy(
                        color = Color(0xFF000000)
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                // Show frequency if available
                hashtag.frequency?.let { frequency ->
                    Text(
                        text = "${formatPostCount(frequency)} posts",
                        style = AppThemeTextStyle.Body14H.copy(
                            color = Color(0xFF999999)
                        )
                    )
                }
            }
        }

        // Trending indicator if frequency is high
        hashtag.frequency?.let { frequency ->
            if (frequency > 1_000_000) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_arrow_top_right),
                    contentDescription = "Trending",
                    modifier = Modifier.size(16.dp),
                    tint = Color(0xFFFF6B6B)
                )
            }
        }
    }
}

/**
 * Formats post count for display (e.g., 21200000 -> "21.2M")
 */
private fun formatPostCount(count: Int): String {
    return when {
        count >= 1_000_000 -> {
            val millions = count / 1_000_000.0
            if (millions >= 10) {
                "${millions.toInt()}M"
            } else {
                "%.1fM".format(millions)
            }
        }

        count >= 1_000 -> {
            val thousands = count / 1_000.0
            if (thousands >= 10) {
                "${thousands.toInt()}K"
            } else {
                "%.1fK".format(thousands)
            }
        }

        else -> count.toString()
    }
}

/**
 * Individual hashtag chip with remove functionality
 */
@Composable
private fun HashtagChip(
    hashtag: HashtagItem,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .background(
                color = Color(0xFFF0F0F0),
                shape = MaterialTheme.shapes.medium,
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = hashtag.hashtag,
                style = AppThemeTextStyle.Body14H.copy(
                    color = Color(0xFF000000)
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Box(
                modifier = Modifier
                    .size(16.dp)
                    .clickable { onRemove() },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Remove hashtag",
                    modifier = Modifier.size(10.dp),
                    tint = AppThemeColors.Gray600,
                )
            }
        }
    }
}

/**
 * Loading indicator for hashtag suggestions
 */
@Composable
private fun LoadingIndicator(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.padding(horizontal = 16.dp)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(14.dp),
                color = Color(0xFF999999),
                strokeWidth = 1.5.dp
            )
            Text(
                text = "Loading...",
                style = AppThemeTextStyle.Body14H.copy(
                    color = Color(0xFF999999)
                )
            )
        }
    }
}

/**
 * Error message with retry and dismiss options
 */
@Composable
private fun ErrorMessage(
    error: String,
    onRetry: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFFFFF0F0),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = error,
                    style = AppThemeTextStyle.Body14H.copy(
                        color = Color(0xFFCC0000)
                    ),
                    modifier = Modifier.weight(1f)
                )
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(20.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Dismiss error",
                        modifier = Modifier.size(14.dp),
                        tint = Color(0xFFCC0000)
                    )
                }
            }

            // Show retry button for network-related errors
            if (isNetworkError(error)) {
                Text(
                    text = "Retry",
                    style = AppThemeTextStyle.Body14H.copy(
                        color = Color(0xFF007AFF)
                    ),
                    modifier = Modifier
                        .clickable { onRetry() }
                        .padding(vertical = 4.dp)
                )
            }
        }
    }
}

/**
 * Determines if an error is network-related and should show retry option
 */
private fun isNetworkError(error: String): Boolean {
    val networkKeywords = listOf(
        "internet connection",
        "network",
        "timed out",
        "connect to server",
        "connection"
    )
    return networkKeywords.any { keyword ->
        error.lowercase().contains(keyword)
    }
}

