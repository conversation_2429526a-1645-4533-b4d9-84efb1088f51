package one.srp.gensmo.ui.navigation

import timber.log.Timber
import java.net.URLEncoder

sealed class NavRoutes(val route: String) {
    data object Root : NavRoutes("/")

    sealed class User(route: String) : NavRoutes(route) {
        data object Profile : User("profile")
        data object Settings : User("settings")
        data object Account : User("account")
        data object Library : User("library?selectedTab={selectedTab}") {
            fun createRoute(selectedTab: Int = 0) = "library?selectedTab=$selectedTab"
        }

        data object AccountEdit : User("account/edit")
        data object Login : User("user/login")
        data object CheckIn : User("user/checkIn")

        data object PublicProfile : User("user/profile/public/{userId}") {
            fun createRoute(userId: String) = "user/profile/public/$userId"
        }

        data object Followers : User("user/followers/{userId}") {
            fun createRoute(userId: String) = "user/followers/$userId"
        }

        data object Following : User("user/following/{userId}") {
            fun createRoute(userId: String) = "user/following/$userId"
        }
    }

    sealed class Feed(route: String) : NavRoutes(route) {
        data object Recommend : Feed("feed")
        data object Detail : Feed("feed/detail/{id}?refer={refer}") {
            fun createRoute(id: String, refer: String? = null) = buildString {
                append("feed/detail/$id")
                if (!refer.isNullOrEmpty()) {
                    append("?refer=$refer")
                } else {
                    append("?refer=")
                }
            }
        }
    }

    data object Camera : NavRoutes("camera?mode={mode}") {
        fun createRoute(mode: String? = null) = "camera?mode=${
            mode?.let { URLEncoder.encode(it, "UTF-8") } ?: ""
        }"
    }

    data object Login : NavRoutes("login?query={query}&imageUrl={imageUrl}") {
        fun createRoute(query: String?, imageUrl: String?) = buildString {
            append("login?query=")
            append(query?.let {
                URLEncoder.encode(it, "UTF-8")
            } ?: "")
            append("&imageUrl=")
            append(imageUrl?.let {
                URLEncoder.encode(it, "UTF-8")
            } ?: "")
        }.also {
            Timber.d("Generated route: '$it'")
        }
    }

    sealed class Collage(route: String) : NavRoutes(route) {
        data object Search :
            Collage("collage/search?query={query}&imageUrl={imageUrl}&budget={budget}&stylesList={stylesList}") {
            fun createRoute(
                query: String,
                imageUrl: String? = null,
                budget: String? = null,
                stylesList: String? = null,
            ): String {
                // 预处理query，确保LLM能理解，然后进行标准URL编码
                val cleanedQuery = query
                    .replace("**", "")           // 移除markdown粗体标记
                    .replace("*", "")            // 移除其他markdown标记
                    .replace("% ", " percent ")  // 处理百分号和空格的组合，避免编码问题
                    .replace("%", " percent")    // 处理单独的百分号
                    .trim()                      // 去除首尾空格

                return "collage/search?query=${
                    URLEncoder.encode(
                        cleanedQuery,
                        "UTF-8"
                    )
                }&imageUrl=${imageUrl?.let { URLEncoder.encode(it, "UTF-8") } ?: ""}${
                    budget?.let { "&budget=${URLEncoder.encode(it, "UTF-8")}" } ?: ""
                }${
                    stylesList?.let { "&stylesList=${URLEncoder.encode(it, "UTF-8")}" } ?: ""
                }"
            }
        }

        data object Task : Collage("collage/task/{id}") {
            fun createRoute(id: String) = "collage/task/$id"
        }
    }

    data object History : NavRoutes("history")
    data object Preference : NavRoutes("preference?source={source}") {
        fun createRoute(source: String? = null) =
            "preference?source=${source?.let { URLEncoder.encode(it, "UTF-8") }}"
    }

    data object Saved : NavRoutes("saved")

    sealed class TryOn(route: String) : NavRoutes(route) {
        data object Task : TryOn("tryon/task/{id}") {
            fun createRoute(id: String) = "tryon/task/$id"
        }

        data object Generate : TryOn("tryon/generate")

        object Create : TryOn("tryon/create?source={source}") {
            const val CREATE_ROUTE = "tryon/create"
            fun routeWithSource(source: String) = "$CREATE_ROUTE?source=$source"
        }

        data object Model : TryOn("tryon/model?setFace={setFace}&userPreference={userPreference}") {
            fun createRoute(setFace: String? = null, userPreference: String? = null) =
                "tryon/model?setFace=${
                    setFace?.let {
                        URLEncoder.encode(it, "UTF-8")
                    } ?: ""
                }&userPreference=${
                    userPreference?.let {
                        URLEncoder.encode(it, "UTF-8")
                    } ?: ""
                }"
        }

        data object Detail : Feed("tryon/detail/{id}?refer={refer}") {
            fun createRoute(id: String, refer: String? = null) = buildString {
                append("tryon/detail/$id")
                if (!refer.isNullOrEmpty()) {
                    append("?refer=$refer")
                } else {
                    append("?refer=")
                }
            }
        }
    }

    data object Closet : NavRoutes("closet")

    sealed class Session(route: String) : NavRoutes(route) {
        data object Chat : Session("session/chat")

        sealed class View(route: String) : Session(route) {
            data object CollageTask : View("session/view/collage/task/{id}?i={index}") {
                fun createRoute(id: String, index: Int = 0) =
                    "session/view/collage/task/$id?i=$index"
            }

            data object TryOnTask : View("session/view/tryon/task/{id}") {
                fun createRoute(id: String) = "session/view/tryon/task/$id"
            }
        }

        data object Login : Session("session/login")
    }

    data object Activity : NavRoutes("activity?url={url}") {
        fun createRoute(activityUrl: String) = "activity?url=${
            URLEncoder.encode(activityUrl, "UTF-8")
        }"
    }

    data object ProductWeb : NavRoutes("web/product?url={url}") {
        fun createRoute(url: String) = "web/product?url=${URLEncoder.encode(url, "UTF-8")}"
    }

    sealed class Onboard(route: String) : NavRoutes(route) {
        data object Welcome : Onboard("onboard/welcome")
        data object Pick : Onboard("onboard/pick")
        data object Result : Onboard("onboard/result/{tags}")
        data object Login : Onboard("onboard/login")
    }

    sealed class Detail(route: String) : NavRoutes(route) {
        data object PostEditor : Detail("detail/post/edit?id={id}&type={type}") {
            fun createRoute(id: String? = null, type: String = "collage") = buildString {
                append("detail/post/edit?id=")
                append(id ?: "")
                append("&type=")
                append(type)
            }
        }

        data object Hashtag : Detail("detail/hashtag/{hashtag}") {
            fun createRoute(hashtag: String) = "detail/hashtag/${
                URLEncoder.encode(
                    if (hashtag.startsWith("#")) hashtag.slice(1 until hashtag.length) else hashtag,
                    "UTF-8"
                )
            }"
        }
    }

    data object Notification : NavRoutes("notification")
}