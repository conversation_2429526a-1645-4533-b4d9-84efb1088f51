package one.srp.gensmo.ui.screens.camera

import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.viewmodel.camera.CameraViewModel

@Composable
fun CameraScreen(
    navActions: NavActions,
    onPhotoTaken: (String, String) -> Unit,
    onSelectedBrand: (String, String, String?, ProductItem?) -> Unit,
    viewModel: CameraViewModel = viewModel(),
    cameraModeTitle: String? = null,
) {
    CameraPicker(
        onPhotoTaken = onPhotoTaken,
        onMiss = { navActions.back() },
        onSelectBrand = { t, b, bp ->
            bp.showImage?.let { uri ->
                onSelectedBrand(uri, t, b.brand, bp.product)
            }
        },
        viewModel = viewModel,
        cameraModeTitle = cameraModeTitle
    )
}