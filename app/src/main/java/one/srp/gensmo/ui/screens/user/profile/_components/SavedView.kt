package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.SavedItem
import one.srp.gensmo.R
import one.srp.gensmo.ui.screens.recommend._components.FeedList
import one.srp.gensmo.ui.screens.user.profile._viewmodel.SavedListViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun SavedView(
    modifier: Modifier = Modifier,
    onItemClick: (SavedItem) -> Unit = {},
    viewModel: SavedListViewModel = hiltViewModel(),
) {
    // 埋点：Profile 收藏列表增量展示
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Profile)
    val itemList = viewModel.pageFlow.collectAsLazyPagingItems()
    // 记录已上报数量，初始为0
    val lastReportedCount = remember { mutableStateOf(0) }
    // 监听分页加载完成，增量上报新数据
    LaunchedEffect(itemList.loadState.append) {
        if (itemList.loadState.append is LoadState.NotLoading) {
            val allItems = itemList.itemSnapshotList.items.filterNotNull()
            if (allItems.size > lastReportedCount.value) {
                // 取新增部分
                val newItems = allItems.subList(lastReportedCount.value, allItems.size)
                val eventItems = newItems.mapIndexed { idx, saved ->
                    val category = when (saved.collectionType) {
                        CollectionType.Collage.value, CollectionType.CollageFromSearch.value -> EventItemCategory.GeneralCollage
                        CollectionType.TryOn.value -> EventItemCategory.TryOnTask
                        CollectionType.Product.value -> EventItemCategory.Product
                        else -> EventItemCategory.PseudoProduct
                    }
                    EventItem(
                        itemCategory = category,
                        itemId = saved.collectionId,
                        itemName = saved.detailTitle ?: saved.reasoning,
                        index = lastReportedCount.value + idx
                    )
                }
                metric(
                    ViewItemList(
                        itemListName = EventItemListName.GalleryTabCollectsList,
                        items = eventItems
                    )
                )
                // 更新已报数量
                lastReportedCount.value = allItems.size
            }
        }
    }

    Box(modifier = modifier) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            if (itemList.itemCount == 0) {
                EmptySaved()
            } else {
                FeedList(modifier = modifier, items = itemList) {
                    SavedItem(item = it, onItemClick = onItemClick)
                }
            }
        }
    }
}

@Composable
private fun EmptySaved() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(30.dp)
    ) {
//        Image(painterResource(R.drawable.image_collects_placeholder), null)
//
//        Text(
//            stringResource(R.string.text_all_your_collected_items_will_appear_here),
//            style = AppThemeTextStyle.Heading20D,
//            modifier = Modifier.width(222.dp),
//            maxLines = 2,
//            overflow = TextOverflow.Visible,
//            textAlign = TextAlign.Center,
//        )

        Image(painterResource(R.drawable.image_empty_post), null)

        Text(
            text = "You haven't collected any note yet",
            style = AppThemeTextStyle.Body14H.copy(AppThemeColors.Gray500),
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
fun SavedItem(item: SavedItem, onItemClick: (SavedItem) -> Unit) {
    SavedCard(
        modifier = Modifier
            .padding(2.dp)
            .fillMaxWidth(),
        item = item,
        onItemClick = { onItemClick(item) },
    )
}
