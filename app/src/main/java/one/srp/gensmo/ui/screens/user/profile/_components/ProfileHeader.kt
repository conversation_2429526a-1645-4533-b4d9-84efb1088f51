package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.AvatarThumbViewModel
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.NumberFormatter

@Composable
fun ProfileHeader(
    modifier: Modifier = Modifier,
    navActions: NavActions = NavActions(),
    viewModel: UserProfileViewModel = hiltViewModel(),
    onFollowersClick: (() -> Unit)? = null,
    onFollowingClick: (() -> Unit)? = null,
) {
    LaunchedEffect(Unit) {
        viewModel.getUserInfo()
    }
    // Initialize metric helper for library button
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Profile)

    val avatarVM: AvatarThumbViewModel = hiltViewModel()
    val imageUrlState = avatarVM.imageUrlState.collectAsState()
    val imageUrl = imageUrlState.value

    // 获取用户统计数据
    val followingCount by viewModel.followingCount.collectAsState()
    val followerCount by viewModel.followerCount.collectAsState()
    val totalLikes by viewModel.totalLikes.collectAsState()

    fun gotoCloset() {
        // 埋点：管理模特按钮点击
        metric(
            SelectItem(
                itemListName = if (imageUrl.isNullOrBlank()) EventItemListName.NoAvatarCreateBtn else EventItemListName.ManageAvatarBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterAvatarManagement
            )
        )
        navActions.navigateToCloset()
    }

    fun gotoSettings() {
        metric(
            SelectItem(
                itemListName = EventItemListName.SettingsBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterSetting
            )
        )
        navActions.navigateToUserSettings()
    }

    // Navigate to user library with metric
    fun gotoLibrary() {
        metric(
            SelectItem(
                itemListName = EventItemListName.HistoryBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterHistory
            )
        )
        navActions.navigateToUserLibrary()
    }

    Column(modifier = modifier.padding(horizontal = 16.dp, vertical = 16.dp)) {
        // 顶部行：用户名和设置图标
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val userName by viewModel.userName.collectAsState()
            Text(
                text = userName,
                style = AppThemeTextStyle.Heading18D,
                modifier = Modifier.weight(1f)
            )
            
            Box(
                modifier = Modifier.clickable { gotoSettings() },
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painterResource(R.drawable.icon_settings),
                    null,
                    modifier = Modifier.size(24.dp),
                    colorFilter = ColorFilter.tint(Color(0xFF868D94))
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 头像和统计数据行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 用户头像
            Box {
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                ) {
                    val userAvatar by viewModel.userAvatar.collectAsState()
                    AsyncImage(
                        model = when (userAvatar) {
                            "icon_random_thumb" -> R.drawable.icon_random_thumb
                            "icon_unregister" -> R.drawable.icon_unregister
                            else -> userAvatar
                        },
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop,
                        error = painterResource(id = R.drawable.icon_unregister)
                    )
                }

                // 编辑按钮
                Button(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(24.dp),
                    shape = MaterialTheme.shapes.medium,
                    onClick = {
                        // 埋点：头像按钮点击进入编辑
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.PortraitBtn,
                                method = EventMethod.Click,
                                actionType = EventActionType.EnterProfileEdit
                            )
                        )
                        navActions.navigateToUserAccountEdit()
                    },
                    contentPadding = PaddingValues(4.dp),
                    colors = ButtonDefaults.buttonColors(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.onSurface
                    )
                ) {
                    Image(painterResource(R.drawable.icon_edit), null)
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 统计数据
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "Following",
                    value = NumberFormatter.format(followingCount),
                    modifier = Modifier.weight(1f),
                    onClick = onFollowingClick
                )
                
                StatItem(
                    label = "Followers",
                    value = NumberFormatter.format(followerCount),
                    modifier = Modifier.weight(1f),
                    onClick = onFollowersClick
                )
                
                StatItem(
                    label = "Likes",
                    value = NumberFormatter.format(totalLikes),
                    modifier = Modifier.weight(1f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 底部按钮行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Manage your Avatar 按钮
            ManageAvatarButton(
                onClick = { gotoCloset() },
                navActions = navActions,
                modifier = Modifier.weight(1.5f)
            )

            // History 按钮
            HistoryButton(
                onClick = { gotoLibrary() },
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null
) {
    Column(
        modifier = modifier.then(
            if (onClick != null) {
                Modifier.clickable { onClick() }
            } else {
                Modifier
            }
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = AppThemeTextStyle.Heading16D,
            color = AppThemeColors.Black
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            style = AppThemeTextStyle.Body12LightH,
            color = AppThemeColors.Gray600,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun ManageAvatarButton(
    onClick: () -> Unit,
    navActions: NavActions,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .height(48.dp)
            .background(
                color = AppThemeColors.Gray50,
                shape = MaterialTheme.shapes.medium
            )
            .padding(horizontal = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                AvatarThumb(navActions = navActions)
                Text("Manage your Avatar", style = AppThemeTextStyle.Body12H)
            }
            Icon(Icons.Default.ChevronRight, null, modifier = Modifier.size(16.dp))
        }
    }
}

@Composable
private fun HistoryButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .height(48.dp)
            .background(
                color = AppThemeColors.Gray50,
                shape = MaterialTheme.shapes.medium
            )
            .padding(horizontal = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                Image(
                    painterResource(R.drawable.icon_tryon_history),
                    null,
                    modifier = Modifier.size(16.dp),
                    colorFilter = ColorFilter.tint(Color(0xFF868D94))
                )
                Text("History", style = AppThemeTextStyle.Body12H)
            }
            Icon(Icons.Default.ChevronRight, null, modifier = Modifier.size(16.dp))
        }
    }
}
