package one.srp.gensmo.ui.screens.tryon.task._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.remote.ProductService
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class TryOnProductAlternativesViewModel @Inject constructor() : ViewModel() {
    private val _queryStatus = MutableStateFlow(TaskStatus.Idle)
    val queryStatus: StateFlow<TaskStatus> = _queryStatus.asStateFlow()

    private val _queryResult = MutableStateFlow<List<ProductItem>?>(null)
    val queryResult: StateFlow<List<ProductItem>?> = _queryResult.asStateFlow()

    private var queryJob: Job? = null

    fun getTryOnProductAlternatives(moodboardId: String, tag: String) {
        queryJob = viewModelScope.launch {
            try {
                val res = ProductService.api.getTryOnProductWithTag(moodboardId, tag)

                if (res.isSuccessful) {
                    res.body()?.let {
                        _queryResult.emit(it)
                        _queryStatus.emit(TaskStatus.Success)
                    }
                } else {
                    _queryStatus.emit(TaskStatus.Fail)
                }
            } catch (e: Exception) {
                Timber.e(e)
                _queryStatus.emit(TaskStatus.Fail)
            }
        }
    }
}