package one.srp.gensmo.ui.components.camera

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.view.Surface
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.Crossfade
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.FlingBehavior
import androidx.compose.foundation.gestures.ScrollScope
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Cameraswitch
import androidx.compose.material.icons.filled.FlashOff
import androidx.compose.material.icons.filled.FlashOn
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.BrandItem
import one.srp.core.network.model.BrandProductItem
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.mime.tranImageUriToUrl
import one.srp.gensmo.viewmodel.camera.CameraViewModel
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.abs

@Composable
fun CameraPicker(
    onPhotoTaken: (String, String) -> Unit,
    onMiss: () -> Unit,
    onSelectBrand: (String, BrandItem, BrandProductItem) -> Unit = { _, _, _ -> },
    viewModel: CameraViewModel = viewModel(),
    cameraModeTitle: String? = null,
    cameraProductViewModel: CameraProductViewModel = hiltViewModel(),
    metricViewModel: MetricViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    var lensFacing by remember { mutableIntStateOf(CameraSelector.LENS_FACING_BACK) }
    var preview by remember { mutableStateOf<Preview?>(null) }
    var imageCapture by remember { mutableStateOf<ImageCapture?>(null) }
    var camera by remember { mutableStateOf<Camera?>(null) }
    var shouldRebindCamera by remember { mutableStateOf(true) }
    var isCapturing by remember { mutableStateOf(false) }
    var previewEnabled by remember { mutableStateOf(true) }
    var flashEnabled by remember { mutableStateOf(false) }
    var hasFlash by remember { mutableStateOf(false) }

    var uploading by remember { mutableStateOf(false) }

    val metric = metricViewModel.compatMetric(EventRefer.CameraMode)

    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView
            )
        )
    }

    // 添加权限状态
    var hasCameraPermission by remember {
        mutableStateOf(
            ContextCompat.checkSelfPermission(
                context, Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED
        )
    }

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasCameraPermission = isGranted
    }

    // 请求权限
    LaunchedEffect(Unit) {
        if (!hasCameraPermission) {
            permissionLauncher.launch(Manifest.permission.CAMERA)
        }
    }

    val coroutineScope = rememberCoroutineScope()
    fun processTake(uri: Uri) {
        coroutineScope.launch {
            uploading = true
            val url = tranImageUriToUrl(uri.toString())
            url?.let { onPhotoTaken(url, viewModel.selectedMode.query) }
            uploading = false
        }
    }

    // 使用 ImagePicker 组件替代原来的相册选择
    val imagePicker = rememberImagePicker(onImageSelected = { uri ->
        processTake(uri)
    }, onError = { e ->
        e.printStackTrace()
    })

    LaunchedEffect(lensFacing, shouldRebindCamera) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()

            preview = Preview.Builder().setTargetRotation(Surface.ROTATION_0).build()

            imageCapture =
                ImageCapture.Builder().setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                    .setTargetRotation(Surface.ROTATION_0)
                    .setFlashMode(if (flashEnabled) ImageCapture.FLASH_MODE_ON else ImageCapture.FLASH_MODE_OFF)
                    .build()

            val cameraSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

            try {
                cameraProvider.unbindAll()
                camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner, cameraSelector, preview, imageCapture
                )
                hasFlash = camera?.cameraInfo?.hasFlashUnit() == true
                camera?.cameraControl?.enableTorch(flashEnabled)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }, ContextCompat.getMainExecutor(context))
    }

    // 添加一个函数来释放相机资源
    fun releaseCamera() {
        preview?.surfaceProvider = null
        preview = null
        imageCapture = null
        camera = null
        ProcessCameraProvider.getInstance(context).get().unbindAll()
    }

    val cameraProducts = cameraProductViewModel.cameraProducts.collectAsState().value
    var brandOpen by remember { mutableStateOf(false) }
    var currIndex by remember { mutableIntStateOf(0) }
    val currBrand = remember(cameraProducts, currIndex) { cameraProducts.getOrNull(currIndex) }
    fun onOpenBrand() {
        brandOpen = true

        metric(
            SelectItem(
                itemListName = EventItemListName.BrandBtn,
                method = EventMethod.Click,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Brand,
                        itemName = currBrand?.brand
                    )
                )
            )
        )
    }

    fun onCloseBrand() {
        brandOpen = false
    }

    fun onClickItem(item: BrandProductItem) {
        currBrand?.let { brand ->
            metric(
                SelectItem(
                    itemListName = EventItemListName.ProductListProduct,
                    method = EventMethod.Click,
                    actionType = EventActionType.CollageGen,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.Brand,
                            itemName = brand.brand
                        ),
                        EventItem(
                            itemCategory = EventItemCategory.Product,
                            itemId = item.product?.globalId,
                            itemName = item.product?.brand,
                        )
                    )
                )
            )

            // 先关闭品牌面板
            onCloseBrand()
            // 释放相机资源
            releaseCamera()
            // 调用回调
            onSelectBrand("Outfit styling for this piece", currBrand, item)
        }
    }

    fun onShuffleBrand() {
        if (cameraProducts.isEmpty()) return
        currIndex = (currIndex + 1) % cameraProducts.size
    }

    LaunchedEffect(cameraProducts) {
        if (cameraProducts.isNotEmpty()) {
            while (true) {
                delay(5000L)
                if (!brandOpen) {
                    onShuffleBrand()
                }
            }
        }
    }

    if (hasCameraPermission) {
        if (uploading || isCapturing) {
            Dialog(
                onDismissRequest = {}) {
                BaseLoading(color = Color.LightGray)
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
                .statusBarsPadding()
                .navigationBarsPadding()
                .clickable(interactionSource = null, indication = null, onClick = {}),
        ) {
            val iconSize = 68

            // 顶部按钮栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                IconButton(
                    onClick = {
                        releaseCamera()
                        onMiss()
                    }) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_close_round),
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    IconButton(
                        onClick = {
                            if (!isCapturing && hasFlash) {
                                flashEnabled = !flashEnabled
                                camera?.cameraControl?.enableTorch(flashEnabled)
                            }
                        },
                        enabled = !isCapturing && lensFacing == CameraSelector.LENS_FACING_BACK && hasFlash
                    ) {
                        Icon(
                            imageVector = if (flashEnabled) Icons.Default.FlashOn else Icons.Default.FlashOff,
                            contentDescription = if (flashEnabled) "关闭闪光灯" else "开启闪光灯",
                            tint = Color.White.copy(
                                alpha = if (!isCapturing && lensFacing == CameraSelector.LENS_FACING_BACK && hasFlash) 1f else 0.5f
                            )
                        )
                    }

                    IconButton(
                        onClick = {
                            if (!isCapturing) {
                                lensFacing = if (lensFacing == CameraSelector.LENS_FACING_BACK) {
                                    CameraSelector.LENS_FACING_FRONT
                                } else {
                                    CameraSelector.LENS_FACING_BACK
                                }
                                shouldRebindCamera = !shouldRebindCamera
                            }
                        }, enabled = !isCapturing
                    ) {
                        Icon(
                            imageVector = Icons.Default.Cameraswitch,
                            contentDescription = "切换相机",
                            tint = Color.White.copy(
                                alpha = if (isCapturing) 0.5f else 1f
                            )
                        )
                    }
                }
            }

            // 相机预览区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                AndroidView(
                    factory = { context ->
                        PreviewView(context).apply {
                            implementationMode = PreviewView.ImplementationMode.COMPATIBLE
                            scaleType = PreviewView.ScaleType.FILL_CENTER
                        }
                    }, modifier = Modifier.fillMaxSize()
                ) { previewView ->
                    if (previewEnabled) {
                        preview?.surfaceProvider = previewView.surfaceProvider
                    } else {
                        preview?.surfaceProvider = null
                    }
                }

                if (!brandOpen) {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        val overlayIcon = when (viewModel.selectedMode.title) {
                            "STYLE THIS PIECE" -> R.drawable.icon_clothes
                            else -> R.drawable.icon_rectangle
                        }

                        Box(
                            modifier = Modifier.fillMaxSize()
                        ) {
                            // 图标居中
                            Icon(
                                painter = painterResource(id = overlayIcon),
                                contentDescription = null,
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .fillMaxWidth()
                                    .scale(1.4f),
                                tint = Color.White
                            )

                            // 文字放在底部
                            Text(
                                text = "Good lighting, clean background, \ncomplete front view for best results.",
                                color = Color.White,
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .padding(PaddingValues(horizontal = 16.dp, vertical = 16.dp))
                            )
                        }
                    }
                }

                if (brandOpen) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                    ) {
                        BrandPanel(
                            currBrand,
                            onClick = { p -> onClickItem(p) },
                            onClose = { onCloseBrand() },
                            onShuffle = { onShuffleBrand() },
                        )
                    }
                }
            }

            // 底部控制区域（半透明黑色背景）
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.Black.copy(alpha = 0.7f))
            ) {
                Column {
                    // 模式选择区域
                    Box(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val listState = rememberLazyListState()
                        val screenWidth = LocalConfiguration.current.screenWidthDp.dp
                        val startPadding = (screenWidth) / 2

                        // 修改 FlingBehavior 的实现
                        val flingBehavior = remember {
                            object : FlingBehavior {
                                override suspend fun ScrollScope.performFling(initialVelocity: Float): Float {
                                    // 增加滚动速度
                                    val dampedVelocity = initialVelocity * 0.5f
                                    if (abs(dampedVelocity) < 400f) {
                                        return 0f
                                    }
                                    return dampedVelocity
                                }
                            }
                        }

                        var isLayoutReady by remember { mutableStateOf(false) }

                        LazyRow(
                            state = listState,
                            flingBehavior = flingBehavior,
                            contentPadding = PaddingValues(horizontal = startPadding),
                            modifier = Modifier
                                .fillMaxWidth()
                                .onGloballyPositioned {
                                    isLayoutReady = true
                                },
                            userScrollEnabled = true,
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            itemsIndexed(viewModel.getCameraModes()) { index, mode ->
                                var itemWidth by remember { mutableIntStateOf(0) }

                                Box(
                                    modifier = Modifier
                                        .wrapContentWidth()
                                        .onGloballyPositioned { coordinates ->
                                            itemWidth = coordinates.size.width
                                        }
                                        .clickable(
                                            enabled = !listState.isScrollInProgress, onClick = {
                                                viewModel.selectMode(mode)
                                                scope.launch {
                                                    listState.animateScrollToItem(
                                                        index, scrollOffset = itemWidth / 2
                                                    )
                                                }
                                            }), contentAlignment = Alignment.Center
                                ) {
                                    val isSelected = mode == viewModel.selectedMode
                                    val alpha by animateFloatAsState(
                                        targetValue = if (isSelected) 1f else 0.5f,
                                        animationSpec = tween(durationMillis = 100)
                                    )

                                    Text(
                                        text = mode.title,
                                        color = Color.White.copy(alpha = alpha),
                                        style = MaterialTheme.typography.bodyMedium,
                                        modifier = Modifier.padding(vertical = 20.dp)
                                    )
                                }
                            }
                        }

                        LaunchedEffect(isLayoutReady, cameraModeTitle) {
                            if (isLayoutReady) {
                                val targetIndex = if (!cameraModeTitle.isNullOrEmpty()) {
                                    // 如果传入了特定的模式名称，查找匹配的模式
                                    val index = viewModel.getCameraModes()
                                        .indexOfFirst { it.title == cameraModeTitle }
                                    if (index != -1) index else 2.coerceAtMost(viewModel.getCameraModes().size - 1)
                                } else {
                                    // 默认选择索引
                                    3.coerceAtMost(viewModel.getCameraModes().size - 1)
                                }

                                viewModel.selectMode(viewModel.getCameraModes()[targetIndex])
                                val itemSize =
                                    listState.layoutInfo.visibleItemsInfo.find { it.index == targetIndex }?.size
                                        ?: 0
                                listState.scrollToItem(
                                    targetIndex, scrollOffset = itemSize / 2
                                )
                            }
                        }

                        LaunchedEffect(listState) {
                            snapshotFlow {
                                val layoutInfo = listState.layoutInfo
                                val visibleItemsInfo = layoutInfo.visibleItemsInfo
                                val viewportWidth =
                                    layoutInfo.viewportEndOffset - layoutInfo.viewportStartOffset
                                val viewportCenter =
                                    layoutInfo.viewportStartOffset + (viewportWidth / 2)

                                // 获取最接近中心的项目
                                val centerItem = visibleItemsInfo.minByOrNull { itemInfo ->
                                    val itemCenter = itemInfo.offset + (itemInfo.size / 2)
                                    abs(itemCenter - viewportCenter)
                                }

                                centerItem?.index ?: 0
                            }.distinctUntilChanged().collect { centerIndex ->
                                // 立即更新选中的模式
                                viewModel.selectMode(viewModel.getCameraModes()[centerIndex])
                            }
                        }

                        var lastScrollOffset by remember { mutableIntStateOf(0) }

                        LaunchedEffect(listState.isScrollInProgress) {
                            if (!listState.isScrollInProgress && lastScrollOffset != listState.firstVisibleItemScrollOffset) {
                                lastScrollOffset = listState.firstVisibleItemScrollOffset

                                // 滚动停止时，计算最接近中心的项目
                                val layoutInfo = listState.layoutInfo
                                val visibleItemsInfo = layoutInfo.visibleItemsInfo
                                val viewportWidth =
                                    layoutInfo.viewportEndOffset - layoutInfo.viewportStartOffset
                                val viewportCenter =
                                    layoutInfo.viewportStartOffset + (viewportWidth / 2)

                                // 获取最接近中心的项目
                                val centerItem = visibleItemsInfo.minByOrNull { itemInfo ->
                                    val itemCenter = itemInfo.offset + (itemInfo.size / 2)
                                    abs(itemCenter - viewportCenter)
                                }

                                // 居中对齐最接近中心的项目
                                centerItem?.let {
                                    scope.launch {
                                        listState.animateScrollToItem(
                                            it.index, scrollOffset = it.size / 2
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // 底部按钮布局
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 32.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceAround,
                    ) {
                        // 相册选择按钮（左下角）
                        IconButton(
                            onClick = { imagePicker.launch() },
                            enabled = !isCapturing,
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.btn_upload_image),
                                contentDescription = "从相册选择",
                                tint = Color.White.copy(
                                    alpha = if (isCapturing) 0.5f else 1f
                                )
                            )
                        }

                        // 拍照按钮使用当前选中模式的图标
                        IconButton(
                            onClick = {
                                if (!isCapturing && imageCapture != null) {
                                    metric(
                                        SelectItem(
                                            itemListName = EventItemListName.ShutterBtn,
                                            method = EventMethod.Click,
                                            actionType = EventActionType.CollageGen,
                                            items = listOf(
                                                EventItem(
                                                    itemCategory = EventItemCategory.ShutterMode,
                                                    itemName = viewModel.selectedMode.title.lowercase()
                                                        .replace(" ", "_")
                                                ),
                                                EventItem(
                                                    itemCategory = EventItemCategory.UserQueryTextPic,
                                                    itemName = viewModel.selectedMode.query
                                                )
                                            )
                                        )
                                    )

                                    isCapturing = true
                                    previewEnabled = false
                                    takePhoto(
                                        context = context,
                                        imageCapture = imageCapture,
                                        onPhotoCaptured = { uri ->
                                            isCapturing = false
                                            previewEnabled = true
                                            releaseCamera()
                                            processTake(uri)
                                        },
                                        onError = {
                                            isCapturing = false
                                            previewEnabled = true
                                        })
                                }
                            },
                            enabled = !isCapturing,
                            modifier = Modifier
                                .size(iconSize.dp)
                                .weight(1f)
                        ) {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier.size(iconSize.dp)
                            ) {
                                // 底层的快门按钮
                                Image(
                                    painter = painterResource(id = R.drawable.btn_shutter),
                                    contentDescription = null,
                                    modifier = Modifier.size(iconSize.dp),
                                    alpha = if (isCapturing) 0.5f else 1f
                                )

                                // 上层的模式图标
                                Image(
                                    painter = painterResource(id = viewModel.selectedMode.iconResId),
                                    contentDescription = "拍照",
                                    modifier = Modifier.size((iconSize * 0.4f).dp),
                                    alpha = if (isCapturing) 0.5f else 1f,
                                    colorFilter = ColorFilter.tint(Color.Black)
                                )
                            }
                        }

                        Row(
                            modifier = Modifier.weight(1f),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            when (viewModel.selectedMode.title) {
                                "STYLE THIS PIECE", "GENERAL" -> {
                                    Box(modifier = Modifier.clickable {
                                        onOpenBrand()
                                    }) {
                                        Box(
                                            modifier = Modifier
                                                .graphicsLayer {
                                                    rotationZ = 30f
                                                    transformOrigin = TransformOrigin(1f, 1f)
                                                }
                                                .background(
                                                    AppThemeColors.Gray800,
                                                    MaterialTheme.shapes.large
                                                )
                                                .size(40.dp))

                                        Box(
                                            modifier = Modifier
                                                .graphicsLayer {
                                                    rotationZ = 15f
                                                    transformOrigin = TransformOrigin(1f, 1f)
                                                }
                                                .background(
                                                    AppThemeColors.Gray600,
                                                    MaterialTheme.shapes.large
                                                )
                                                .size(40.dp))

                                        Box(
                                            modifier = Modifier
                                                .clip(MaterialTheme.shapes.large)
                                                .border(
                                                    BorderStroke(1.dp, AppThemeColors.Gray200),
                                                    MaterialTheme.shapes.large
                                                )
                                                .background(AppThemeColors.White)
                                                .size(40.dp)
                                                .padding(2.dp),
                                            contentAlignment = Alignment.Center,
                                        ) {
                                            Crossfade(
                                                targetState = currBrand?.brandIcon,
                                                animationSpec = tween(durationMillis = 1200)
                                            ) { icon ->
                                                AsyncImage(
                                                    model = ImageRequest.Builder(LocalContext.current)
                                                        .data(icon).crossfade(true)
                                                        .build(),
                                                    contentDescription = null,
                                                    modifier = Modifier.size(36.dp),
                                                )
                                            }
                                        }
                                    }
                                }

                                else -> {}
                            }
                        }
                    }
                }
            }
        }
    } else {
        // 显示无权限提示
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Permission required to use this feature",
                    color = Color.Black,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                IconButton(
                    onClick = { onMiss() }) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_close_round),
                        contentDescription = "返回",
                        tint = Color.Black
                    )
                }
            }
        }
    }
}

@Composable
fun BrandPanel(
    item: BrandItem?,
    onClick: (BrandProductItem) -> Unit = {},
    onClose: () -> Unit = {},
    onShuffle: () -> Unit = {},
    metricViewModel: MetricViewModel = hiltViewModel(),
) {
    val metric = metricViewModel.compatMetric(EventRefer.CameraMode)

    LaunchedEffect(item) {
        item?.let { brand ->
            metric(
                ViewItemList(
                    itemListName = EventItemListName.ProductList,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.Brand,
                            itemName = brand.brand
                        ),
                    ) + item.products.map { bp ->
                        EventItem(
                            itemCategory = EventItemCategory.Product,
                            itemId = bp.product?.globalId,
                            itemName = bp.product?.brand,
                        )
                    }
                )
            )
        }
    }

    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .clip(MaterialTheme.shapes.large)
                        .border(
                            BorderStroke(1.dp, AppThemeColors.Gray200),
                            MaterialTheme.shapes.large
                        )
                        .background(AppThemeColors.White)
                        .size(28.dp)
                        .padding(2.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Crossfade(
                        targetState = item?.brandIcon,
                        animationSpec = tween(durationMillis = 1200)
                    ) { icon ->
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(icon).crossfade(true)
                                .build(),
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                        )
                    }
                }

                Text(
                    text = item?.brand ?: "",
                    style = AppThemeTextStyle.Heading14H.copy(AppThemeColors.White),
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            Button(
                onClick = { onShuffle() }, colors = ButtonDefaults.buttonColors(
                    containerColor = AppThemeColors.Black.copy(0.6f), contentColor = Color.White
                )
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        painterResource(R.drawable.icon_shuffle), null, tint = AppThemeColors.White
                    )
                    Text(text = "Shuffle")
                }
            }

            IconButton(
                onClick = { onClose() }, colors = IconButtonDefaults.iconButtonColors(
                    containerColor = AppThemeColors.Black.copy(0.6f), contentColor = Color.White
                )
            ) {
                Icon(Icons.Default.KeyboardArrowDown, null, modifier = Modifier.size(30.dp))
            }
        }

        LazyRow(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(Color.Transparent, AppThemeColors.Gray800.copy(0.6f))
                    )
                )
                .padding(top = 4.dp, bottom = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            itemsIndexed(item?.products ?: emptyList()) { index, bp ->
                LaunchedEffect(bp) {
                    metric(
                        SelectItem(
                            itemListName = EventItemListName.ProductListProduct,
                            method = EventMethod.TrueViewTrigger,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.Brand,
                                    itemName = item?.brand
                                ),
                                EventItem(
                                    itemCategory = EventItemCategory.Product,
                                    itemId = bp.product?.globalId,
                                    itemName = bp.product?.brand,
                                )
                            )
                        )
                    )
                }
                Box(
                    modifier = Modifier
                        .clip(MaterialTheme.shapes.large)
                        .background(AppThemeColors.Gray50)
                        .clickable { onClick(bp) }) {
                    AsyncImage(
                        model = bp.showImage,
                        contentDescription = null,
                        modifier = Modifier.size(80.dp),
                    )
                }
            }
        }
    }
}

private fun takePhoto(
    context: Context,
    imageCapture: ImageCapture?,
    onPhotoCaptured: (Uri) -> Unit,
    onError: () -> Unit,
) {
    imageCapture?.let {
        val photoFile = File(
            context.getExternalFilesDir(null), SimpleDateFormat(
                "yyyy-MM-dd-HH-mm-ss-SSS", Locale.CHINA
            ).format(System.currentTimeMillis()) + ".jpg"
        )

        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        it.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(outputFileResults: ImageCapture.OutputFileResults) {
                    val savedUri = Uri.fromFile(photoFile)
                    onPhotoCaptured(savedUri)
                }

                override fun onError(exception: ImageCaptureException) {
                    exception.printStackTrace()
                    onError()
                }
            })
    } ?: onError()
}
