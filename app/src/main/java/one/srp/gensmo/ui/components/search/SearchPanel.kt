package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.core.network.model.EditorTag
import one.srp.gensmo.data.model.GenerationState
import one.srp.core.network.model.HomePageInfoStyle
import one.srp.gensmo.data.repository.utils.BaseState
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.search.SearchExtensionViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchPanel(
    isVisible: Boolean,
    placeholderText: String = "",
    searchQueryText: String = "",
    imageUrl: String? = null,
    onDismiss: () -> Unit,
    viewModel: SearchExtensionViewModel = hiltViewModel(),
    onCameraRequest: () -> Unit = {},
    onSearch: (query: String, imageUrl: String, stylesList: String?, budget: String?, isProModeEnabled: Boolean) -> Unit = { _, _, _, _, _ -> },
    onUpdateQuery: (String) -> Unit = {},
    headerVisible: Boolean = true,
    navActions: NavActions,
) {
    var capturedText by remember { mutableStateOf("") }
    val suggestions by viewModel.randomSuggestions.collectAsStateWithLifecycle()
    val modelUrl by viewModel.modelUrl.collectAsStateWithLifecycle()
    val homeInfo by viewModel.homeInfo.collectAsStateWithLifecycle()
    val selectedStyles by viewModel.selectedStyles.collectAsStateWithLifecycle()
    val selectedBudget by viewModel.selectedBudget.collectAsStateWithLifecycle()
    val searchQueryValue by viewModel.searchQueryValue.collectAsStateWithLifecycle()
    val budgetOptions = viewModel.budgetOptions
    val refreshSuggestions = { viewModel.getRandomSuggestions() }
    val generationState by viewModel.generationState.collectAsStateWithLifecycle()
    val isTextFieldFocused by viewModel.isTextFieldFocused.collectAsStateWithLifecycle()
    val hasKeyboardBeenShown by viewModel.hasKeyboardBeenShown.collectAsStateWithLifecycle()
    val displayImageUrl by viewModel.displayImageUrl.collectAsStateWithLifecycle()
    val focusManager = LocalFocusManager.current

    LaunchedEffect(searchQueryText) {
        if (searchQueryText.isNotEmpty() && searchQueryValue.text != searchQueryText) {
            // 只有当外部传入的searchQueryText与当前的searchQueryValue不同时才更新
            // 保持当前的光标位置，确保不超过新文本的长度
            val currentSelection = searchQueryValue.selection.start

            viewModel.updateSearchQuery(
                TextFieldValue(
                    searchQueryText,
                    TextRange(minOf(currentSelection, searchQueryText.length))
                )
            )
        }
    }

    LaunchedEffect(imageUrl) {
        viewModel.updateDisplayImageUrl(imageUrl)
    }

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.SearchBoot)
    LaunchedEffect(isVisible) {
        if (isVisible) {
            // 页面曝光埋点
            metric(
                SelectItem(
                    itemListName = EventItemListName.Screen,
                    method = EventMethod.PageView
                )
            )
            capturedText = placeholderText
        }
    }

    LaunchedEffect(Unit) {
        viewModel.loadModelInfo()
        viewModel.fetchHomeInfo()
    }

    if (isVisible) {
        val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
        // 定义带埋点的 onSearch 包装函数
        val onSearchWithMetric =
            { query: String, imageUrl: String, stylesList: String?, budget: String?, isProModeEnabled: Boolean ->
                metric(
                    SelectItem(
                        itemListName = EventItemListName.GenerateBtn,
                        method = EventMethod.Click,
                        actionType = EventActionType.CollageGen,
                        items = listOf(
                            EventItem(
                                itemCategory = if (imageUrl.isBlank()) EventItemCategory.UserQueryTextPic else EventItemCategory.UserQueryTextOnly,
                                itemName = query,
                            ),
                            EventItem(
                                itemCategory = EventItemCategory.Model,
                                itemName = if (isProModeEnabled) "pro" else "normal",
                            )
                        )
                    )
                )
                onSearch(query, imageUrl, stylesList, budget, isProModeEnabled)
                viewModel.resetState()
                onDismiss()
            }

        ModalBottomSheet(
            onDismissRequest = {
                viewModel.resetState()
                onDismiss()
            },
            sheetState = sheetState,
            dragHandle = null,
            containerColor = Color.Transparent,
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
        ) {
            Box(modifier = Modifier.fillMaxWidth()) {
                Image(
                    painter = painterResource(id = R.drawable.overlay),
                    contentDescription = null,
                    modifier = Modifier.matchParentSize(),
                    contentScale = ContentScale.FillBounds,
                )

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    if (headerVisible) {
                        IconButton(
                            onClick = {
                                focusManager.clearFocus()
                                viewModel.resetState()
                                onDismiss()
                            },
                            modifier = Modifier
                                .padding(bottom = 0.dp)
                                .align(Alignment.Start)
                        ) {
                            Icons.Default.Close.let { closeIcon ->
                                Icon(
                                    imageVector = closeIcon,
                                    contentDescription = "关闭"
                                )
                            }
                        }

                        SearchTitle(
                            imageUrl = modelUrl,
                            modifier = Modifier
                                .fillMaxWidth(),
                            navActions = navActions
                        )

                        if (!isTextFieldFocused && searchQueryValue.text.isEmpty() && selectedStyles.isEmpty() && selectedBudget == null && imageUrl == null) {
                            SearchExtension(
                                navActions = navActions,
                                homeInfo = homeInfo,
                                onClickItem = {
                                    viewModel.resetState()
                                    onDismiss()
                                }
                            )
                        }
                    }

                    SearchContent(
                        placeholderText = capturedText,
                        searchQueryValue = searchQueryValue,
                        suggestions = suggestions,
                        onRefreshClick = refreshSuggestions,
                        onSearch = onSearchWithMetric,
                        onCameraClick = onCameraRequest,
                        onUpdateQuery = onUpdateQuery,
                        onFocusChanged = { focused ->
                            viewModel.updateTextFieldFocus(focused)
                        },
                        selectedStyles = selectedStyles,
                        onStyleDelete = { style ->
                            viewModel.toggleStyle(style)
                        },
                        selectedBudget = selectedBudget,
                        onBudgetDelete = { budget ->
                            viewModel.clearSelectedBudget()
                        },
                        onRefineQuery = { query ->
                            viewModel.queryPolishing(query)
                        },
                        onUpdateSearchQuery = { newValue ->
                            viewModel.updateSearchQuery(newValue)
                        },
                        generationState = generationState,
                        onUndoQueryPolishing = {
                            viewModel.undoQueryPolishing()
                        },
                        hasKeyboardBeenShown = hasKeyboardBeenShown,
                        displayImageUrl = displayImageUrl,
                        onUpdateDisplayImageUrl = { url ->
                            viewModel.updateDisplayImageUrl(url)
                        },
                        isProModeEnabled = viewModel.isProModeEnabled.collectAsStateWithLifecycle().value,
                        onToggleProMode = {
                            viewModel.toggleProMode()
                        }
                    )

                    if (!isTextFieldFocused && hasKeyboardBeenShown) {
                        when (homeInfo) {
                            is BaseState.Success -> {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(Color.White)
                                        .padding(bottom = 16.dp)
                                ) {
                                    Column(
                                        modifier = Modifier.fillMaxWidth(),
                                        verticalArrangement = Arrangement.spacedBy(16.dp)
                                    ) {
                                        SearchStyleList(
                                            homeInfo = homeInfo,
                                            onStyleClick = { style ->
                                                viewModel.toggleStyle(style)
                                            }
                                        )
                                        SearchBudgetList(
                                            budgetOptions = budgetOptions,
                                            selectedBudget = selectedBudget,
                                            onBudgetClick = { budget ->
                                                viewModel.selectBudget(budget)
                                            }
                                        )
                                    }
                                }
                            }

                            else -> {}
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun SearchContent(
    placeholderText: String,
    searchQueryValue: TextFieldValue,
    suggestions: List<String>,
    onRefreshClick: () -> Unit,
    onSearch: (query: String, imageUrl: String, stylesList: String?, budget: String?, isProModeEnabled: Boolean) -> Unit,
    onCameraClick: () -> Unit,
    onUpdateQuery: (String) -> Unit,
    onFocusChanged: (Boolean) -> Unit = {},
    selectedStyles: List<HomePageInfoStyle>,
    onStyleDelete: (HomePageInfoStyle) -> Unit,
    selectedBudget: EditorTag?,
    onBudgetDelete: (EditorTag) -> Unit,
    onRefineQuery: (String) -> Unit,
    onUpdateSearchQuery: (TextFieldValue) -> Unit,
    generationState: GenerationState,
    onUndoQueryPolishing: () -> Unit,
    hasKeyboardBeenShown: Boolean,
    displayImageUrl: String?,
    onUpdateDisplayImageUrl: (String?) -> Unit,
    isProModeEnabled: Boolean,
    onToggleProMode: () -> Unit,
) {
    var isKeyboardVisible by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

    LaunchedEffect(searchQueryValue) {
        if (searchQueryValue.text.isNotEmpty()) {
            isKeyboardVisible = true
        }
    }

    LaunchedEffect(isKeyboardVisible) {
        onFocusChanged(isKeyboardVisible)
        if (isKeyboardVisible) {
            focusRequester.requestFocus()
        } else {
            focusManager.clearFocus()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 0.dp)
            .background(Color.White)
    ) {
        TextField(
            value = searchQueryValue,
            onValueChange = { newValue ->
                onUpdateSearchQuery(newValue)
                onUpdateQuery(newValue.text)
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 4.dp)
                .then(
                    if (!hasKeyboardBeenShown) {
                        Modifier.height(300.dp)
                    } else {
                        Modifier
                    }
                )
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    if (focusState.isFocused && !isKeyboardVisible) {
                        isKeyboardVisible = true
                    }
                    if (!focusState.isFocused && isKeyboardVisible) {
                        isKeyboardVisible = false
                    }
                },
            placeholder = {
                Column(
                    modifier = Modifier.padding(top = 4.dp)
                ) {
                    Text(
                        text = placeholderText,
                        style = AppThemeTextStyle.Body16H.copy(
                            color = Color(0x80000000).copy(alpha = 0.5f)
                        )
                    )
                }
            },
            singleLine = false,
            maxLines = if (!hasKeyboardBeenShown) 8 else 4,
            colors = TextFieldDefaults.colors(
                unfocusedContainerColor = Color.Transparent,
                focusedContainerColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent
            )
        )

        if (searchQueryValue.text.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .padding(start = 16.dp, top = 16.dp, end = 16.dp, bottom = 16.dp)
                    .background(color = Color(0xFFF4F4F4), shape = RoundedCornerShape(size = 4.dp))
                    .padding(start = 8.dp, top = 4.dp, end = 8.dp, bottom = 4.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Icon(
                        painter = painterResource(
                            id = when (generationState) {
                                GenerationState.COMPLETED -> R.drawable.icon_back
                                else -> R.drawable.icon_star
                            }
                        ),
                        contentDescription = null,
                        tint = Color(0xFF222222),
                        modifier = Modifier.size(16.dp)
                    )
                    Text(
                        text = when (generationState) {
                            GenerationState.IDLE -> "Refine my idea"
                            GenerationState.GENERATING -> "Personalizing your style..."
                            GenerationState.COMPLETED -> "Undo"
                        },
                        style = AppThemeTextStyle.Body14H.copy(
                            color = Color(0xFF222222)
                        ),
                        modifier = Modifier
                            .clickable {
                                when (generationState) {
                                    GenerationState.IDLE -> onRefineQuery(searchQueryValue.text)
                                    GenerationState.GENERATING -> {}
                                    GenerationState.COMPLETED -> onUndoQueryPolishing()
                                }
                            }
                    )
                }
            }
        }

        FlowRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 16.dp, top = 0.dp, bottom = 20.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            selectedStyles.forEach { style ->
                SearchStyleTag(
                    style = style,
                    onDelete = onStyleDelete
                )
            }

            selectedBudget?.let { budget ->
                SearchBudgetTag(
                    budget = budget,
                    onDelete = onBudgetDelete
                )
            }
        }

        if (searchQueryValue.text.isEmpty() && !isKeyboardVisible) {
            SearchSuggestions(
                suggestions = suggestions,
                onSuggestionClick = { suggestion ->
                    val newValue = TextFieldValue(suggestion, TextRange(suggestion.length))
                    onUpdateSearchQuery(newValue)
                    onUpdateQuery(suggestion)
                },
                onRefreshClick = onRefreshClick
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧：图片按钮和Pro Mode开关
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                if (!displayImageUrl.isNullOrEmpty()) {
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .padding(4.dp)
                    ) {
                        AsyncImage(
                            model = displayImageUrl,
                            contentDescription = "搜索图片",
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(4.dp)),
                            contentScale = ContentScale.Crop
                        )

                        IconButton(
                            onClick = { onUpdateDisplayImageUrl(null) },
                            modifier = Modifier
                                .size(20.dp)
                                .align(Alignment.TopEnd)
                                .offset(x = 10.dp, y = (-10).dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭图片",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(16.dp)
                                    .background(
                                        color = Color.Black,
                                        shape = RoundedCornerShape(8.dp)
                                    )
                            )
                        }
                    }
                } else {
                    val dashColor = Color(0xFFE4E4E4)
                    val cornerRadiusValue = 10.dp
                    val strokeWidth = 1.dp
                    val dashPattern = floatArrayOf(10f, 10f)

                    Row(
                        modifier = Modifier
                            .size(40.dp)
                            .background(
                                color = Color(0xFFFAFAFA),
                                shape = RoundedCornerShape(size = cornerRadiusValue)
                            )
                            .drawBehind {
                                val stroke = Stroke(
                                    width = strokeWidth.toPx(),
                                    pathEffect = PathEffect.dashPathEffect(dashPattern, 0f)
                                )
                                drawRoundRect(
                                    color = dashColor,
                                    style = stroke,
                                    cornerRadius = CornerRadius(cornerRadiusValue.toPx())
                                )
                            }
                            .clickable {
                                onCameraClick()
                                onUpdateQuery(searchQueryValue.text)
                            },
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        IconButton(
                            onClick = {
                                onCameraClick()
                                onUpdateQuery(searchQueryValue.text)
                            },
                            modifier = Modifier.size(40.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.icon_add_outline),
                                contentDescription = "添加照片",
                                tint = Color(0xFF121212)
                            )
                        }
                    }
                }

                ProStyleSwitch(
                    checked = isProModeEnabled,
                    onCheckedChange = { onToggleProMode() }
                )
            }

            // 右侧：键盘和生成按钮
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            color = Color(0xFFEFEFEF),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .clickable { isKeyboardVisible = !isKeyboardVisible },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(
                            id = if (isKeyboardVisible)
                                R.drawable.icon_keyboard_hide
                            else
                                R.drawable.icon_keyboard_show
                        ),
                        contentDescription = if (isKeyboardVisible) "隐藏键盘" else "显示键盘",
                        tint = Color(0xFF1E1E1E),
                        modifier = Modifier.size(24.dp)
                    )
                }

                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            color = Color(0xFF1E1E1E),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .clickable {
                            onSearch(
                                searchQueryValue.text.ifEmpty { placeholderText },
                                displayImageUrl ?: "",
                                if (selectedStyles.isNotEmpty()) selectedStyles.map { style: HomePageInfoStyle -> style.title }
                                    .joinToString(",") else null,
                                selectedBudget?.value,
                                isProModeEnabled
                            )
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_arrow_up),
                        contentDescription = "生成",
                        tint = Color(0xFFF3F3F3),
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
} 