package one.srp.gensmo.ui.navigation

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.EaseIn
import androidx.compose.animation.core.EaseOut
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import androidx.navigation.navArgument
import androidx.navigation.navDeepLink
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import one.srp.core.network.model.ChatMessage
import one.srp.core.network.model.ChatMessageRole
import one.srp.core.network.model.SearchParams
import one.srp.core.network.model.SearchQueryMessage
import one.srp.core.network.model.SearchQueryMessageWrapper
import one.srp.core.network.model.TryOnParams
import one.srp.core.network.model.TryOnQueryMessage
import one.srp.core.network.model.TryOnQueryMessageWrapper
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.screens.camera.CameraScreen
import one.srp.gensmo.ui.screens.collage.search.CollageSearchScreen
import one.srp.gensmo.ui.screens.collage.task.CollageTaskScreen
import one.srp.gensmo.ui.screens.detail.collage.FeedDetailScreen
import one.srp.gensmo.ui.screens.detail.editor.PostEditorScreen
import one.srp.gensmo.ui.screens.detail.tryon.TryOnDetailScreen
import one.srp.gensmo.ui.screens.notification.NotificationScreen
import one.srp.gensmo.ui.screens.onboard.pick.OnboardPickScreen
import one.srp.gensmo.ui.screens.onboard.result.OnboardResultScreen
import one.srp.gensmo.ui.screens.onboard.welcome.OnboardWelcomeScreen
import one.srp.gensmo.ui.screens.recommend.feed.FeedHomeScreen
import one.srp.gensmo.ui.screens.recommend.hashtag.HashtagScreen
import one.srp.gensmo.ui.screens.session.chat.SessionChatScreen
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.screens.session.login.SessionLoginScreen
import one.srp.gensmo.ui.screens.session.view.search.CollageTaskViewScreen
import one.srp.gensmo.ui.screens.session.view.tryon.TryOnTaskViewScreen
import one.srp.gensmo.ui.screens.tryon._viewmodel.TryOnGenerateViewModel
import one.srp.gensmo.ui.screens.tryon.closet.ClosetScreen
import one.srp.gensmo.ui.screens.tryon.create.TryOnCreateScreen
import one.srp.gensmo.ui.screens.tryon.generate.TryOnGenerateScreen
import one.srp.gensmo.ui.screens.tryon.model.TryOnModelScreen
import one.srp.gensmo.ui.screens.tryon.task.TryOnTaskScreen
import one.srp.gensmo.ui.screens.user.account.UserAccountEditScreen
import one.srp.gensmo.ui.screens.user.account.UserAccountScreen
import one.srp.gensmo.ui.screens.user.checkin.UserCheckInScreen
import one.srp.gensmo.ui.screens.user.followers.FollowersListScreen
import one.srp.gensmo.ui.screens.user.following.FollowingListScreen
import one.srp.gensmo.ui.screens.user.history.UserHistoryScreen
import one.srp.gensmo.ui.screens.user.library.UserLibraryScreen
import one.srp.gensmo.ui.screens.user.login.LoginScreen
import one.srp.gensmo.ui.screens.user.login.UserLoginScreen
import one.srp.gensmo.ui.screens.user.preference.UserPreferenceScreen
import one.srp.gensmo.ui.screens.user.profile.UserProfileScreen
import one.srp.gensmo.ui.screens.user.profile.UserPublicProfileScreen
import one.srp.gensmo.ui.screens.user.saved.UserSavedScreen
import one.srp.gensmo.ui.screens.user.settings.UserSettingsScreen
import one.srp.gensmo.utils.integration.LaunchAction
import one.srp.gensmo.utils.integration.LaunchActionDetector
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import timber.log.Timber
import java.net.URLDecoder
import java.util.UUID

@Composable
fun NavGraph(
    modifier: Modifier = Modifier,
    startDestination: String = NavRoutes.Root.route,
    navController: NavHostController,
) {
    val navActions = remember(navController) { NavActions(navController) }
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    val sharedNavViewModel = remember(navController) { SharedNavViewModel() }
    val feedItem = sharedNavViewModel.feedItem.collectAsState().value

    val collageSearchViewModel: CollageSearchViewModel = hiltViewModel()
    val feedDetailViewModel: FeedDetailViewModel = hiltViewModel()
    val tryOnGenerateViewModel: TryOnGenerateViewModel = hiltViewModel()
    val sessionChatViewModel: SessionChatViewModel = hiltViewModel()

    val onboardEnable = remember {
        runBlocking {
            LaunchActionDetector.checkFirstLaunch(
                context,
                LaunchAction.USER_ONBOARDING
            ) && !UserDataStoreManager.isUserLoggedIn()
        }
    }

    fun createSession(message: ChatMessage, skipRoute: Boolean = false) {
        sessionChatViewModel.clear()
        sessionChatViewModel.setInitialState(message)

        val isLogIn = runBlocking { UserDataStoreManager.isUserLoggedIn() }
        val isFirstLaunch = runBlocking {
            LaunchActionDetector.checkFirstLaunch(context, LaunchAction.UNREGISTER_SESSION_CHAT)
        }
        if (isLogIn || isFirstLaunch) {
            coroutineScope.launch {
                LaunchActionDetector.recordFirstLaunch(
                    context, LaunchAction.UNREGISTER_SESSION_CHAT
                )
            }
            navActions.navigateToSessionChat(skipRoute)
        } else {
            navActions.navigateToSessionLogin()
        }
    }

    fun openTryOn(item: TryOnParams, skipRoute: Boolean = false) {
        val message = TryOnQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = TryOnQueryMessageWrapper(
                tryonQuery = item.copy(isAsync = true)
            )
        )
        createSession(message)
    }

    // 深链接现在由 AppsFlyer 统一处理，无需在这里处理

    NavHost(
        navController = navController, startDestination = startDestination, modifier = modifier
    ) {
        navigation(
            route = NavRoutes.Root.route,
            startDestination = if (onboardEnable) NavRoutes.Onboard.Welcome.route else NavRoutes.Session.View.CollageTask.createRoute("a27625c0-90a7-4e26-a445-b939c46a4672") // NavRoutes.Feed.Recommend.route
        ) {
            composable(route = NavRoutes.User.Profile.route) {
                UserProfileScreen(
                    navActions,
                    onClearFeed = { sharedNavViewModel.clear() },
                    onClearTryOn = { tryOnGenerateViewModel.clear() },
                    openTryOn = { openTryOn(it) },
                    createSession = { createSession(it) },
                )
            }

            composable(route = NavRoutes.User.Settings.route) {
                UserSettingsScreen(navActions)
            }

            composable(route = NavRoutes.User.Account.route) {
                UserAccountScreen(navActions)
            }

            composable(
                route = NavRoutes.User.Library.route,
                arguments = listOf(navArgument("selectedTab") {
                    type = NavType.IntType
                    defaultValue = 0
                })
            ) { backStackEntry ->
                val selectedTab = backStackEntry.arguments?.getInt("selectedTab") ?: 0
                UserLibraryScreen(
                    navActions = navActions,
                    tryOnGenerateViewModel = tryOnGenerateViewModel,
                    collageSearchViewModel = collageSearchViewModel,
                    initialSelectedTab = selectedTab,
                    sessionChatViewModel = sessionChatViewModel,
                    createSession = { createSession(it) },
                )
            }

            composable(route = NavRoutes.User.AccountEdit.route) {
                UserAccountEditScreen(navActions)
            }

            composable(route = NavRoutes.User.Login.route) {
                UserLoginScreen(navActions)
            }

            composable(route = NavRoutes.User.CheckIn.route) {
                UserCheckInScreen(navActions)
            }

            composable(NavRoutes.Feed.Recommend.route) {
                FeedHomeScreen(navActions, openDetail = {
                    sharedNavViewModel.updateFeedItem(it)
                    if (it.tryOnTaskId?.isNotBlank() == true) {
                        navActions.navigateToTryOnDetail(it.tryOnTaskId ?: "", "feed")
                    } else {
                        navActions.navigateToFeedDetail(it.moodboardId, "feed")
                    }
                }, createSession = { createSession(it) })
            }

            composable(
                route = NavRoutes.Feed.Detail.route,
                arguments = listOf(
                    navArgument("id") { type = NavType.StringType },
                    navArgument("refer") {
                        type = NavType.StringType
                        nullable = true
                        defaultValue = null
                    }),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://feed/comboDetail?feed_id={id}"
                })
            ) {
                val id = it.arguments?.getString("id")
                val refer = it.arguments?.getString("refer")?.takeIf { refer ->
                    refer.isNotEmpty()
                }
                FeedDetailScreen(
                    navActions,
                    feedItem = feedItem,
                    moodboardId = id,
                    feedType = refer,
                    openTryOn = { openTryOn(it, true) },
                    viewModel = feedDetailViewModel,
                    onUpdateRemixProduct = { collageSearchViewModel.updateRemixProduct(it) },
                    createSession = { createSession(it) },
                )
            }

            composable(
                route = NavRoutes.TryOn.Detail.route,
                arguments = listOf(
                    navArgument("id") { type = NavType.StringType },
                    navArgument("refer") {
                        type = NavType.StringType
                        nullable = true
                        defaultValue = null
                    }),
            ) {
                val id = it.arguments?.getString("id")
                val refer = it.arguments?.getString("refer")?.takeIf { refer ->
                    refer.isNotEmpty()
                }
                TryOnDetailScreen(
                    navActions,
                    feedItem = feedItem,
                    tryOnTaskId = id,
                    feedType = refer,
                    openTryOn = { openTryOn(it, true) },
                    viewModel = feedDetailViewModel,
                    onUpdateRemixProduct = { collageSearchViewModel.updateRemixProduct(it) },
                    createSession = { createSession(it) },
                )
            }

            composable(
                route = NavRoutes.Camera.route, arguments = listOf(
                    navArgument("mode") {
                        type = NavType.StringType
                        nullable = true
                        defaultValue = null
                    }), enterTransition = {
                    slideIntoContainer(
                        towards = AnimatedContentTransitionScope.SlideDirection.Up,
                        animationSpec = tween(200, easing = EaseIn)
                    )
                }, exitTransition = {
                    slideOutOfContainer(
                        towards = AnimatedContentTransitionScope.SlideDirection.Down,
                        animationSpec = tween(200, easing = EaseOut)
                    )
                }
            ) { backStackEntry ->
                val mode = backStackEntry.arguments?.getString("mode")?.takeIf { it.isNotEmpty() }
                    ?.let { encoded -> URLDecoder.decode(encoded, "UTF-8") }
                Timber.d("mode: $mode")

                CameraScreen(
                    navActions = navActions,
                    cameraModeTitle = mode,
                    onPhotoTaken = { uri, searchQuery ->
                        Timber.d("onPhotoTaken: $uri")
                        Timber.d("searchQuery: $searchQuery")
                        if (!mode.isNullOrEmpty()) {
                            createSession(
                                SearchQueryMessage(
                                    sessionId = "default",
                                    messageId = UUID.randomUUID().toString(),
                                    role = ChatMessageRole.User.value,
                                    visible = true,
                                    value = SearchQueryMessageWrapper(
                                        searchQuery = SearchParams(
                                            query = searchQuery,
                                            imageUrl = uri,
                                            debugLevel = 0,
                                            budget = "",
                                            isAsync = true,
                                            route = "",
                                            isPresetQuery = false,
                                            moodboardVersion = "v2",
                                        ),
                                    )
                                ), true
                            )
                        }
                    },
                    onSelectedBrand = { uri, q, brand, product ->
                        if (!mode.isNullOrEmpty()) {
                            createSession(
                                SearchQueryMessage(
                                    sessionId = "default",
                                    messageId = UUID.randomUUID().toString(),
                                    role = ChatMessageRole.User.value,
                                    visible = true,
                                    value = SearchQueryMessageWrapper(
                                        searchQuery = SearchParams(
                                            query = q,
                                            imageUrl = uri,
                                            debugLevel = 0,
                                            budget = "",
                                            isAsync = true,
                                            route = "",
                                            isPresetQuery = false,
                                            moodboardVersion = "v2",
                                            brand = brand,
                                            specifiedProduct = product,
                                        ),
                                    )
                                ), true
                            )
                        }
                    }
                )
            }

            composable(
                route = NavRoutes.Login.route, arguments = listOf(navArgument("query") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = ""
                }, navArgument("imageUrl") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                })
            ) { backStackEntry ->
                val query = backStackEntry.arguments?.getString("query")?.takeIf { queryStr ->
                    queryStr.isNotEmpty()
                }?.let { encoded ->
                    URLDecoder.decode(encoded, "UTF-8")
                } ?: ""
                val imageUrl = backStackEntry.arguments?.getString("imageUrl")?.takeIf { imgUrl ->
                    imgUrl.isNotEmpty()
                }?.let { encoded ->
                    URLDecoder.decode(encoded, "UTF-8")
                } ?: ""

                LoginScreen(
                    onLoginSuccess = {
                        Timber.d("onLoginSuccess in router")
                        if (query.isNotEmpty() || imageUrl.isNotEmpty()) {
                            navActions.navigateFromLoginToCollageSearch(
                                query = query, imageUrl = imageUrl
                            )
                        } else {
                            navActions.back()
                        }
                    },
                    navActions = navActions,
                )
            }

            composable(
                route = NavRoutes.Collage.Search.route, arguments = listOf(navArgument("query") {
                    type = NavType.StringType
                    nullable = false
                    defaultValue = ""
                }, navArgument("imageUrl") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }, navArgument("budget") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }, navArgument("stylesList") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                })
            ) {
                val query = it.arguments?.getString("query") ?: ""
                val imageUrl = it.arguments?.getString("imageUrl")
                val budget = it.arguments?.getString("budget")
                val stylesList = it.arguments?.getString("stylesList")

                CollageSearchScreen(
                    navActions,
                    query = query,
                    imageUrl = imageUrl,
                    budget = budget,
                    stylesList = stylesList,
                    collageSearchViewModel = collageSearchViewModel
                )
            }

            composable(
                route = NavRoutes.Collage.Task.route,
                arguments = listOf(navArgument("id") { type = NavType.StringType }),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://search?task_id={id}"
                })
            ) {
                val taskId = it.arguments?.getString("id")
                CollageTaskScreen(
                    navActions,
                    collageSearchViewModel = collageSearchViewModel,
                    taskId = taskId,
                    openTryOn = { it -> openTryOn(it) },
                    createSession = { it -> createSession(it) },
                )
            }

            composable(route = NavRoutes.History.route) {
                UserHistoryScreen(
                    navActions = navActions, collageSearchViewModel = collageSearchViewModel
                )
            }

            composable(
                route = NavRoutes.Preference.route,
                arguments = listOf(navArgument("source") { type = NavType.StringType })
            ) {
                val source = it.arguments?.getString("source") ?: "unknown"
                UserPreferenceScreen(
                    navActions = navActions,
                    collageSearchViewModel = collageSearchViewModel,
                    source = source,
                )
            }

            composable(route = NavRoutes.Saved.route) {
                UserSavedScreen(
                    navActions,
                    feedDetailViewModel = feedDetailViewModel,
                    sharedNavViewModel = sharedNavViewModel,
                    tryOnGenerateViewModel = tryOnGenerateViewModel
                )
            }

            composable(route = NavRoutes.TryOn.Generate.route) {
                val tryOnParams = remember { sharedNavViewModel.tryOnParams.value }
                LaunchedEffect(Unit) { sharedNavViewModel.clear() }
                TryOnGenerateScreen(navActions, tryOnParams, tryOnGenerateViewModel)
            }

            composable(
                route = NavRoutes.TryOn.Task.route,
                arguments = listOf(navArgument("id") { type = NavType.StringType }),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://tryon2?fetchTryonTaskId={id}"
                })
            ) {
                val taskId = it.arguments?.getString("id")
                TryOnTaskScreen(
                    navActions,
                    taskId = taskId,
                    viewModel = tryOnGenerateViewModel,
                    onUpdateRemixProduct = { collageSearchViewModel.updateRemixProduct(it) },
                    openTryOn = { openTryOn(it, true) },
                    createSession = { createSession(it) },
                )
            }

            composable(route = NavRoutes.Closet.route) {
                ClosetScreen(navActions = navActions)
            }

            composable(
                route = NavRoutes.TryOn.Create.routeWithSource("{source}"), arguments = listOf(
                    navArgument("source") {
                        type = NavType.StringType
                        defaultValue = NavRoutes.TryOn.Create.CREATE_ROUTE
                    })
            ) { backStackEntry ->
                TryOnCreateScreen(
                    navActions = navActions,
                    source = backStackEntry.arguments?.getString("source")
                        ?: NavRoutes.TryOn.Create.CREATE_ROUTE
                )
            }

            composable(
                route = NavRoutes.TryOn.Model.route, arguments = listOf(navArgument("setFace") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = ""
                }, navArgument("userPreference") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = ""
                })
            ) {
                val setFace =
                    it.arguments?.getString("setFace")?.takeIf { it.isNotEmpty() }?.let { encoded ->
                        URLDecoder.decode(encoded.replace("+", " "), "UTF-8")
                    } ?: ""
                val userPreference =
                    it.arguments?.getString("userPreference")?.takeIf { it.isNotEmpty() }
                        ?.let { encoded ->
                            URLDecoder.decode(encoded.replace("+", " "), "UTF-8")
                        } ?: ""
                TryOnModelScreen(
                    navActions = navActions,
                    setFace = setFace,
                    userPreference = userPreference,
                )
            }

            composable(
                route = NavRoutes.Session.Chat.route,
            ) {
                SessionChatScreen(
                    navActions = navActions,
                    sharedNavViewModel = sharedNavViewModel,
                    viewModel = sessionChatViewModel,
                )
            }

            composable(
                route = NavRoutes.Session.View.CollageTask.route,
                arguments = listOf(
                    navArgument("id") { type = NavType.StringType },
                    navArgument("index") { type = NavType.IntType }),
            ) {
                val taskId = it.arguments?.getString("id")
                val index = it.arguments?.getInt("index") ?: 0

                CollageTaskViewScreen(
                    navActions = navActions,
                    taskId = taskId,
                    initialIndex = index,
                    sharedNavViewModel = sharedNavViewModel,
                    viewModel = sessionChatViewModel,
                )
            }

            composable(
                route = NavRoutes.Session.View.TryOnTask.route,
                arguments = listOf(navArgument("id") { type = NavType.StringType }),
            ) {
                val taskId = it.arguments?.getString("id")

                TryOnTaskViewScreen(
                    navActions = navActions,
                    taskId = taskId,
                    sharedNavViewModel = sharedNavViewModel,
                    viewModel = sessionChatViewModel,
                )
            }

            composable(route = NavRoutes.Session.Login.route) {
                SessionLoginScreen(navActions = navActions)
            }

            composable(
                route = NavRoutes.Activity.route,
                arguments = listOf(navArgument("url") {
                    type = NavType.StringType
                    nullable = false
                }),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://webview?url={url}"
                })
            ) {
                val activityUrl = it.arguments?.getString("url")?.let { encoded ->
                    URLDecoder.decode(encoded, "UTF-8")
                } ?: ""

                one.srp.gensmo.ui.screens.activity.ActivityScreen(
                    navActions = navActions,
                    activityUrl = activityUrl
                )
            }

            composable(
                route = NavRoutes.ProductWeb.route,
                arguments = listOf(navArgument("url") {
                    type = NavType.StringType
                    nullable = false
                }),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://web/product?url={url}"
                })
            ) {
                val url = it.arguments?.getString("url")?.let { encoded ->
                    URLDecoder.decode(encoded, "UTF-8")
                } ?: ""

                one.srp.gensmo.ui.screens.web.ProductWebScreen(
                    navActions = navActions,
                    url = url
                )
            }

            composable(route = NavRoutes.Onboard.Welcome.route) {
                OnboardWelcomeScreen(navActions)
            }

            composable(route = NavRoutes.Onboard.Login.route) {
                one.srp.gensmo.ui.screens.onboard.login.OnboardLoginScreen(navActions)
            }

            composable(route = NavRoutes.Onboard.Pick.route) {
                OnboardPickScreen(navActions)
            }

            composable(
                route = NavRoutes.Onboard.Result.route,
                arguments = listOf(navArgument("tags") { 
                    type = NavType.StringType 
                    defaultValue = ""
                })
            ) { backStackEntry ->
                val tagsParam = backStackEntry.arguments?.getString("tags") ?: ""
                val tags = if (tagsParam.isNotEmpty()) {
                    java.net.URLDecoder.decode(tagsParam, "UTF-8").split(",")
                } else {
                    emptyList()
                }
                OnboardResultScreen(navActions, tags)
            }

            composable(
                route = NavRoutes.User.PublicProfile.route,
                arguments = listOf(navArgument("userId") { type = NavType.StringType }),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://user/profile?userId={userId}"
                })
            ) { backStackEntry ->
                val userId = backStackEntry.arguments?.getString("userId")
                UserPublicProfileScreen(
                    userId = userId,
                    onClearFeed = { sharedNavViewModel.clear() },
                    navActions = navActions
                )
            }

            composable(
                route = NavRoutes.User.Followers.route,
                arguments = listOf(navArgument("userId") { type = NavType.StringType })
            ) { backStackEntry ->
                val userId = backStackEntry.arguments?.getString("userId") ?: ""
                FollowersListScreen(
                    userId = userId,
                    navActions = navActions
                )
            }

            composable(
                route = NavRoutes.User.Following.route,
                arguments = listOf(navArgument("userId") { type = NavType.StringType })
            ) { backStackEntry ->
                val userId = backStackEntry.arguments?.getString("userId") ?: ""
                FollowingListScreen(
                    userId = userId,
                    navActions = navActions
                )
            }

            composable(
                route = NavRoutes.Detail.PostEditor.route,
                arguments = listOf(
                    navArgument("id") {
                        type = NavType.StringType
                        nullable = true
                        defaultValue = null
                    },
                    navArgument("type") {
                        type = NavType.StringType
                    }
                )
            ) { backStackEntry ->
                val id = backStackEntry.arguments?.getString("id")?.takeIf { it.isNotEmpty() }
                val type = backStackEntry.arguments?.getString("type") ?: "collage"
                PostEditorScreen(
                    id = id,
                    type = type,
                    navActions = navActions
                )
            }

            composable(
                route = NavRoutes.Detail.Hashtag.route,
                arguments = listOf(
                    navArgument("hashtag") {
                        type = NavType.StringType
                    }
                ),
                deepLinks = listOf(navDeepLink {
                    uriPattern = "gensmo://topic/detail?topic={hashtag}"
                })
            ) { backStackEntry ->
                val hashtag = backStackEntry.arguments?.getString("hashtag")?.let { encoded ->
                    URLDecoder.decode(encoded, "UTF-8")
                } ?: ""

                HashtagScreen(
                    hashtag = hashtag,
                    navActions = navActions
                )
            }

            composable(route = NavRoutes.Notification.route) {
                NotificationScreen(navActions = navActions)
            }
        }
    }
}
