package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import one.srp.core.network.model.FeedItem
import one.srp.gensmo.R
import one.srp.gensmo.ui.screens.recommend._components.FeedCard
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel
import kotlin.math.max

@Composable
fun DetailSimilar(
    modifier: Modifier = Modifier,
    itemId: String,
    onItemClick: (FeedItem) -> Unit = {},
    onItemView: (FeedItem) -> Unit = {},
    onLikeClick: (FeedItem, Boolean) -> Unit = { _, _ -> },
    onLoadMore: (List<FeedItem>) -> Unit = {},
    feedDetailViewModel: FeedDetailViewModel,
) {
    val similarPagingItems = feedDetailViewModel.getSimilarPagingFlow(itemId).collectAsLazyPagingItems()

    val windowInfo = LocalWindowInfo.current
    val density = LocalDensity.current
    val windowHeight = with(density) { windowInfo.containerSize.height.toDp() }
    val topBarHeight = 64.dp // TopBar 的默认高度
    val gridHeight = windowHeight - topBarHeight
    
    val lazyGridState = rememberLazyStaggeredGridState()

    // 跟踪之前的数据数量和加载状态
    var previousItemCount by remember { mutableIntStateOf(0) }
    var previousRefreshState by remember { mutableStateOf<LoadState?>(null) }
    var previousAppendState by remember { mutableStateOf<LoadState?>(null) }
    var hasReportedInitialLoad by remember { mutableStateOf(false) }
    
    // 跟踪已曝光的item，避免重复曝光
    val viewedItems = remember { mutableSetOf<String>() }



    // 监听分页加载状态，上报列表展现事件
    LaunchedEffect(similarPagingItems.loadState) {
        val refreshState = similarPagingItems.loadState.refresh
        val appendState = similarPagingItems.loadState.append
        val currentItems = similarPagingItems.itemSnapshotList.items

        // 第一次加载完成后触发 - 确保只上报一次初始加载
        if (refreshState is LoadState.NotLoading &&
            !hasReportedInitialLoad &&
            currentItems.isNotEmpty()
        ) {
            onLoadMore(currentItems)
            previousItemCount = currentItems.size
            hasReportedInitialLoad = true
        }

        // 后续加载完成后触发 - 只有当状态真正从Loading变为NotLoading时才触发
        if (appendState is LoadState.NotLoading &&
            previousAppendState is LoadState.Loading &&
            currentItems.size > previousItemCount
        ) {
            // 只传入新加载的数据
            val newItems = currentItems.drop(previousItemCount)
            onLoadMore(newItems)
            previousItemCount = currentItems.size
        }

        // 更新状态
        previousRefreshState = refreshState
        previousAppendState = appendState
    }

    // 移除全局可见性检测，改为在item级别检测



    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 16.dp)
    ) {
        // 只有在有数据或正在加载时才显示标题
        if (similarPagingItems.itemCount > 0 || similarPagingItems.loadState.refresh is LoadState.Loading) {
            Text(
                text = stringResource(R.string.text_more_like_this),
                style = AppThemeTextStyle.Heading20D,
                modifier = Modifier.padding(bottom = 12.dp)
            )
        }

        if (similarPagingItems.itemCount == 0 && similarPagingItems.loadState.refresh is LoadState.Loading) {
            // 初始加载状态
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 20.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (similarPagingItems.itemCount == 0 && similarPagingItems.loadState.refresh is LoadState.Error) {
            
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 20.dp)
                    .clickable {
                        similarPagingItems.refresh()
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Failed to load, tap to retry",
                    style = AppThemeTextStyle.Body14H,
                    color = Color.Gray
                )
            }
        } else {
            // 显示列表
            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(2),
                state = lazyGridState,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(gridHeight),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalItemSpacing = 8.dp
            ) {
                items(similarPagingItems.itemCount) { index ->
                    similarPagingItems[index]?.let { item ->
                        var liked by remember(item.moodboardId) { mutableStateOf(item.isLiked) }
                        var hasTriggeredView by remember(item.moodboardId) { mutableStateOf(false) }
                        
                        FeedCard(
                            modifier = Modifier
                                .fillMaxWidth()
                                .onGloballyPositioned { coordinates ->
                                    if (!hasTriggeredView && !viewedItems.contains(item.moodboardId)) {
                                        val rect: Rect = coordinates.boundsInWindow()
                                        // 判断是否和屏幕有交集
                                        val isVisible = rect.bottom > 0f && rect.top < windowInfo.containerSize.height
                                        if (isVisible) {
                                            hasTriggeredView = true
                                            viewedItems.add(item.moodboardId)
                                            onItemView(item)
                                        }
                                    }
                                },
                            item = item,
                            onItemClick = { onItemClick(item) },
                            liked = liked,
                            onLikeClick = { 
                                val newLike = !liked
                                liked = newLike
                                item.isLiked = newLike
                                item.likedCount = (item.likedCount ?: 0).let { count ->
                                    if (newLike) count + 1 else max(count - 1, 0)
                                }
                                onLikeClick(item, newLike)
                            }
                        )
                    }
                }
                
                // 加载更多状态
                when (val appendState = similarPagingItems.loadState.append) {
                    is LoadState.Loading -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }
                    }
                    is LoadState.Error -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp)
                                    .clickable {
                                        similarPagingItems.retry()
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "Failed to load more, tap to retry",
                                    style = AppThemeTextStyle.Body14H,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                    is LoadState.NotLoading -> {
                        if (appendState.endOfPaginationReached && similarPagingItems.itemCount > 0) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "No more content",
                                        style = AppThemeTextStyle.Body14H,
                                        color = Color.Gray
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
