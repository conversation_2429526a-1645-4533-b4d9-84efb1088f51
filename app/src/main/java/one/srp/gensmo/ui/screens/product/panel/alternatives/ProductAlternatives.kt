package one.srp.gensmo.ui.screens.product.panel.alternatives

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.ui.screens.product.panel._components.ProductAction
import one.srp.gensmo.ui.screens.product.panel._components.ProductAlter
import one.srp.gensmo.ui.screens.product.panel._components.ProductDetail
import one.srp.gensmo.ui.screens.product.panel._components.ProductDrawer
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import timber.log.Timber

@Composable
fun ProductAlternativesDrawer(
    open: Boolean = false,
    onClose: () -> Unit,
    onTryOn: (ProductItem) -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
    selected: ProductItem? = null,
    products: List<ProductItem>? = null,
    onReplace: (ProductItem) -> Unit = {},
    replaceable: Boolean = true,
    refer: EventRefer = EventRefer.ProductDetail,
    onOpenProductWeb: (String) -> Unit,
) {
    var selectedItem by remember { mutableStateOf<ProductItem?>(null) }
    fun onChange(item: ProductItem) {
        selectedItem = item
    }
    LaunchedEffect(selected) {
        selectedItem = selected
    }

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)

    LaunchedEffect(open) {
        if (open) {
            metric(
                SelectItem(
                    itemListName = EventItemListName.Screen,
                    method = EventMethod.PageView
                )
            )
        }
    }

    LaunchedEffect(selectedItem) {
        if (open) {
            // 埋点：商品详情页默认选项展示
            metric(
                ViewItem(
                    refer = refer,
                    itemListName = EventItemListName.Default,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.Product,
                            itemId = selectedItem?.globalId,
                            itemName = selectedItem?.title
                        )
                    )
                )
            )
        }
    }
    val linkValid = selectedItem?.link?.let {
        val uri = it.toUri()
        uri.scheme != null && uri.host != null
    } == true

    fun openShopLink() {
        selectedItem?.let { item ->
            if (linkValid) {
                try {
                    metric(
                        SelectItem(
                            method = EventMethod.Click,
                            itemListName = EventItemListName.ProductCard,
                            actionType = EventActionType.ProductExternalJump,
                            items = listOf(
                                EventItem(
                                    itemId = item.id,
                                    itemName = item.title,
                                    itemCategory = EventItemCategory.Product,
                                )
                            )
                        )
                    )
                    MetricData.logEventAF("af_product_external_jump")

                    onOpenProductWeb(item.link ?: "/")
                } catch (err: Exception) {
                    Timber.e(err)
                }
            }
        }
    }

    val coroutineScope = rememberCoroutineScope()
    var saved by remember(selectedItem) { mutableStateOf(selectedItem?.isFavorited == true) }
    fun saveProduct() {
        coroutineScope.launch {
            selectedItem?.globalId?.let {
                val newSaved = !saved
                saved = newSaved
                selectedItem?.isFavorited = newSaved

                try {
                    if (newSaved) {
                        UserService.api.postSaved(it, CollectionType.Product)
                    } else {
                        UserService.api.deleteSaved(it, CollectionType.Product)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    saved = !newSaved
                    selectedItem?.isFavorited = !newSaved
                }
            }
        }
    }

    ProductDrawer(open = open, onClose = onClose) {
        Box {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                selectedItem?.let {
                    ProductDetail(
                        item = it,
                        replaceable = selectedItem != selected && replaceable,
                        saved = saved,
                        onSave = { saveProduct() },
                        onOutJump = { openShopLink() },
                        onReplace = { onReplace(it) },
                        onTryOn = { onTryOn(it) },
                        tryOnEnabled = selectedItem?.tags?.cateTag in listOf(
                            "coat",
                            "top",
                            "bottom",
                            "full-body",
                            "ootd"
                        ),
                        refer = refer
                    )
                }

                products?.let {
                    if (products.isNotEmpty()) {
                        ProductAlter(
                            selected = selectedItem,
                            items = products,
                            onChange = { onChange(it) },
                            refer = refer
                        )
                    }
                }

                selectedItem?.let { nonNullItem ->
                    ProductAction(
                        item = nonNullItem,
                        onRemix = { onRemix(nonNullItem) },
                        onShopNow = { openShopLink() },
                        shopNowEnabled = linkValid,
                        refer = refer
                    )
                }
            }

            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(12.dp)
            ) {
                IconButton(
                    colors = IconButtonDefaults.iconButtonColors(
                        Color.Transparent
                    ),
                    onClick = { onClose() }
                ) {
                    Icon(Icons.Default.Close, null)
                }
            }
        }
    }
}
