package one.srp.gensmo.ui.screens.session.chat._components.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.BrandItem
import one.srp.core.network.model.BrandProductItem
import one.srp.core.network.model.ChatMessage
import one.srp.core.network.model.ChatMessageRole
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.EditorTag
import one.srp.core.network.model.EditorTagType
import one.srp.core.network.model.HomePageInfoStyle
import one.srp.core.network.model.MoodboardContent
import one.srp.core.network.model.MoodboardEntity
import one.srp.core.network.model.MoodboardTryOnGarmentItem
import one.srp.core.network.model.SearchInspoItem
import one.srp.core.network.model.SearchInspoRes
import one.srp.core.network.model.SearchLoadingMessage
import one.srp.core.network.model.SearchParams
import one.srp.core.network.model.SearchQueryMessage
import one.srp.core.network.model.SearchQueryMessageWrapper
import one.srp.core.network.model.SearchResMessage
import one.srp.core.network.model.TryOnParams
import one.srp.core.network.model.TryOnQueryMessage
import one.srp.core.network.model.TryOnQueryMessageWrapper
import one.srp.core.network.utils.JSON
import one.srp.gensmo.R
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.collage.MoodboardRenderer
import one.srp.gensmo.ui.components.collage.MoodboardTryOnPanel
import one.srp.gensmo.ui.components.collage.TryOnPanelDrawer
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.preview.ZoomDialog
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.session._components.MessageBox
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.screens.session.chat._components.SaveIconButton
import one.srp.gensmo.ui.screens.session.chat._components.SessionNavigation
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.render.renderComposeToBitmap
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import one.srp.gensmo.viewmodel.search.SearchExtensionViewModel
import timber.log.Timber
import java.util.UUID


@Composable
fun SearchQueryComponent(
    item: SearchQueryMessage,
    onMessage: (ChatMessage) -> Unit = {},
) {
    var queryOpen by remember { mutableStateOf(false) }
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Channel)

    fun onEdit() {
        queryOpen = true

        metric(
            SelectItem(
                itemListName = EventItemListName.EditYourInputBtn,
                method = EventMethod.Click,
                actionType = EventActionType.InitializeSearch,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Channel,
                        itemId = item.sessionId,
                    )
                ),
            )
        )
    }

    fun editQuery(param: SearchParams) {
        queryOpen = false
        onMessage(
            SearchQueryMessage(
                item.sessionId,
                UUID.randomUUID().toString(),
                ChatMessageRole.User.value,
                true,
                SearchQueryMessageWrapper(
                    searchQuery = param
                )
            )
        )
    }

    Column(verticalArrangement = Arrangement.spacedBy(16.dp), horizontalAlignment = Alignment.End) {
        if (item.value.searchQuery.imageUrl.isNotEmpty()) {
            MessageBox(shape = MaterialTheme.shapes.small) {
                ZoomDialog(modifier = Modifier.size(60.dp)) {
                    AsyncImage(
                        item.value.searchQuery.imageUrl, null, modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }

        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painterResource(R.drawable.icon_edit),
                null,
                modifier = Modifier
                    .size(18.dp)
                    .clickable { onEdit() }
            )

            MessageBox {
                Column(
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = item.value.searchQuery.query,
                        style = AppThemeTextStyle.Body16H
                    )

                    item.value.searchQuery.inspoLabel?.let {
                        LabelContainer(items = it)
                    }
                }
            }
        }

        QueryDrawer(
            open = queryOpen,
            onClose = { queryOpen = false },
            initialInput = item.value.searchQuery,
            onCommit = { editQuery(it) }
        )
    }
}

private val mockHintList = listOf(
    "Firing up your creative board…",
    "Warming up the AI engine…",
    "Getting things started…",
    "Revving the design process…",
    "Setting the stage for inspiration…"
)

@Composable
fun SearchLoadingComponent(item: SearchLoadingMessage? = null) {
    val hintText =
        item?.value?.searchLoading?.displayText?.ifBlank { null } ?: mockHintList.random()

    Column(
        modifier = Modifier.padding(start = 16.dp, top = 8.dp, bottom = 8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BaseLoading(modifier = Modifier.size(12.dp), strokeWidth = 1.5.dp)
            Text(hintText, style = AppThemeTextStyle.Body16H)
        }

        Box(modifier = Modifier.fillMaxWidth()) {
            Image(
                painterResource(R.drawable.image_session_collage_loading_bg),
                null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
fun SearchResComponent(
    item: SearchResMessage,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val collageSearchViewModel = hiltViewModel<CollageSearchViewModel>()
    val sharedNavViewModel = hiltViewModel<SharedNavViewModel>()

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Channel)
    val metricItems = listOf(
        EventItem(
            itemCategory = EventItemCategory.Channel,
            itemId = item.sessionId,
        ), EventItem(
            itemCategory = EventItemCategory.CollageGenTask,
            itemId = item.value.searchRes.taskId,
            itemName = item.value.searchRes.reasoning,
        )
    )

    val result = item.value.searchRes
    val inspoRes = item.value.inspo

    val moodboardList = remember(result.moodboards) {
        result.moodboards?.mapNotNull {
            it.copy(
                parsedContent = it.parsedContent
                    ?: JSON.decodeFromString<MoodboardContent>(it.content)
            )
        } ?: emptyList()
    }

    // 渲染和预览函数
    suspend fun postMoodboard(moodboard: MoodboardEntity) {
        try {
            moodboard.parsedContent?.let { content ->
                val bitmap = renderComposeToBitmap(
                    context,
                    content.width.toInt(),
                    (content.height ?: (content.width / 0.7)).toInt()
                ) {
                    MoodboardRenderer(
                        modifier = Modifier.fillMaxWidth(), item = content, offscreen = true
                    )
                }
                collageSearchViewModel.updatePreviewFromBitmap(
                    moodboard, result, bitmap
                )
            }
        } catch (e: Exception) {
            Timber.w(e)
        }
    }

    // 确保初始状态也被缓存
    LaunchedEffect(result.taskId) {
        val cached = sharedNavViewModel.getSearchItem(result.taskId)
        if (cached == null) {
            sharedNavViewModel.putSearchItem(result.taskId, result)
        }
    }

    fun onItemClick(index: Int = 0) {
        metric(
            SelectItem(
                itemListName = EventItemListName.CollageList,
                method = EventMethod.Click,
                actionType = EventActionType.EnterCollageGen,
                items = metricItems + listOf(
                    EventItem(
                        itemCategory = EventItemCategory.GeneralCollage,
                        itemId = moodboardList.getOrNull(index)?.id,
                        itemName = item.value.searchRes.reasoning,
                    )
                ),
            )
        )

        onNavigate(SessionNavigation.CollageTask, item, index)
    }

    fun onInspoClick(inspo: SearchInspoItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.InspoListInspo,
                method = EventMethod.Click,
                actionType = EventActionType.CollageGen,
                items = metricItems + listOf(
                    EventItem(
                        itemCategory = EventItemCategory.RecoInspo,
                        itemName = inspo.showQuery,
                    )
                ),
            )
        )

        onMessage(
            SearchQueryMessage(
                item.sessionId,
                UUID.randomUUID().toString(),
                ChatMessageRole.User.value,
                true,
                SearchQueryMessageWrapper(
                    searchQuery = item.value.searchQuery.copy(
                        inspoLabel =
                            listOf(inspo.showQuery) + (item.value.searchQuery.inspoLabel
                                ?: emptyList())
                    )
                )
            )
        )
    }

    var tryOnPanelOpen by remember { mutableStateOf(false) }
    var selectedMoodboard by remember { mutableStateOf<MoodboardEntity?>(null) }

    fun openTryOnPanel(moodboard: MoodboardEntity) {
        selectedMoodboard = moodboard
        tryOnPanelOpen = true
    }

    fun handleTryOnSelection(selectedItems: List<MoodboardTryOnGarmentItem>) {
        tryOnPanelOpen = false
        selectedMoodboard?.let { moodboard ->
            coroutineScope.launch {
                val model = UserDataStoreManager.getModelInfo()
                val selectedProducts = selectedItems.mapNotNull { selected ->
                    moodboard.products.find { product -> product.globalId == selected.itemId }
                }

                val imageType = selectedItems.filter { it.itemType == "user_image" }

                val params = TryOnParams(
                    modelId = model.second ?: "",
                    moodboardId = moodboard.id,
                    taskId = item.value.searchRes.taskId,
                    internalImageList = selectedItems.mapNotNull { p -> p.imageUrl },
                    products = selectedProducts,
                    userImage = imageType.firstOrNull()?.imageUrl,
                    userImageTag = imageType.firstOrNull()?.garmentType,
                    isAsync = true,
                )

                // 创建并发送TryOnQueryMessage
                val tryOnMessage = TryOnQueryMessage(
                    sessionId = item.sessionId,
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = TryOnQueryMessageWrapper(
                        tryonQuery = params
                    )
                )

                onMessage(tryOnMessage)
            }
        }
    }

    val normalizedStatus = result.status?.trim()?.lowercase()
    val isCompleted = normalizedStatus == "completed" || moodboardList.isNotEmpty()
    
    if (isCompleted) {
        LaunchedEffect(Unit) {
            metric(
                ViewItemList(
                    itemListName = EventItemListName.CollageList,
                    items = metricItems + (result.moodboards?.mapIndexed { index, it ->
                        EventItem(
                            itemId = it.id,
                            itemCategory = EventItemCategory.GeneralCollage,
                            itemName = result.reasoning,
                            index = index,
                        )
                    } ?: emptyList()),
                ))
        }

        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            result.reasoning?.let {
                Column(modifier = Modifier.padding(horizontal = 16.dp)) {
                    Text(it, style = AppThemeTextStyle.Body16H)
                }
            }



            CollageView(items = moodboardList, onItemClick = { onItemClick(it) }, onTryOnClick = {
                openTryOnPanel(moodboardList[it])
                metric(
                    SelectItem(
                        itemListName = EventItemListName.TryOnBtn,
                        method = EventMethod.Click,
                        actionType = EventActionType.TryOnSelect,
                        items = metricItems + listOf(
                            EventItem(
                                itemCategory = EventItemCategory.GeneralCollage,
                                itemId = moodboardList[it].id,
                                itemName = item.value.searchRes.reasoning,
                            )
                        ),
                    )
                )
            }, onItemSave = { moodboard ->
                // 这里可以添加其他保存后的逻辑，metrics已经在onPreSave中处理了
            }, onPreSave = { moodboard, newState ->
                // 在API调用前发送metrics统计和执行postMoodboard逻辑
                metric(
                    SelectItem(
                        itemListName = EventItemListName.SaveBtn,
                        method = EventMethod.Click,
                        actionType = if (newState) EventActionType.Save else EventActionType.Unsave,
                        items = metricItems + listOf(
                            EventItem(
                                itemCategory = EventItemCategory.GeneralCollage,
                                itemId = moodboard.id,
                                itemName = item.value.searchRes.reasoning,
                            )
                        ),
                    )
                )

                // 只在保存（而非取消保存）时才执行postMoodboard逻辑
                if (newState) {
                    coroutineScope.launch {
                        postMoodboard(moodboard)
                    }
                }
            }, onStateSync = { moodboard ->
                // 直接内联状态同步逻辑，避免函数引用问题
                val cachedSearchItem = sharedNavViewModel.getSearchItem(result.taskId)
                if (cachedSearchItem != null) {
                    cachedSearchItem.moodboards?.find { it.id == moodboard.id }
                        ?.let { cachedMoodboard ->
                            cachedMoodboard.isFavorited = moodboard.isFavorited
                        }
                    sharedNavViewModel.putSearchItem(result.taskId, cachedSearchItem)
                } else {
                    sharedNavViewModel.putSearchItem(result.taskId, result)
                }
            })

            InspoView(item = inspoRes, onItemClick = { onInspoClick(it) }, onMount = {
                metric(
                    ViewItemList(
                        itemListName = EventItemListName.InspoList,
                        items = metricItems + inspoRes.queryExtensionList.mapIndexed { index, it ->
                            EventItem(
                                itemCategory = EventItemCategory.RecoInspo,
                                itemName = it.showQuery,
                                index = index,
                            )
                        },
                    )
                )
            })

            // Try On面板
            selectedMoodboard?.allTryOnItems?.let { tryOnItems ->
                TryOnPanelDrawer(open = tryOnPanelOpen, onClose = { tryOnPanelOpen = false }) {
                    MoodboardTryOnPanel(
                        items = tryOnItems,
                        onClose = { tryOnPanelOpen = false },
                        onTryOn = { selectedItems -> handleTryOnSelection(selectedItems) }
                    )
                }
            }
        }
    } else {
        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            Row(modifier = Modifier.padding(horizontal = 16.dp)) {
                Text(
                    stringResource(R.string.text_well_that_was_unexpected_one_more_time),
                    style = AppThemeTextStyle.Body16H
                )
            }
        }
    }
}

@Composable
fun CollageView(
    items: List<MoodboardEntity>,
    onItemClick: (Int) -> Unit = {},
    onTryOnClick: (Int) -> Unit = {},
    onItemSave: ((MoodboardEntity) -> Unit)? = null,
    onPreSave: ((MoodboardEntity, Boolean) -> Unit)? = null,
    onStateSync: ((MoodboardEntity) -> Unit)? = null,
) {
    Box {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 6.dp)
        ) {
            items(items.size) { index ->
                items[index].let { moodboard ->
                    moodboard.parsedContent?.let { content ->
                        Box(
                            modifier = Modifier
                                .height(300.dp)
                                .clipToBounds()
                                .aspectRatio(content.height?.let { h -> content.width / h }
                                    ?: run { 3 / 4f })
                                .clickable { onItemClick(index) }
                        ) {
                            MoodboardRenderer(modifier = Modifier.fillMaxWidth(), item = content)

                            // Save按钮 - 右上角
                            SaveActionContainer(
                                type = CollectionType.Collage,
                                id = moodboard.id,
                                initialState = moodboard.isFavorited == true,
                                onPreAction = { newState ->
                                    onPreSave?.invoke(moodboard, newState)
                                },
                                onAction = {
                                    moodboard.isFavorited = it
                                    onStateSync?.invoke(moodboard)
                                    onItemSave?.invoke(moodboard)
                                }
                            ) { state, onClick ->
                                SaveIconButton(
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(8.dp),
                                    enable = state,
                                    onClick = onClick
                                )
                            }

                            // Try On 按钮 - 覆盖在moodboard上方
                            if (moodboard.allTryOnItems != null) {
                                Button(
                                    onClick = {
                                        onTryOnClick(index)
                                    },
                                    modifier = Modifier
                                        .height(48.dp)
                                        .align(Alignment.BottomCenter)
                                        .padding(bottom = 16.dp),
                                    shape = MaterialTheme.shapes.large,
                                    colors = ButtonDefaults.outlinedButtonColors(
                                        containerColor = AppThemeColors.White,
                                        contentColor = AppThemeColors.Black
                                    ),
                                    contentPadding = PaddingValues(
                                        vertical = 2.dp,
                                        horizontal = 12.dp
                                    ),
                                    elevation = ButtonDefaults.buttonElevation(1.dp),
                                ) {
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Image(painterResource(R.drawable.icon_try_on_star), null)

                                        Text(
                                            text = stringResource(R.string.text_try_on),
                                            style = AppThemeTextStyle.Body16H,
                                            color = Color.Black
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
private fun InspoView(
    modifier: Modifier = Modifier,
    item: SearchInspoRes,
    onItemClick: (SearchInspoItem) -> Unit = {},
    onMount: () -> Unit = {},
) {
    val inspoTitle = item.inspoTitle
    val inspoList = item.queryExtensionList

    LaunchedEffect(Unit) {
        onMount()
    }

    Column(verticalArrangement = Arrangement.spacedBy(8.dp), modifier = modifier) {
        Column(modifier = Modifier.padding(horizontal = 16.dp)) {
            Text(
                inspoTitle ?: "Want to add a color, vibe or detail?\uD83D\uDC47",
                style = AppThemeTextStyle.Body16H,
            )
        }

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(6.dp),
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
        ) {
            items(inspoList.size) { index ->
                inspoList[index].let { inspo ->
                    Box(
                        modifier = Modifier
                            .padding(vertical = 2.dp)
                            .clickable { onItemClick(inspo) }
                    ) {
                        Box(modifier = Modifier.clip(MaterialTheme.shapes.large)) {
                            AsyncImage(
                                inspo.imageUrl,
                                null,
                                modifier = Modifier.height(60.dp),
                                contentScale = ContentScale.FillBounds
                            )

                            Box(
                                modifier = Modifier
                                    .matchParentSize()
                                    .background(AppThemeColors.White.copy(0.4f))
                            ) {
                                Column(
                                    modifier = Modifier
                                        .align(Alignment.Center)
                                        .padding(horizontal = 8.dp, vertical = 4.dp)
                                ) {
                                    Text(
                                        inspo.showQuery,
                                        style = AppThemeTextStyle.Body12H,
                                        maxLines = 2,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Center,
                                    )
                                }
                            }
                        }

                        Card(
                            modifier = Modifier
                                .size(12.dp)
                                .align(Alignment.TopEnd)
                                .offset(x = 6.dp, y = (-6).dp)
                                .clickable { onItemClick(inspo) },
                            shape = CircleShape,
                            colors = CardDefaults.cardColors(
                                containerColor = AppThemeColors.Gray650,
                                contentColor = AppThemeColors.White,
                            ),
                        ) {
                            Icon(Icons.Default.Add, null, tint = AppThemeColors.White)
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun QueryDrawer(
    open: Boolean,
    onClose: () -> Unit = {},
    initialInput: SearchParams,
    onCommit: (SearchParams) -> Unit = {},
) {
    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(initialInput.imageUrl) }
    var capturedSearchText by remember { mutableStateOf<String>(initialInput.query) }
    var capturedBrandItem by remember { mutableStateOf<BrandItem?>(null) }
    var capturedBrandProduct by remember { mutableStateOf<BrandProductItem?>(null) }

    val viewModel: SearchExtensionViewModel = hiltViewModel()

    LaunchedEffect(initialInput) {
        if (initialInput.budget.isNotEmpty()) {
            viewModel.selectBudget(
                EditorTag(
                    icon = when (initialInput.budget) {
                        "2" -> "$$"
                        "3" -> "$$$"
                        else -> "$"
                    },
                    text = "On a budget",
                    type = EditorTagType.BUDGET,
                    value = initialInput.budget
                )
            )
        }

        initialInput.inspoLabel?.map {
            viewModel.toggleStyle(HomePageInfoStyle(title = it, image = null))
        }
    }

    fun commit(
        q: String,
        img: String,
        stylesList: String?,
        budget: String?,
        brand: BrandItem? = null,
        brandProduct: BrandProductItem? = null,
        isProModeEnabled: Boolean = false,
    ) {
        if (q.trim().isNotEmpty()) {
            onCommit(
                SearchParams(
                    query = q,
                    imageUrl = img,
                    inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                        ?.filter { it.isNotEmpty() }
                        ?: emptyList()),
                    budget = budget ?: "",
                    debugLevel = 0,
                    isAsync = true,
                    route = "",
                    isPresetQuery = false,
                    moodboardVersion = "v2",
                    brand = brand?.brand,
                    specifiedProduct = brandProduct?.product,
                    isAgent = isProModeEnabled,
                ))
            onClose()
        }
    }

    SearchPanel(
        isVisible = open && !showCameraPicker,
        onDismiss = {
            onClose()
            capturedImageUri = null
            capturedSearchText = ""
        },
        viewModel = viewModel,
        imageUrl = capturedImageUri,
        searchQueryText = capturedSearchText,
        onCameraRequest = { showCameraPicker = true },
        onSearch = { q, img, stylesList, budget, isProModeEnabled ->
            commit(
                q,
                img,
                stylesList,
                budget,
                capturedBrandItem,
                capturedBrandProduct,
                isProModeEnabled
            )
        },
        onUpdateQuery = { query -> capturedSearchText = query },
        navActions = NavActions(),
        placeholderText = stringResource(R.string.text_describe_an_occasion_vibe_or_something_you_want_help_with),
        headerVisible = false,
    )

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    if (showCameraPicker) {
        ModalBottomSheet(
            sheetState = sheetState,
            onDismissRequest = { showCameraPicker = false },
            dragHandle = null,
            shape = RectangleShape,
        ) {
            CameraPicker(
                onPhotoTaken = { uri, searchQueryWords ->
                    capturedImageUri = uri.toString()
                    capturedSearchText += searchQueryWords
                    showCameraPicker = false
                },
                onMiss = {
                    showCameraPicker = false
                    capturedImageUri = null
                },
                onSelectBrand = { q, b, bp ->
//                    capturedBrandItem = b
//                    capturedBrandProduct = bp
//                    capturedImageUri = bp.showImage
//                    capturedSearchText += q
                    showCameraPicker = false
                    commit(q, bp.showImage?:"", null, null, b, bp, false)
                }
            )
        }
    }
}

@Composable
private fun LabelContainer(modifier: Modifier = Modifier, items: List<String>) {
    LazyRow(horizontalArrangement = Arrangement.spacedBy(8.dp), modifier = modifier) {
        items(items.size) { index ->
            val inspo = items[index]
            Card(
                modifier = Modifier
                    .height(24.dp)
                    .widthIn(max = 160.dp),
                shape = MaterialTheme.shapes.large,
                colors = CardDefaults.cardColors(
                    MaterialTheme.colorScheme.surfaceVariant,
                    MaterialTheme.colorScheme.onSurface,
                ),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxHeight()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        inspo,
                        style = AppThemeTextStyle.Body12H,
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    )
                }
            }
        }
    }
}


