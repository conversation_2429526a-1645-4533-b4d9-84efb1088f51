package one.srp.gensmo.ui.components.navigate

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarScrollBehavior
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.navigation.NavRoutes
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.integration.LaunchAction
import one.srp.gensmo.utils.integration.LaunchActionDetector
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.user.LoginUiState
import one.srp.gensmo.viewmodel.user.LoginViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BottomNavigationBar(
    modifier: Modifier = Modifier,
    navActions: NavActions,
    refer: EventRefer,
    scrollBehavior: BottomAppBarScrollBehavior? = null,
    onActionClick: () -> Unit = {},
    loginViewModel: LoginViewModel = hiltViewModel(),
) {
    val currentRoute = navActions.currentRoute
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)

    val context = LocalContext.current
    var isFirstLaunch by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        isFirstLaunch = LaunchActionDetector.checkFirstLaunch(context, LaunchAction.BOTTOM_NAV_BAR)
        LaunchActionDetector.recordFirstLaunch(context, LaunchAction.BOTTOM_NAV_BAR)
    }

    fun checkLogin(): Boolean {
        loginViewModel.checkAuthState()
        return loginViewModel.uiState.value is LoginUiState.LoggedIn
    }

    BottomAppBar(
        modifier = Modifier
            .zIndex(10f)
            .height(68.dp)
            .then(modifier),
        containerColor = MaterialTheme.colorScheme.surface,
        contentPadding = PaddingValues(0.dp),
        scrollBehavior = scrollBehavior,
    ) {
        Box {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                IconButton(
                    onClick = {
                        if (currentRoute == NavRoutes.Feed.Recommend.route) {
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.NavibarHome,
                                    method = EventMethod.Click,
                                    actionType = EventActionType.NoAction
                                )
                            )
                        } else {
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.NavibarHome,
                                    method = EventMethod.SwitchSection,
                                    actionType = EventActionType.EnterHome
                                )
                            )
                            navActions.navigateToFeedRecommend()
                        }
                    },
                    modifier = Modifier.weight(1f),
                ) {
                    Image(
                        painterResource(
                            if (currentRoute == NavRoutes.Feed.Recommend.route) R.drawable.icon_home_selected
                            else R.drawable.icon_home_unselected
                        ), contentDescription = null, modifier = Modifier.size(24.dp)
                    )
                }

                SearchButtonWithGuide(
                    modifier = Modifier.weight(1f),
                    onClick = {
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.NavibarStyle,
                                method = EventMethod.Click,
                                actionType = EventActionType.InitializeSearch
                            )
                        )
                        onActionClick()
                    },
                    enable = isFirstLaunch
                )

                IconButton(
                    onClick = {
                        if (currentRoute == NavRoutes.User.Profile.route) {
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.NavibarProfile,
                                    method = EventMethod.Click,
                                    actionType = EventActionType.NoAction
                                )
                            )
                        } else {
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.NavibarProfile,
                                    method = EventMethod.SwitchSection,
                                    actionType = EventActionType.EnterProfile
                                )
                            )
                            if (checkLogin()) {
                                navActions.navigateToUserProfile()
                            } else {
                                navActions.navigateToUserLogin()
                            }
                        }
                    },
                    modifier = Modifier.weight(1f),
                ) {
                    Image(
                        painterResource(
                            if (currentRoute == NavRoutes.User.Profile.route) R.drawable.icon_profile_black
                            else R.drawable.icon_profile_line
                        ), contentDescription = null, modifier = Modifier.size(24.dp)
                    )
                }
            }

        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchButtonWithGuide(
    modifier: Modifier = Modifier,
    enable: Boolean = false,
    onClick: () -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    var guideOpen by remember { mutableStateOf(false) }

    fun openTooltip() {
        guideOpen = true
    }

    fun closeTooltip() {
        coroutineScope.launch {
            delay(200)
            guideOpen = false
        }
    }

    LaunchedEffect(enable) {
        if (enable) {
            delay(2500)
            openTooltip()
            delay(5000)
            closeTooltip()
        }
    }

    IconButton(
        onClick = { onClick() },
        modifier = Modifier
            .height(40.dp)
            .width(60.dp),
    ) {
        Image(
            painterResource(R.drawable.icon_search_button),
            contentDescription = null,
            modifier = Modifier.fillMaxSize()
        )
    }

    if (guideOpen) {
        Dialog(
            onDismissRequest = { }, properties = DialogProperties(
                dismissOnBackPress = false, dismissOnClickOutside = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter
            ) {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 68.dp)
                        .background(
                            color = MaterialTheme.colorScheme.surface,
                            shape = MaterialTheme.shapes.medium
                        )
                ) {
                    Box(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painterResource(R.drawable.icon_guide_clothes),
                                null,
                                modifier = Modifier.size(66.dp)
                            )
                            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                Text(
                                    stringResource(R.string.text_have_a_style_idea),
                                    style = AppThemeTextStyle.Heading14H
                                )
                                Text(
                                    stringResource(R.string.text_turn_it_into_an_outfit_instantly),
                                    style = AppThemeTextStyle.Body14H
                                )
                            }
                        }

                        IconButton(
                            onClick = { closeTooltip() },
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .offset(x = 18.dp, y = (-10).dp)
                        ) {
                            Icon(Icons.Default.Close, null)
                        }
                    }

                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .offset(y = 8.dp)
                            .size(16.dp)
                            .rotate(45f)
                            .background(MaterialTheme.colorScheme.surface)
                    )
                }

                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 6.dp)
                        .wrapContentSize()
                ) {
                    IconButton(
                        onClick = { closeTooltip() },
                        modifier = Modifier
                            .height(40.dp)
                            .width(60.dp),
                    ) {
                        Image(
                            painterResource(R.drawable.icon_search_button),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
            }
        }
    }
}
