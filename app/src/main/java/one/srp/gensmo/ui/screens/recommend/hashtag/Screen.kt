package one.srp.gensmo.ui.screens.recommend.hashtag

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LargeTopAppBar
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.events.ViewItemList
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.BrandItem
import one.srp.core.network.model.BrandProductItem
import one.srp.core.network.model.ChatMessageRole
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.SearchParams
import one.srp.core.network.model.SearchQueryMessage
import one.srp.core.network.model.SearchQueryMessageWrapper
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.recommend.hashtag._components.AdditionalParams
import one.srp.gensmo.ui.screens.recommend.hashtag._components.HashtagActivityParams
import one.srp.gensmo.ui.screens.recommend.hashtag._components.HashtagFeedFlow
import one.srp.gensmo.ui.screens.recommend.hashtag._components.HashtagFloatingButton
import one.srp.gensmo.ui.screens.recommend.hashtag._components.HashtagHeader
import one.srp.gensmo.ui.screens.recommend.hashtag._components.text.MarkdownBottomSheet
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.hashtag.HashtagDetailViewModel
import one.srp.gensmo.viewmodel.hashtag.HashtagFeedType
import one.srp.gensmo.viewmodel.search.SearchExtensionViewModel
import timber.log.Timber
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HashtagScreen(
    hashtag: String,
    navActions: NavActions = NavActions(),
    createSession: (SearchQueryMessage, AdditionalParams?) -> Unit = { _, _ -> },
    viewModel: HashtagDetailViewModel = hiltViewModel(),
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.HashtagDetail)

    // Setup scroll behavior for collapsible top app bar
    val topAppBarState = rememberTopAppBarState()
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior(topAppBarState)

    // Tab state management
    val selectedTab by viewModel.selectedTab.collectAsState()
    val hashtagInfo by viewModel.hashtagInfo.collectAsState()
    val tabs = listOf(HashtagFeedType.POPULAR, HashtagFeedType.RECENT)

    // Bottom sheet state management
    val showMarkdownBottomSheet by viewModel.showMarkdownBottomSheet.collectAsState()
    val markdownContent by viewModel.markdownContent.collectAsState()

    // Search panel state management
    val showSearchPanel by viewModel.showSearchPanel.collectAsState()
    val searchExtensionViewModel: SearchExtensionViewModel = hiltViewModel()

    LaunchedEffect(hashtag) {
        metric(SelectItem(EventItemListName.Screen, method = EventMethod.PageView))
        MetricData.logEventAF("af_hashtag_detail_view")
        Timber.d("HashtagDetailScreen opened for hashtag: $hashtag")
        viewModel.setHashtag(hashtag)
    }

    // Analytics for initial tab view
    LaunchedEffect(selectedTab) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApHashtagFeedList,
                method = EventMethod.PageView,
                items = listOf(
                    EventItem(
                        itemId = "${hashtag}_${selectedTab.name}_view",
                        itemName = "${hashtag} ${selectedTab.displayName} Tab View",
                        itemCategory = EventItemCategory.Hashtag
                    )
                )
            )
        )
    }

    // 拦截系统返回按钮，确保使用我们的安全返回逻辑
    BackHandler {
        navActions.back()
    }


    fun genEventItemByFeedType(item: FeedItem): EventItem {
        return (if (item.tryOnTaskId?.isNotBlank() == true) EventItem(
            itemCategory = EventItemCategory.TryOnCollage,
            itemId = item.tryOnTaskId,
            itemName = item.reasoning
        )
        else EventItem(
            itemCategory = EventItemCategory.GeneralCollage,
            itemId = item.moodboardId,
            itemName = item.reasoning
        ))
    }

    fun onHashtagFeedLoadMore(list: List<FeedItem> = emptyList()) {
        metric(
            ViewItemList(
                itemListName = EventItemListName.ApHashtagFeedList,
                items = list.map { item -> genEventItemByFeedType(item) } + EventItem(
                    itemId = "${hashtag}_${selectedTab.name}",
                    itemName = "${hashtag} ${selectedTab.displayName}",
                    itemCategory = EventItemCategory.Hashtag
                )
            )
        )
    }

    fun clickFeedItem(item: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApHashtagFeedListItem,
                method = EventMethod.Click,
                actionType = EventActionType.EnterFeedDetail,
                items = listOf(genEventItemByFeedType(item)) + listOf(
                    EventItem(
                        itemId = hashtag,
                        itemName = hashtag,
                        itemCategory = EventItemCategory.Hashtag,
                    ),
                    EventItem(
                        itemId = selectedTab.name,
                        itemName = selectedTab.displayName,
                        itemCategory = EventItemCategory.Hashtag,
                    )
                )
            )
        )
        // 导航到详情页面
        if (item.tryOnTaskId?.isNotBlank() == true) {
            navActions.navigateToTryOnDetail(item.tryOnTaskId ?: "", "hashtag")
        } else {
            navActions.navigateToFeedDetail(item.moodboardId, "hashtag")
        }
    }

    fun onHashtagFeedItemView(item: FeedItem) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ApHashtagFeedList,
                method = EventMethod.TrueViewTrigger,
                items = listOf(genEventItemByFeedType(item)) + listOf(
                    EventItem(
                        itemId = hashtag,
                        itemName = hashtag,
                        itemCategory = EventItemCategory.Hashtag,
                    ),
                    EventItem(
                        itemId = selectedTab.name,
                        itemName = selectedTab.displayName,
                        itemCategory = EventItemCategory.Hashtag,
                    )
                )
            )
        )
    }

    var placeholder by remember { mutableStateOf("Describe the style or occasion you want to explore...") }
    fun onSearch(
        q: String,
        img: String,
        stylesList: String?,
        budget: String?,
        brand: BrandItem? = null,
        brandProduct: BrandProductItem? = null,
        isProModeEnabled: Boolean = false,
        params: AdditionalParams? = null,
    ) {
        if (q.trim().isNotEmpty()) {
            Timber.d("onSearch: $q, $img, $stylesList, $budget")
            createSession(
                SearchQueryMessage(
                    sessionId = "default",
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = SearchQueryMessageWrapper(
                        searchQuery = SearchParams(
                            query = q,
                            imageUrl = img,
                            debugLevel = 0,
                            budget = budget ?: "",
                            isAsync = true,
                            route = "",
                            isPresetQuery = false,
                            moodboardVersion = "v2",
                            inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                                ?.filter { it.isNotEmpty() } ?: emptyList()),
                            brand = brand?.brand,
                            specifiedProduct = brandProduct?.product,
                            isAgent = isProModeEnabled,
                        )
                    )
                ), params
            )
        }
    }

    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf("") }
    var capturedBrandItem by remember { mutableStateOf<BrandItem?>(null) }
    var capturedBrandProduct by remember { mutableStateOf<BrandProductItem?>(null) }


    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppThemeColors.Gray50)
    ) {
        // 沉浸式背景图片
        hashtagInfo?.backgroundImageUrl?.let { backgroundUrl ->
            Box {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(backgroundUrl)
                        .crossfade(true)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth
                )

                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    AppThemeColors.Gray50.copy(0.65f),
                                    AppThemeColors.Gray50,
                                    AppThemeColors.Gray50,
                                    AppThemeColors.Gray50,
                                    AppThemeColors.Gray50,
                                )
                            )
                        )
                )
            }
        }

        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(scrollBehavior.nestedScrollConnection),
            containerColor = Color.Transparent, // 让背景透明，显示后面的图片
            topBar = {
                LargeTopAppBar(
                    expandedHeight = if (hashtagInfo?.hashtagType?.contains("activity") == true) 360.dp else 320.dp,
                    title = {
                        HashtagHeader(
                            hashtagInfo = hashtagInfo,
                            onActionClick = {
                                // Show markdown bottom sheet when action is clicked
                                viewModel.showMarkdownBottomSheet()
                            }
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = { navActions.back() }) {
                            Icon(
                                imageVector = Icons.Default.ChevronLeft,
                                contentDescription = "Back",
                                modifier = Modifier.size(32.dp),
                            )
                        }
                    },
                    colors = TopAppBarDefaults.largeTopAppBarColors(
                        containerColor = Color.Transparent,
                        scrolledContainerColor = androidx.compose.material3.MaterialTheme.colorScheme.surface.copy(
                            alpha = 0.95f
                        )
                    ),
                    scrollBehavior = scrollBehavior
                )
            }
        ) { paddingValues ->
            HashtagContent(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                selectedTab = selectedTab,
                tabs = tabs,
                viewModel = viewModel,
                hashtag = hashtag,
                onTabSelected = { tab ->
                    viewModel.setSelectedTab(tab)
                },
                onItemClick = { clickFeedItem(it) },
                onLoadMore = { onHashtagFeedLoadMore(it) },
                onItemView = { onHashtagFeedItemView(it) },
                onEmptyStateAction = { action -> }
            )
        }

        // Floating Action Button for Search
        hashtagInfo?.joinButtonContent?.takeIf { it.isNotBlank() }?.let { buttonContent ->
            HashtagFloatingButton(
                content = buttonContent,
                onClick = {
                    // Show search panel when floating button is clicked
                    viewModel.showSearchPanel()
                },
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 48.dp)
            )
        }

        // Markdown Bottom Sheet
        MarkdownBottomSheet(
            isVisible = showMarkdownBottomSheet,
            markdownContent = markdownContent,
            title = hashtagInfo?.ruleTitle ?: "Hashtag Information",
            onDismiss = {
                viewModel.hideMarkdownBottomSheet()
            }
        )

        SearchPanel(
            placeholderText = placeholder,
            isVisible = showSearchPanel && !showCameraPicker,
            onDismiss = { viewModel.hideSearchPanel() },
            viewModel = searchExtensionViewModel,
            imageUrl = capturedImageUri,
            searchQueryText = capturedSearchText,
            onCameraRequest = { showCameraPicker = true },
            onSearch = { q, img, stylesList, budget, isProModeEnabled ->
                onSearch(
                    q,
                    img,
                    stylesList,
                    budget,
                    capturedBrandItem,
                    capturedBrandProduct,
                    isProModeEnabled,
                    hashtagInfo?.let { hashtagInfo ->
                        AdditionalParams(
                            hashtag = HashtagActivityParams(
                                topic = hashtagInfo.hashtag ?: "",
                                topicIcon = hashtagInfo.iconUrl ?: ""
                            )
                        )
                    }
                )
            },
            onUpdateQuery = { query -> capturedSearchText = query },
            navActions = navActions
        )
        if (showCameraPicker) {
            CameraPicker(
                onPhotoTaken = { uri, searchQueryWords ->
                    capturedImageUri = uri.toString()
                    capturedSearchText += searchQueryWords
                    showCameraPicker = false
                },
                onMiss = {
                    showCameraPicker = false
                    capturedImageUri = null
                },
                onSelectBrand = { q, b, bp ->
//                capturedBrandItem = b
//                capturedBrandProduct = bp
//                capturedImageUri = bp.showImage
//                capturedSearchText += q
                    showCameraPicker = false
                    onSearch(q, bp.showImage ?: "", null, null, b, bp, false)
                }
            )
        }
    }
}

@Composable
private fun HashtagContent(
    modifier: Modifier = Modifier,
    selectedTab: HashtagFeedType,
    tabs: List<HashtagFeedType>,
    viewModel: HashtagDetailViewModel,
    hashtag: String,
    onTabSelected: (HashtagFeedType) -> Unit,
    onItemClick: (FeedItem) -> Unit,
    onLoadMore: (List<FeedItem>) -> Unit,
    onItemView: (FeedItem) -> Unit,
    onEmptyStateAction: (String) -> Unit,
) {
    val selectedIndex = tabs.indexOf(selectedTab)

    Column(
        modifier = modifier.background(
            AppThemeColors.Gray50
        )
    ) {
        // Tab Row
        TabRow(
            selectedTabIndex = selectedIndex,
            modifier = Modifier.fillMaxWidth(),
            divider = {},
            indicator = { tabPositions ->
                if (selectedIndex < tabPositions.size) {
                    BoxWithConstraints(contentAlignment = Alignment.BottomCenter) {
                        val paddingH = (this.maxWidth - 110.dp) / tabPositions.size / 2
                        TabRowDefaults.SecondaryIndicator(
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedIndex])
                                .padding(horizontal = paddingH),
                        )
                    }
                }
            },
        ) {
            tabs.forEachIndexed { index, tab ->
                Tab(
                    selected = selectedTab == tab,
                    onClick = { onTabSelected(tab) },
                    text = {
                        Text(
                            text = tab.displayName,
                            style = AppThemeTextStyle.Body16H.copy(if (selectedTab == tab) AppThemeColors.Black else AppThemeColors.Gray500)
                        )
                    }
                )
            }
        }

        // Feed content area based on selected tab
        key(selectedTab) {
            HashtagFeedFlow(
                modifier = Modifier.fillMaxSize(),
                viewModel = viewModel,
                hashtag = hashtag,
                feedType = selectedTab,
                onItemClick = onItemClick,
                onLoadMore = onLoadMore,
                onItemView = onItemView,
                onEmptyStateAction = onEmptyStateAction
            )
        }
    }
}
