package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.core.network.model.HomePageInfo
import one.srp.core.network.model.HomePageInfoStyle
import one.srp.gensmo.data.repository.utils.BaseState
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun SearchStyleList(
    homeInfo: BaseState<HomePageInfo>,
    modifier: Modifier = Modifier,
    onStyleClick: (HomePageInfoStyle) -> Unit = {}
) {
    val styles = when (homeInfo) {
        is BaseState.Success -> homeInfo.data.chooseAStyle
        else -> null
    }

    if (styles.isNullOrEmpty()) return

    Column(
        modifier = modifier
    ) {
        Text(
            text = "Style types",
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            style = AppThemeTextStyle.Heading16D
        )

        Row(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            styles.forEach { style ->
                StyleCard(
                    style = style,
                    onClick = { onStyleClick(style) }
                )
            }
        }
    }
}

@Composable
private fun StyleCard(
    style: HomePageInfoStyle,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier
            .width(92.dp)
            .height(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            AsyncImage(
                model = style.image,
                contentDescription = style.title,
                modifier = Modifier
                    .shadow(elevation = 3.8592522144317627.dp, spotColor = Color(0x1A2F536D), ambientColor = Color(0x1A2F536D))
                    .border(width = 0.33333.dp, color = Color(0xFFF0F0F0), shape = RoundedCornerShape(size = 6.66667.dp))
                    .padding(0.33333.dp)
                    .width(91.99999.dp)
                    .height(91.99999.dp)
                    .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 6.66667.dp)),
                contentScale = ContentScale.Crop
            )
            
            Text(
                text = style.title ?: "",
                style = AppThemeTextStyle.Body12H,
                modifier = Modifier
                    .padding(vertical = 4.dp)
                    .fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center
            )
        }
    }
}
