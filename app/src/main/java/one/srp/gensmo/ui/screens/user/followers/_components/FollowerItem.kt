package one.srp.gensmo.ui.screens.user.followers._components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.gensmo.R
import one.srp.core.network.model.FollowerItem
import one.srp.gensmo.ui.components.action.FollowAction
import one.srp.gensmo.ui.components.action.FollowStatus
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.user.FollowersListViewModel

@Composable
fun FollowerItem(
    follower: FollowerItem,
    currentUserId: String? = null,
    onUserClick: () -> Unit,
    onFollowMetric: (SelectItem) -> Unit,
    viewModel: FollowersListViewModel = hiltViewModel()
) {
    // 从 ViewModel 获取关注状态
    val followStatusMap by viewModel.followStatusMap.collectAsState()
    val currentFollowStatus = followStatusMap[follower.uid]

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .clickable { 
                // 整个 item 点击埋点
                onFollowMetric(
                    SelectItem(
                        itemListName = EventItemListName.UserInfo,
                        method = EventMethod.Click,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.User,
                                itemId = follower.uid,
                                itemName = follower.username
                            ),
                            EventItem(
                                itemCategory = EventItemCategory.Follower,
                                itemId = "1",
                                itemName = "Followers List"
                            )
                        )
                    )
                )
                onUserClick()
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 用户头像
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            AsyncImage(
                model = follower.avatar,
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop,
                error = painterResource(id = R.drawable.icon_unregister)
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 用户信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = follower.username,
                style = AppThemeTextStyle.Body14H,
                color = AppThemeColors.Black
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 只有当不是当前用户时才显示关注按钮
        if (currentUserId != follower.uid) {
            // 使用 ViewModel 中的关注状态作为初始状态
            val initialFollowStatus = currentFollowStatus ?: when (follower.followStatus) {
                FollowStatus.Followed.value -> FollowStatus.Followed
                FollowStatus.Mutual.value -> FollowStatus.Mutual
                FollowStatus.FollowedBack.value -> FollowStatus.FollowedBack
                FollowStatus.NotFollowed.value -> FollowStatus.NotFollowed
                else -> null
            }

            FollowAction(
                initialFollowStatus = initialFollowStatus,
                userId = follower.uid,
                onFollow = { isFollowing, newFollowStatus ->
                    // 埋点：关注/取消关注按钮点击
                    onFollowMetric(
                        SelectItem(
                            itemListName = EventItemListName.FollowBtn,
                            method = EventMethod.Click,
                            actionType = if (isFollowing) EventActionType.Follow else EventActionType.Unfollow,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.User,
                                    itemId = follower.uid,
                                    itemName = follower.username
                                ),
                                EventItem(
                                    itemCategory = EventItemCategory.Follower,
                                    itemId = "1",
                                    itemName = "Followers List"
                                )
                            )
                        )
                    )
                    
                    // API 调用完成后，更新 ViewModel 中的关注状态
                    viewModel.updateFollowStatus(follower.uid, newFollowStatus)
                }
            ) { isFollowing, followStatus, clickFollow ->
                Button(
                    onClick = { clickFollow() },
                    modifier = Modifier
                        .height(32.dp)
                        .width(100.dp),
                    shape = RoundedCornerShape(2.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.Red600,
                        contentColor = if (isFollowing) AppThemeColors.Gray700 else AppThemeColors.White
                    )
                ) {
                    Text(
                        text = when {
                            !isFollowing -> stringResource(R.string.text_follow)
                            followStatus == FollowStatus.Mutual -> stringResource(R.string.text_friends)
                            followStatus == FollowStatus.Followed -> stringResource(R.string.text_following)
                            else -> stringResource(R.string.text_following)
                        },
                        style = AppThemeTextStyle.Body12H,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
} 