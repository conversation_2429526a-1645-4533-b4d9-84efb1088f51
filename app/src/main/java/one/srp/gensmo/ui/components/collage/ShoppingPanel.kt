package one.srp.gensmo.ui.components.collage

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.MoodboardEntity
import one.srp.core.network.model.ProductItem
import one.srp.gensmo.R
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import timber.log.Timber


@Composable
fun ShoppingPanel(item: MoodboardEntity, onOpenProductWeb: (String) -> Unit) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.ShopList)

    LaunchedEffect(Unit) {
        metric(SelectItem(EventItemListName.Screen, method = EventMethod.PageView))
    }

    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .padding(horizontal = 24.dp)
            .fillMaxHeight(0.7f),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(stringResource(R.string.text_related_products), fontSize = 18.sp)

        Column(
            modifier = Modifier.verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item.products.mapIndexed { index, product ->
                ShoppingItemCard(product, index, onOpenProductWeb)
            }
        }
    }
}

@Composable
fun ShoppingItemCard(item: ProductItem, index: Int = -1, onOpenProductWeb: (String) -> Unit) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.ShopList)

    val linkValid = item.link?.let {
        val uri = it.toUri()
        uri.scheme != null && uri.host != null
    } == true

    val openLink = {
        if (linkValid) {
            try {
                metric(
                    SelectItem(
                        method = EventMethod.Click,
                        itemListName = EventItemListName.ProductCard,
                        actionType = EventActionType.ProductExternalJump,
                        items = listOf(
                            EventItem(
                                itemId = item.id,
                                itemName = item.title,
                                itemCategory = EventItemCategory.Product,
                                index = index,
                            )
                        )
                    )
                )
                MetricData.logEventAF("af_product_external_jump")

                onOpenProductWeb(item.link ?: "")
            } catch (err: Exception) {
                Timber.e(err)
            }
        }
    }

    Card(
        colors = CardDefaults.cardColors(Color.White), border = CardDefaults.outlinedCardBorder()
    ) {
        Row(
            modifier = Modifier
                .padding(8.dp)
                .heightIn(0.dp, 120.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current).data(item.mainImage?.link)
                    .crossfade(true).build(),
                contentDescription = null,
                modifier = Modifier.weight(1f),
                contentScale = ContentScale.Fit,
            )

            Column(
                modifier = Modifier.weight(2f), verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                item.title?.let { Text(text = it, maxLines = 1, overflow = TextOverflow.Ellipsis) }
                item.buyboxWinner?.price?.raw?.let { Text(it) }

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current).data(item.sourceIcon)
                            .crossfade(true).build(),
                        contentDescription = null,
                        contentScale = ContentScale.Fit,
                    )
                    item.platform?.let {
                        Text(
                            text = it,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            fontSize = 13.sp,
                            modifier = Modifier.weight(1f)
                        )
                    }

                    Button(
                        onClick = { openLink() },
                        modifier = Modifier.padding(2.dp),
                        enabled = linkValid,
                    ) {
                        Text(stringResource(R.string.text_view_more), fontSize = 12.sp)
                    }
                }
            }
        }
    }
}