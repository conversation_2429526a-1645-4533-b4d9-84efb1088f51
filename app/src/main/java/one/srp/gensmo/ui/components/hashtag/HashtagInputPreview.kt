package one.srp.gensmo.ui.components.hashtag

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import one.srp.core.network.model.HashtagItem
import one.srp.gensmo.ui.theme.AppTheme

/**
 * Preview composables for HashtagInputComponent development and testing
 */

@Preview(showBackground = true)
@Composable
fun HashtagInputComponentPreview() {
    var selectedHashtags by remember { 
        mutableStateOf(
            listOf(
                HashtagItem(hashtag = "#fashion", frequency = 1000, iconUrl = null),
                HashtagItem(hashtag = "#style", frequency = 500, iconUrl = null)
            )
        ) 
    }

    AppTheme {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                HashtagInputComponent(
                    selectedHashtags = selectedHashtags,
                    onHashtagsChanged = { hashtags ->
                        selectedHashtags = hashtags
                    },
                    placeholder = "Add hashtags...",
                    maxHashtags = 10
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HashtagInputComponentEmptyPreview() {
    var selectedHashtags by remember { mutableStateOf(emptyList<HashtagItem>()) }

    AppTheme {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                HashtagInputComponent(
                    selectedHashtags = selectedHashtags,
                    onHashtagsChanged = { hashtags ->
                        selectedHashtags = hashtags
                    },
                    placeholder = "Start typing to add hashtags...",
                    maxHashtags = 5
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HashtagInputComponentMaxReachedPreview() {
    val selectedHashtags = (1..10).map { 
        HashtagItem(hashtag = "#hashtag$it", frequency = it * 100, iconUrl = null) 
    }

    AppTheme {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color.White
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                HashtagInputComponent(
                    selectedHashtags = selectedHashtags,
                    onHashtagsChanged = { },
                    placeholder = "Add hashtags...",
                    maxHashtags = 10
                )
            }
        }
    }
}