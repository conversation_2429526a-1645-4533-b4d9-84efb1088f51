package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.core.network.model.SavedItem
import one.srp.gensmo.ui.theme.appThemeShapes
import one.srp.gensmo.utils.mime.resizeImage

@Composable
fun SavedCard(
    modifier: Modifier = Modifier,
    item: SavedItem,
    onItemClick: () -> Unit = {},
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onItemClick() }
            .then(modifier),
        shape = appThemeShapes.small,
        colors = CardDefaults.cardColors(MaterialTheme.colorScheme.background),
    ) {
        Column(
            modifier = Modifier
                .padding(4.dp, 0.dp)
                .padding(bottom = 4.dp)
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current).data(
                    resizeImage(item.image ?: item.tryOnUrl ?: item.imageUrl ?: "", 400)
                ).crossfade(true).build(),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop
            )
        }
    }
}