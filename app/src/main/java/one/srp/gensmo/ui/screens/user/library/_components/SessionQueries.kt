package one.srp.gensmo.ui.screens.user.library._components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import one.srp.core.network.model.SessionHistoryItem
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun SessionQueryItem(
    query: SessionHistoryItem,
    modifier: Modifier = Modifier,
    onDelete: (String) -> Unit = {},
    onOpenHistory: (SessionHistoryItem) -> Unit = {},
) {
    var showMenu by remember { mutableStateOf(false) }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onOpenHistory(query) },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = query.sessionTitle ?: "Session-${query.sessionId}",
                    style = AppThemeTextStyle.Body16H,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = formatDate(query.lastUpdated),
                    style = AppThemeTextStyle.Body12LightH.copy(
                        color = Color(0xFF767676)
                    )
                )

//                Box {
//                    IconButton(
//                        onClick = { showMenu = true },
//                        modifier = Modifier.size(24.dp)
//                    ) {
//                        Icon(
//                            painter = painterResource(id = R.drawable.icon_more_actions),
//                            contentDescription = null,
//                            modifier = Modifier.fillMaxSize(),
//                            tint = Color.Black
//                        )
//                    }
//
//                    DropdownMenu(
//                        expanded = showMenu,
//                        onDismissRequest = { showMenu = false },
//                        properties = PopupProperties(
//                            focusable = true,
//                            usePlatformDefaultWidth = false
//                        ),
//                        offset = DpOffset(0.dp, 8.dp),
//                        modifier = Modifier
//                            .width(160.dp)
//                            .shadow(
//                                elevation = 12.dp,
//                                spotColor = Color(0x1A2F536D),
//                                ambientColor = Color(0x1A2F536D)
//                            )
//                            .background(
//                                color = Color(0xFFFFFFFF),
//                                shape = RoundedCornerShape(size = 4.dp)
//                            )
//                    ) {
//                        DropdownMenuItem(
//                            text = {
//                                Row(
//                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
//                                    verticalAlignment = Alignment.CenterVertically
//                                ) {
//                                    Icon(
//                                        painter = painterResource(id = R.drawable.icon_trashbin),
//                                        contentDescription = null,
//                                        modifier = Modifier.size(20.dp),
//                                        tint = Color(0xFFEC221F)
//                                    )
//                                    Text(
//                                        "Delete",
//                                        style = AppThemeTextStyle.Body14LightH,
//                                        color = Color(0xFFEC221F)
//                                    )
//                                }
//                            },
//                            onClick = {
//                                onDelete(query.sessionId)
//                                showMenu = false
//                            },
//                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
//                        )
//                    }
//                }
            }
        }
    }
}

private fun formatDate(timestamp: Double): String {
    val date = Date((timestamp * 1000).toLong())
    val now = Date()
    val diff = now.time - date.time

    return when {
        diff < 24 * 60 * 60 * 1000 -> "Today"
        diff < 48 * 60 * 60 * 1000 -> "Yesterday"
        else -> SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(date)
    }
} 