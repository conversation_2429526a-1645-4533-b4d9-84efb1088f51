package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import one.srp.gensmo.ui.theme.AppThemeTextStyle

/**
 * 自定义Pro Style开关组件
 * 将"Pro Style"文本放在开关内部，文本离左右圆点都有一定距离
 */
@Composable
fun ProStyleSwitch(
    checked: <PERSON>ole<PERSON>,
    onCheckedChange: (<PERSON>olean) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .width(120.dp) // 增加宽度以容纳更长文本
            .height(32.dp)
            .background(
                color = if (checked) Color(0xFF1E1E1E) else Color(0xFFEFEFEF),
                shape = RoundedCornerShape(16.dp)
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onCheckedChange(!checked) }
    ) {
        // 白色圆形按钮 - 只在对应位置显示
        if (!checked) {
            // 关闭状态：显示左边的圆点
            Box(
                modifier = Modifier
                    .size(30.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(15.dp)
                    )
                    .align(Alignment.CenterStart)
            )
        } else {
            // 开启状态：显示右边的圆点
            Box(
                modifier = Modifier
                    .size(30.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(15.dp)
                    )
                    .align(Alignment.CenterEnd)
            )
        }
        
        // 文本内容 - 根据状态调整位置
        Text(
            text = "Agent Mode",
            style = AppThemeTextStyle.Body12H.copy(
                color = if (checked) Color.White else Color(0xFF1E1E1E)
            ),
            modifier = Modifier
                .align(Alignment.Center)
                .offset(
                    x = if (checked) (-12).dp else 12.dp // 关闭时偏右，开启时偏左
                )
        )
    }
} 