package one.srp.gensmo.ui.screens.user.following

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.runBlocking
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.following._components.FollowingItem
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.user.FollowingListViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FollowingListScreen(
    userId: String,
    navActions: NavActions,
    viewModel: FollowingListViewModel = hiltViewModel()
) {
    val following by viewModel.following.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val hasMore by viewModel.hasMore.collectAsState()

    val listState = rememberLazyListState()

    // 获取当前用户ID
    val currentUserId = runBlocking { UserDataStoreManager.getUserId() }

    // 初始化埋点 helper，设置 refer 为 profile_list
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.ProfileList)

    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Following,
                        itemId = "2",
                        itemName = "Following List"
                    )
                )
            )
        )
    }

    LaunchedEffect(userId) {
        viewModel.loadFollowing(userId)
    }

    // 监听滚动到底部，加载更多数据
    LaunchedEffect(listState) {
        val loadMoreThreshold = 3
        var lastLoadMoreIndex = -1 // 记录上次加载的索引，避免重复触发
        
        snapshotFlow { listState.layoutInfo.visibleItemsInfo }
            .collect { visibleItems ->
                if (visibleItems.isNotEmpty() && hasMore && !isLoading) {
                    val lastVisibleItem = visibleItems.last()
                    val totalItems = listState.layoutInfo.totalItemsCount
                    
                    // 只有当滚动到接近底部且不是重复触发时才加载更多
                    if (lastVisibleItem.index >= totalItems - loadMoreThreshold && 
                        lastVisibleItem.index != lastLoadMoreIndex) {
                        lastLoadMoreIndex = lastVisibleItem.index
                        viewModel.loadMore()
                    }
                }
            }
    }

    Scaffold(
        topBar = {
            TopBar(
                immersive = true,
                onBack = { navActions.back() },
                content = {
                    Text(
                        text = "Following",
                        style = AppThemeTextStyle.Heading16D,
                        color = AppThemeColors.Black
                    )
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (following.isEmpty() && !isLoading) {
                Text(
                    text = "No following yet",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(16.dp)
                )
            } else {
                LazyColumn(
                    state = listState,
                    modifier = Modifier.fillMaxSize()
                ) {
                    items(
                        items = following,
                        key = { it.uid }
                    ) { followingItem ->
                        FollowingItem(
                            following = followingItem,
                            currentUserId = currentUserId,
                            onUserClick = { 
                                navActions.navigateToUserPublicProfile(followingItem.uid)
                            },
                            onFollowMetric = { selectItem ->
                                metric(selectItem)
                            },
                            viewModel = viewModel
                        )
                    }

                    if (isLoading && following.isNotEmpty()) {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                BaseLoading()
                            }
                        }
                    }
                }
            }

            if (isLoading && following.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    BaseLoading()
                }
            }
        }
    }
} 