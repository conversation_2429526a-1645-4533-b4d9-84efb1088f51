package one.srp.gensmo.ui.screens.onboard.pick

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import one.srp.gensmo.MainApplication
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import one.srp.gensmo.data.remote.UserService
import one.srp.core.network.model.OnboardTagsRequest
import androidx.compose.runtime.LaunchedEffect
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory

private data class Vibe(val title: String)

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun OnboardPickScreen(
    navActions: NavActions = NavActions(),
    refer: EventRefer = EventRefer.OpenTagSelect,
) {
    val coroutineScope = rememberCoroutineScope()
    val allVibes = remember {
        listOf(
            "Casual",
            "Formal", 
            "Business",
            "Sporty",
            "Streetwear",
            "Athleisure",
            "Minimalist",
            "Chic",
            "Vintage",
            "Bohemian",
            "Elegant",
            "Classic",
            "Feminine",
            "Preppy",
            "Edgy",
            "Romantic",
            "Sophisticated",
            "Youthful",
            "Grunge",
            "Punk",
            "Gothic",
            "Retro",
            "Luxury",
            "Hipster",
            "Androgynous"
        ).map { Vibe(it) }
    }
        val selected = remember { mutableStateListOf<String>() }

    val context = LocalContext.current
    val oneSignalManager = (context.applicationContext as MainApplication).oneSignalManager

    // metric pageview
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)
    LaunchedEffect(Unit) {
        metric(SelectItem(itemListName = EventItemListName.Screen, method = EventMethod.PageView))
    }

    fun complete() {
        oneSignalManager.requestNotificationPermission(context)
        navActions.navigateToFeedRecommend()
    }

    fun handleButtonClick() {
        if (selected.isEmpty()) {
            complete()
        } else {
            // 首先上报标签
            coroutineScope.launch(Dispatchers.IO) {
                try {
                    UserService.api.postUserOnboardTags(
                        OnboardTagsRequest(tags = selected.toList())
                    )
                } catch (_: Exception) {
                }
            }
            navActions.navigateToOnboardResult(selected.toList())
        }
    }

    Scaffold(
        topBar = {
            TopBar(
                transparent = true,
                navIconEnable = false,
                action = {
                    Row(modifier = Modifier.padding(horizontal = 16.dp)) {
                        TextButton(onClick = {
                            // click skip metric
                            metric(
                                SelectItem(
                                    itemListName = EventItemListName.SkipBtn,
                                    method = EventMethod.Click
                                )
                            )
                            if (selected.isNotEmpty()) {
                                coroutineScope.launch(Dispatchers.IO) {
                                    try {
                                        UserService.api.postUserOnboardTags(
                                            OnboardTagsRequest(tags = selected.toList())
                                        )
                                    } catch (_: Exception) {
                                    }
                                }
                            }
                            complete()
                        }) {
                            Text(stringResource(R.string.text_skip), style = AppThemeTextStyle.Body16H)
                        }
                    }
                },
            )
        },
        bottomBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(16.dp)
            ) {
                Button(
                    onClick = {
                        // click main create-look metric
                        val tagName = selected.joinToString("_") { it.lowercase().replace(" ", "_") }
                        metric(
                            SelectItem(
                                itemListName = EventItemListName.CreateLookBtn,
                                method = EventMethod.Click,
                                items = listOf(
                                    EventItem(
                                        itemCategory = EventItemCategory.OpenTag,
                                        itemName = tagName,
                                    )
                                )
                            )
                        )
                        handleButtonClick()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(54.dp),
                    shape = MaterialTheme.shapes.medium,
                ) {
                    Text(stringResource(R.string.text_get_your_first_look), style = AppThemeTextStyle.Body16H)
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            Text("PICK YOUR VIBE", style = AppThemeTextStyle.Heading24D)
            Text(
                "Choose up to 5 words that feel like you.",
                style = AppThemeTextStyle.Body13LightH.copy(AppThemeColors.Gray700)
            )

            Spacer(modifier = Modifier.height(50.dp))

            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                allVibes.forEach { vibe ->
                    val isSelected = selected.contains(vibe.title)
                    
                    Box(
                        modifier = Modifier
                            .height(48.dp)
                            .clip(RoundedCornerShape(size = 4.dp))
                            .background(
                                if (isSelected) Color.Black else Color.White
                            )
                            .border(
                                width = 1.dp,
                                color = if (isSelected) Color.Black else AppThemeColors.Gray200,
                                shape = RoundedCornerShape(size = 4.dp)
                            )
                            .clickable {
                                if (isSelected) {
                                    selected.remove(vibe.title)
                                } else if (selected.size < 5) {
                                    selected.add(vibe.title)
                                }
                            }
                            .padding(start = 16.dp, top = 12.dp, end = 16.dp, bottom = 12.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = vibe.title,
                            style = AppThemeTextStyle.Body14H.copy(
                                color = if (isSelected) Color.White else Color.Black
                            )
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}
