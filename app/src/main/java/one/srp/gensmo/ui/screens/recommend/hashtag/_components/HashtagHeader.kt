package one.srp.gensmo.ui.screens.recommend.hashtag._components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.core.network.model.HashtagInfo
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import java.util.Date
import java.util.concurrent.TimeUnit

@Composable
fun HashtagHeader(
    modifier: Modifier = Modifier,
    hashtagInfo: HashtagInfo?,
    onActionClick: () -> Unit = {},
) {
    // 如果没有 hashtag 信息，显示占位符或者不显示
    if (hashtagInfo == null) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .height(100.dp),
            contentAlignment = Alignment.Center
        ) {
            BaseLoading(strokeWidth = 2.dp)
        }
        return
    }

    BoxWithConstraints {
        if (this.maxHeight < 80.dp) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 40.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = hashtagInfo.hashtag ?: "",
                    style = AppThemeTextStyle.Heading16H
                )
            }
        } else {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(end = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row {
                    // Main banner section
                    // Top section with cover image
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        (hashtagInfo.imageUrl
                            ?: R.drawable.image_hashtag_placeholder).let { coverUrl ->
                            Card(
                                modifier = Modifier.size(148.dp),
                                shape = MaterialTheme.shapes.medium,
                            ) {
                                AsyncImage(
                                    model = ImageRequest.Builder(LocalContext.current)
                                        .data(coverUrl)
                                        .crossfade(true)
                                        .build(),
                                    contentDescription = hashtagInfo.tagTitle,
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Crop
                                )
                            }
                        }

                        // Content overlay
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            Text(
                                text = hashtagInfo.hashtag ?: "",
                                style = AppThemeTextStyle.Heading16H,
                                overflow = TextOverflow.Ellipsis,
                            )


                            Text(
                                text = "${hashtagInfo.postCount} posts",
                                style = AppThemeTextStyle.Body12H.copy(AppThemeColors.Gray600),
                            )

                            if (hashtagInfo.hashtagType?.contains("activity") == true) {
                                // Status badge and countdown in a row
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                                ) {
                                    // Status badge
                                    hashtagInfo.status?.let { status ->
                                        if (status == "active") {
                                            Box(
                                                modifier = Modifier
                                                    .background(
                                                        Color(0xFF26BA84).copy(0.2f),
                                                        RectangleShape
                                                    )
                                                    .border(1.dp, Color(0xFF26BA84), RectangleShape)
                                                    .padding(horizontal = 8.dp, vertical = 2.dp)
                                            ) {
                                                Text(
                                                    text = "• Live Now •",
                                                    style = AppThemeTextStyle.Body14H.copy(
                                                        Color(
                                                            0xFF26BA84
                                                        )
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }

                                // Countdown timer
                                hashtagInfo.communityEndTimestamp?.let { endTime ->
                                    val timeLeft = calculateTimeLeft(endTime)
                                    if (timeLeft.isNotEmpty()) {
                                        Text(
                                            text = "Submit in: $timeLeft",
                                            style = AppThemeTextStyle.Body12H.copy(AppThemeColors.Gray600),
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .height(70.dp)
                ) {
                    // Description section
                    hashtagInfo.description?.let { description ->
                        Text(
                            text = description,
                            style = AppThemeTextStyle.Body12H.copy(AppThemeColors.Gray650),
                            modifier = Modifier.padding(horizontal = 4.dp),
                        )
                    }
                }

                Row {
                    if (hashtagInfo.hashtagType?.contains("activity") == true) {
                        // CTA section if available
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { onActionClick() },
                            shape = MaterialTheme.shapes.medium,
                            colors = CardDefaults.cardColors(
                                containerColor = AppThemeColors.White,
                                contentColor = AppThemeColors.Black,
                            ),
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                AsyncImage(
                                    model = ImageRequest.Builder(LocalContext.current)
                                        .data(hashtagInfo.ruleImageUrl)
                                        .crossfade(true)
                                        .build(),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(32.dp)
                                        .clip(RoundedCornerShape(4.dp)),
                                    contentScale = ContentScale.Crop
                                )

                                Spacer(modifier = Modifier.width(12.dp))

                                Text(
                                    text = hashtagInfo.ruleTitle ?: "",
                                    style = AppThemeTextStyle.Body12H.copy(fontWeight = FontWeight.SemiBold),
                                    overflow = TextOverflow.Ellipsis,
                                    modifier = Modifier.weight(1f)
                                )

                                Icon(Icons.Default.ChevronRight, null)
                            }
                        }
                    }
                }
            }
        }
    }
}


private fun calculateTimeLeft(endTimestamp: Long): String {
    return try {
        val endTime = Date(endTimestamp)
        val currentTime = Date()

        if (endTime.after(currentTime)) {
            val diffInMillis = endTime.time - currentTime.time
            val days = TimeUnit.MILLISECONDS.toDays(diffInMillis)
            val hours = TimeUnit.MILLISECONDS.toHours(diffInMillis) % 24
            val minutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillis) % 60

            when {
                days > 0 -> "${days}d ${hours}h ${minutes}m"
                hours > 0 -> "${hours}h ${minutes}m"
                else -> "${minutes}m"
            }
        } else {
            ""
        }
    } catch (e: Exception) {
        ""
    }
}
