package one.srp.gensmo.ui.screens.session.chat

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.AgentResMessage
import one.srp.core.network.model.ChatJson
import one.srp.core.network.model.ChatMessage
import one.srp.core.network.model.SearchItem
import one.srp.core.network.model.SearchQueryMessage
import one.srp.core.network.model.SearchResMessage
import one.srp.core.network.model.TryOnQueryMessage
import one.srp.core.network.model.TryOnResMessage
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.session.chat._components.ChatContainer
import one.srp.gensmo.ui.screens.session.chat._components.ChatHeader
import one.srp.gensmo.ui.screens.session.chat._components.SessionNavigation
import one.srp.gensmo.ui.screens.session.chat._viewmodel.ChatMeta
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionActionStatus
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel

@Composable
fun SessionChatScreen(
    navActions: NavActions = NavActions(),
    sharedNavViewModel: SharedNavViewModel = hiltViewModel(),
    viewModel: SessionChatViewModel = hiltViewModel(),
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Channel)

    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView
            )
        )
    }

    val context = LocalContext.current
    val chatMeta = viewModel.chatMeta.collectAsState().value
    val initialMessage = viewModel.initialMessage.collectAsState().value
    val initialParams = viewModel.initialParams.collectAsState().value
    val restoreStatus = viewModel.restoreStatus.collectAsState().value
    val initStatus = viewModel.initStatus.collectAsState().value
    val connectStatus = viewModel.connectStatus.collectAsState().value
    var reconDialogOpen by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.initServer()
    }

    LaunchedEffect(connectStatus) {
        if (connectStatus == SessionActionStatus.Failed) {
            reconDialogOpen = true
        }
    }

    LaunchedEffect(chatMeta.sessionId) {
        chatMeta.sessionId?.let { sessionId ->
            initialMessage?.let {
                viewModel.sendMessage(it.alterId(sessionId))
                when (it) {
                    is SearchQueryMessage -> {
                        viewModel.setTitle(it.value.searchQuery.query)
                    }

                    is TryOnQueryMessage -> {
                        viewModel.setTitle("Try on")
                    }

                    else -> {}
                }
                viewModel.setInitialState(null)
            }
        }
    }

    fun postMessage(message: ChatMessage) {
        viewModel.sendMessage(message)
    }

    fun navigate(route: SessionNavigation, item: ChatMessage, index: Int?) {
        when (route) {
            SessionNavigation.CollageTask -> {
                val result = (item as SearchResMessage).value.searchRes

                // 避免覆盖已同步状态的缓存
                val cached = sharedNavViewModel.getSearchItem(result.taskId)
                if (cached == null) {
                    sharedNavViewModel.putSearchItem(result.taskId, result)
                }
                navActions.navigateToSessionCollageTaskView(result.taskId, index ?: 0)
            }

            SessionNavigation.TryOnTask -> {
                val result = (item as TryOnResMessage).value.tryonRes

                result.tryOnTaskId?.let { tryOnTaskId ->
                    sharedNavViewModel.putTryOnTaskItem(tryOnTaskId, result)
                    navActions.navigateToSessionTryOnTaskView(tryOnTaskId)
                } ?: run {
                    Toast.makeText(context, "Open failed.", Toast.LENGTH_SHORT).show()
                }
            }

            SessionNavigation.AgentCollageTask -> {
                val agent = item as AgentResMessage
                val res = try {
                    ChatJson.decodeFromJsonElement(
                        SearchItem.serializer(),
                        agent.value.agentRes
                    )
                } catch (_: Exception) {
                    SearchItem(taskId = agent.messageId)
                }
                val safeTaskId = res.taskId.ifBlank { agent.messageId }
                // 避免覆盖已同步状态的缓存
                val cached = sharedNavViewModel.getSearchItem(safeTaskId)
                if (cached == null) {
                    sharedNavViewModel.putSearchItem(safeTaskId, res)
                }
                navActions.navigateToSessionCollageTaskView(safeTaskId, index ?: 0)
            }
        }
    }


    fun refreshSession() {
        reconDialogOpen = false

        viewModel.clear()
        viewModel.setInitialSessionId(
            ChatMeta(
                sessionId = chatMeta.sessionId,
                title = chatMeta.title
            )
        )
        viewModel.initServer()
    }

    Scaffold { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            ChatHeader(
                viewModel = viewModel, onBack = { navActions.back() })

            when (restoreStatus) {
                SessionActionStatus.Loading -> {
                    BaseLoading(modifier = Modifier.fillMaxSize())
                }

                SessionActionStatus.Failed -> {
                    BaseError(onClick = { navActions.back() })
                }

                else -> {
                    when (initStatus) {
                        SessionActionStatus.Loading -> {
                            BaseLoading(modifier = Modifier.fillMaxSize())
                        }

                        else -> {
                            ChatContainer(
                                viewModel = viewModel,
                                onMessage = { postMessage(it) },
                                onNavigate = { route, item, index -> navigate(route, item, index) })
                        }
                    }

                }
            }

            if (reconDialogOpen) {
                ConnectFailDialog(
                    onClose = { reconDialogOpen = false },
                    onAction = { refreshSession() })
            }
        }
    }
}

@Composable
private fun ConnectFailDialog(onClose: () -> Unit = {}, onAction: () -> Unit = {}) {
    Dialog(
        onDismissRequest = { onClose() },
    ) {
        Card(
            colors = CardDefaults.cardColors(
                MaterialTheme.colorScheme.background,
                MaterialTheme.colorScheme.onBackground,
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Row {
                    Spacer(modifier = Modifier.weight(1f))
                    IconButton(onClick = { onClose() }) { Icon(Icons.Default.Close, null) }
                }

                Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxWidth()) {
                    Image(painterResource(R.drawable.icon_connection_fail_plug), null)
                }

                Spacer(modifier = Modifier.height(16.dp))

                Column(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        stringResource(R.string.text_connection_failed),
                        style = AppThemeTextStyle.Heading16H.copy(fontSize = 24.sp)
                    )

                    Text(
                        stringResource(R.string.text_please_refresh_and_try_again),
                        style = AppThemeTextStyle.Body14H
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Button(
                        onClick = { onAction() },
                        modifier = Modifier.fillMaxWidth(),
                        shape = MaterialTheme.shapes.medium
                    ) {
                        Text("Refresh", style = AppThemeTextStyle.Body16H)
                    }
                }
            }
        }
    }
}
