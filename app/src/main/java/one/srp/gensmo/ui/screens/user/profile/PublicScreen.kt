package one.srp.gensmo.ui.screens.user.profile

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.material3.Text
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._components.PublicProfileAssets
import one.srp.gensmo.ui.screens.user.profile._components.UserStatsCard
import one.srp.gensmo.ui.screens.user.profile._viewmodel.PublicProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserPublicProfileScreen(
    navActions: NavActions = NavActions(),
    userId: String?,
    onClearFeed: () -> Unit = {},
    viewModel: PublicProfileViewModel = hiltViewModel(),
) {
    LaunchedEffect(userId) {
        userId?.let { viewModel.loadUserProfile(it) }
    }

    val userName by viewModel.userName.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    Scaffold(
        topBar = {
            TopBar(
                immersive = true,
                onBack = { navActions.back() },
                content = {
                    // 显示用户名作为标题
                    Text(
                        text = userName,
                        style = AppThemeTextStyle.Heading16D,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            // 显示加载状态
            BaseLoading(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            )
        } else {
            // 显示正常内容
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // 用户统计卡片
                UserStatsCard(
                    viewModel = viewModel,
                    onFollowersClick = {
                        userId?.let { navActions.navigateToFollowers(it) }
                    },
                    onFollowingClick = {
                        userId?.let { navActions.navigateToFollowing(it) }
                    }
                )
                
                // 用户内容
                PublicProfileAssets(
                    modifier = Modifier.fillMaxWidth(),
                    navActions = navActions,
                    onClearFeed = onClearFeed,
                    viewModel = viewModel
                )
            }
        }
    }
}
