package one.srp.gensmo.ui.screens.recommend.hashtag._components.text

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import one.srp.gensmo.ui.theme.AppThemeColors

/**
 * A Material 3 Modal Bottom Sheet that displays markdown content with proper formatting.
 * 
 * Features:
 * - Material 3 ModalBottomSheet with proper dismiss functionality
 * - Integrated markdown rendering with scrollable content
 * - Close button and swipe-to-dismiss support
 * - Background dimming and proper z-index layering
 * - Graceful handling of empty or malformed markdown content
 * 
 * @param isVisible Whether the bottom sheet should be displayed
 * @param markdownContent The markdown content to display
 * @param title Optional title for the bottom sheet header
 * @param onDismiss Callback when the bottom sheet is dismissed
 * @param modifier Modifier for the bottom sheet
 * @param sheetState Optional sheet state for controlling the bottom sheet
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MarkdownBottomSheet(
    isVisible: Boolean,
    markdownContent: String,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    title: String? = null,
    sheetState: SheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = false
    )
) {
    if (isVisible) {
        ModalBottomSheet(
            onDismissRequest = onDismiss,
            sheetState = sheetState,
            modifier = modifier,
            containerColor = AppThemeColors.White,
            contentColor = AppThemeColors.Black,
        ) {
            MarkdownBottomSheetContent(
                markdownContent = markdownContent,
                title = title,
                onDismiss = onDismiss,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * Internal composable for the bottom sheet content layout
 */
@Composable
private fun MarkdownBottomSheetContent(
    markdownContent: String,
    title: String?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(bottom = 16.dp)
    ) {
//        // Header with title and close button
//        BottomSheetHeader(
//            title = title,
//            onDismiss = onDismiss,
//            modifier = Modifier.fillMaxWidth()
//        )
        
        // Content area
        BottomSheetMarkdownContent(
            markdownContent = markdownContent,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f, fill = false) // Allow content to take available space but not force expansion
        )
    }
}

/**
 * Header component with optional title and close button
 */
@Composable
private fun BottomSheetHeader(
    title: String?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.padding(horizontal = 16.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Title or spacer
        if (title != null) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )
        } else {
            Spacer(modifier = Modifier.weight(1f))
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // Close button
        IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * Content area that handles markdown rendering with proper error handling
 */
@Composable
private fun BottomSheetMarkdownContent(
    markdownContent: String,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        when {
            markdownContent.isBlank() -> {
                // Empty state
                EmptyContentMessage(
                    message = "No additional information available",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp)
                )
            }
            else -> {
                // Render markdown content with scrolling
                MarkdownText(
                    markdown = markdownContent,
                    scrollable = true,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * Empty state message component
 */
@Composable
private fun EmptyContentMessage(
    message: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Please check back later for updates",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
            )
        }
    }
}