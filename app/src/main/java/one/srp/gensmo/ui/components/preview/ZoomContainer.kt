package one.srp.gensmo.ui.components.preview


import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.rememberTransformableState
import androidx.compose.foundation.gestures.transformable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.launch

@Composable
fun ZoomDialog(
    modifier: Modifier = Modifier,
    preview: (@Composable () -> Unit)? = null,
    previewAlignment: Alignment = Alignment.Center,
    onOpen: (() -> Unit)? = null,
    content: @Composable () -> Unit,
) {
    val previewComp = preview ?: content
    var showPreview by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .clickable { onOpen?.invoke(); showPreview = true }
            .then(modifier)
    ) {
        content()
    }

    if (showPreview) {
        Dialog(
            onDismissRequest = { showPreview = false },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            ),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.surface),
                contentAlignment = previewAlignment,
            ) {
                ZoomBox(onDismiss = { showPreview = false }, contentAlignment = previewAlignment) {
                    previewComp()
                }
            }
        }
    }
}

@Composable
fun ZoomBox(
    modifier: Modifier = Modifier,
    initialScale: Float = 1f,
    minScale: Float = 1f,
    maxScale: Float = 5f,
    onDismiss: () -> Unit = {},
    contentAlignment: Alignment = Alignment.Center,
    content: @Composable () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()

    val scale = remember { Animatable(1f) }
    val translationX = remember { Animatable(0f) }
    val translationY = remember { Animatable(0f) }
    val resetZoom = {
        coroutineScope.launch {
            scale.snapTo(1f)
            translationX.snapTo(0f)
            translationY.snapTo(0f)
        }
    }

    var doubleTapped by remember { mutableStateOf(false) }
    fun doubleTap() {
        if (doubleTapped) return

        doubleTapped = true
        if (scale.value > initialScale) {
            resetZoom()
        } else {
            coroutineScope.launch {
                scale.snapTo((maxScale + minScale) / 2)
                translationX.snapTo(0f)
                translationY.snapTo(0f)
            }
        }
        doubleTapped = false
    }

    BoxWithConstraints(modifier = modifier) {
        val constraints = this.constraints
        val transformableState =
            rememberTransformableState { zoomChange, offsetChange, _ ->
                coroutineScope.launch {
                    scale.snapTo((scale.value * zoomChange).coerceIn(minScale, maxScale))

                    val maxX = (scale.value - 1) * constraints.maxWidth / 2
                    val maxY = (scale.value - 1) * constraints.maxHeight / 2

                    translationX.snapTo(
                        (translationX.value + offsetChange.x * scale.value).coerceIn(
                            -maxX,
                            maxX
                        )
                    )
                    translationY.snapTo(
                        (translationY.value + offsetChange.y * scale.value).coerceIn(
                            -maxY,
                            maxY
                        )
                    )
                }
            }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures(onDoubleTap = { doubleTap() })
                }
                .graphicsLayer(
                    scaleX = scale.value,
                    scaleY = scale.value,
                    translationX = translationX.value,
                    translationY = translationY.value
                )
                .transformable(transformableState),
            contentAlignment = contentAlignment,
        ) {
            content()
        }

        IconButton(
            onClick = onDismiss,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(12.dp)
                .background(MaterialTheme.colorScheme.surface, CircleShape)
        ) {
            Icon(Icons.Default.Close, null)
        }
    }
}
