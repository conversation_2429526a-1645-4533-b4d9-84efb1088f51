package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.core.network.model.FeedCommunityInfo
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun EventBanner(modifier: Modifier = Modifier, item: FeedCommunityInfo, onClick: () -> Unit = {}) {
    Box(
        modifier = Modifier.then(modifier)
    ) {
        Row(
            modifier = Modifier
                .clickable { onClick() }
                .height(36.dp)
                .background(AppThemeColors.Gray800.copy(0.5f))
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(start = 60.dp, end = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item.title?.let { t ->
                val text = item.rank?.let { r -> "$t #$r" } ?: t
                Text(
                    text,
                    style = AppThemeTextStyle.Body14H.copy(AppThemeColors.White),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                )
            }

            item.posts?.let { posts ->
                Text("$posts joined", style = AppThemeTextStyle.Body14H.copy(AppThemeColors.White))
                Icon(Icons.Default.ChevronRight, null, tint = AppThemeColors.White)
            }
        }


        Column(
            modifier = Modifier
                .padding(bottom = 11.dp, start = 16.dp)
                .clickable { onClick() }) {
            item.imageUrl?.let { url ->
                Box(
                    modifier = Modifier.size(32.dp)
                ) {
                    AsyncImage(url, null)
                }
            }
        }
    }
}
