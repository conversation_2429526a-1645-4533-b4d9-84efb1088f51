package one.srp.gensmo.ui.screens.recommend.hashtag._components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

/**
 * A floating action button component for hashtag screens that displays search functionality.
 *
 * This button is positioned at the bottom center of the screen and shows content from
 * hashtagInfo.joinButtonContent. It's only visible when content is available.
 *
 * @param content The text content to display on the button from hashtagInfo.joinButtonContent
 * @param onClick Callback function when the button is clicked
 * @param modifier Modifier for styling and positioning
 */
@Composable
fun HashtagFloatingButton(
    content: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val gradientColors = listOf(
        Color(0xFF6A5AE0), // 紫
        Color(0xFFFF6A88), // 粉
        Color(0xFFFFC371), // 橙
        Color(0xFF00C6FF), // 蓝
        Color(0xFF9D50BB)  // 紫粉
    )

    Box(modifier) {
        Button(
            onClick = onClick,
            shape = MaterialTheme.shapes.extraLarge,
            colors = ButtonDefaults.buttonColors(
                containerColor = AppThemeColors.Black,
                contentColor = AppThemeColors.White,
                disabledContainerColor = AppThemeColors.Gray200,
                disabledContentColor = AppThemeColors.Gray500,
            ),
            contentPadding = PaddingValues(
                horizontal = 20.dp,
                vertical = 16.dp
            ),
            elevation = ButtonDefaults.buttonElevation(
                defaultElevation = 8.dp,
                pressedElevation = 12.dp,
                disabledElevation = 0.dp
            ),
            border = BorderStroke(
                width = 2.dp,
                brush = Brush.linearGradient(
                    colors = gradientColors,
                    start = Offset.Zero,
                    end = Offset.Infinite
                )
            )
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(painterResource(R.drawable.icon_hashtag_button), null)

                Text(
                    text = content,
                    style = AppThemeTextStyle.Body16H,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}