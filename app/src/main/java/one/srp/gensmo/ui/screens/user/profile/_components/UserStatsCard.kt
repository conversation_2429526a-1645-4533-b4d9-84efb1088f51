package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.screens.user.profile._viewmodel.PublicProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.NumberFormatter

@Composable
fun UserStatsCard(
    modifier: Modifier = Modifier,
    viewModel: PublicProfileViewModel,
    onFollowersClick: (() -> Unit)? = null,
    onFollowingClick: (() -> Unit)? = null,
) {
    val userAvatar by viewModel.userAvatar.collectAsState()
    val followingCount by viewModel.followingCount.collectAsState()
    val followerCount by viewModel.followerCount.collectAsState()
    val totalLikes by viewModel.totalLikes.collectAsState()

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        // 用户头像和统计信息行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 用户头像
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                AsyncImage(
                    model = when (userAvatar) {
                        "icon_random_thumb" -> R.drawable.icon_random_thumb
                        "icon_unregister" -> R.drawable.icon_unregister
                        else -> userAvatar
                    },
                    contentDescription = null,
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.Crop,
                    error = painterResource(id = R.drawable.icon_unregister)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 统计数据
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "Following",
                    value = NumberFormatter.format(followingCount),
                    modifier = Modifier.weight(1f),
                    onClick = onFollowingClick
                )
                
                StatItem(
                    label = "Followers",
                    value = NumberFormatter.format(followerCount),
                    modifier = Modifier.weight(1f),
                    onClick = onFollowersClick
                )
                
                StatItem(
                    label = "Likes",
                    value = NumberFormatter.format(totalLikes),
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null
) {
    Column(
        modifier = modifier.then(
            if (onClick != null) {
                Modifier.clickable { onClick() }
            } else {
                Modifier
            }
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = AppThemeTextStyle.Heading16D,
            color = AppThemeColors.Black
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            style = AppThemeTextStyle.Body12LightH,
            color = AppThemeColors.Gray600,
            textAlign = TextAlign.Center
        )
    }
} 