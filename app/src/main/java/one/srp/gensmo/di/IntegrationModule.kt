package one.srp.gensmo.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import one.srp.gensmo.utils.integration.OneSignalManager
import one.srp.gensmo.utils.metrics.MetricManager
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object IntegrationModule {

    @Provides
    @Singleton
    fun provideOneSignalManager(metricManager: MetricManager): OneSignalManager {
        return OneSignalManager(metricManager)
    }
} 