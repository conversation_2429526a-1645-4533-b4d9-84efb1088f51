package one.srp.gensmo.data.repository

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import one.srp.core.network.api.FeedApi
import one.srp.core.network.model.HashtagItem
import one.srp.core.network.model.HashtagSuggestResponse
import one.srp.gensmo.data.repository.utils.parseOrThrow
import timber.log.Timber
import javax.inject.Inject

class HashtagRepository @Inject constructor(
    private val feedApi: FeedApi,
) {
    companion object {
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY_MS = 1000L
        private const val MIN_PREFIX_LENGTH = 1
    }

    /**
     * Fetches hashtag suggestions with retry logic and proper error handling
     * @param prefix The hashtag prefix to search for
     * @return Flow of Result containing list of HashtagItem
     */
    fun getHashtagSuggestions(prefix: String): Flow<Result<List<HashtagItem>>> = flow {
        // Validate input
        if (prefix.isBlank() || prefix.length < MIN_PREFIX_LENGTH) {
            emit(Result.success(emptyList()))
            return@flow
        }

        val trimmedPrefix = prefix.trim()
        Timber.d("Fetching hashtag suggestions for prefix: '$trimmedPrefix'")

        emit(runCatching {
            executeWithRetry(MAX_RETRY_ATTEMPTS) {
                val response = parseOrThrow { feedApi.getHashtagSuggest(trimmedPrefix) }
                parseHashtagSuggestResponse(response)
            }
        })
    }.flowOn(Dispatchers.IO)

    /**
     * Legacy method for backward compatibility - returns full response
     */
    fun getHashtagSuggestionsResponse(prefix: String): Flow<Result<HashtagSuggestResponse>> = flow {
        if (prefix.isBlank()) {
            emit(Result.success(HashtagSuggestResponse(prefix = prefix, limit = null, hashtags = emptyList())))
            return@flow
        }

        val trimmedPrefix = prefix.trim()
        Timber.d("Fetching hashtag suggestions response for prefix: '$trimmedPrefix'")

        emit(runCatching {
            executeWithRetry(MAX_RETRY_ATTEMPTS) {
                parseOrThrow { feedApi.getHashtagSuggest(trimmedPrefix) }
            }
        })
    }.flowOn(Dispatchers.IO)

    /**
     * Parses HashtagSuggestResponse to extract List<HashtagItem>
     */
    private fun parseHashtagSuggestResponse(response: HashtagSuggestResponse): List<HashtagItem> {
        return try {
            val hashtags = response.hashtags
            Timber.d("Parsed ${hashtags.size} hashtag suggestions for prefix: '${response.prefix}'")
            hashtags
        } catch (e: Exception) {
            Timber.e(e, "Error parsing hashtag suggest response")
            throw IllegalStateException("Failed to parse hashtag suggestions", e)
        }
    }

    /**
     * Executes a suspend function with retry logic
     */
    private suspend fun <T> executeWithRetry(
        maxAttempts: Int,
        operation: suspend () -> T
    ): T {
        var lastException: Exception? = null
        
        repeat(maxAttempts) { attempt ->
            try {
                return operation()
            } catch (e: Exception) {
                lastException = e
                Timber.w(e, "Attempt ${attempt + 1}/$maxAttempts failed for hashtag suggestions")
                
                // Don't delay on the last attempt
                if (attempt < maxAttempts - 1) {
                    delay(RETRY_DELAY_MS * (attempt + 1)) // Exponential backoff
                }
            }
        }
        
        // All attempts failed, throw the last exception
        throw lastException ?: IllegalStateException("All retry attempts failed")
    }
}