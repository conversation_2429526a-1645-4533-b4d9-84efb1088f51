package one.srp.gensmo.data.repository.session

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import one.srp.core.network.model.SessionHistoryItem
import one.srp.core.network.api.SessionApi
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SessionHistoryRepository @Inject constructor(
    private val sessionApi: SessionApi
) {
    private val _sessions = MutableStateFlow<List<SessionHistoryItem>>(emptyList())
    val sessions: StateFlow<List<SessionHistoryItem>> = _sessions.asStateFlow()

    private val _hasMore = MutableStateFlow(true)
    val hasMore: StateFlow<Boolean> = _hasMore.asStateFlow()

    private var skip = 0
    private val limit = 10

    suspend fun loadSessions(isRefresh: Boolean = false) {
        if (isRefresh) {
            skip = 0
            _hasMore.value = true
            _sessions.value = emptyList()
        }

        if (!_hasMore.value) return

        try {
            val response = sessionApi.getSessionHistory(
                limit = limit,
                skip = skip,
            )
            if (response.isSuccessful) {
                val body = response.body()!!
                val list = body.sessions
                if (list.isEmpty()) {
                    _hasMore.value = false
                } else {
                    _sessions.value = if (skip == 0) {
                        list
                    } else {
                        _sessions.value + list
                    }
                    skip += list.size
                    _hasMore.value = _sessions.value.size < body.totalCount
                }
            } else {
                Timber.e("获取Session记录失败: ${'$'}{response.message()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "获取Session记录失败")
        }
    }
}
