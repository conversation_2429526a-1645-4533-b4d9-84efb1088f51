package one.srp.gensmo.data.repository

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import one.srp.core.network.model.FeedItem
import one.srp.gensmo.data.remote.UserService
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PublishHistoryRepository @Inject constructor() {
    private val _publishHistory = MutableStateFlow<List<FeedItem>>(emptyList())
    val publishHistory: StateFlow<List<FeedItem>> = _publishHistory.asStateFlow()

    private val _hasMore = MutableStateFlow(true)
    val hasMore: StateFlow<Boolean> = _hasMore.asStateFlow()

    private var currentPage = 1
    private val pageSize = 10

    suspend fun loadPublishHistory(
        isRefresh: Boolean = false,
        targetUserId: String? = null
    ) {
        if (isRefresh) {
            currentPage = 1
            _hasMore.value = true
            _publishHistory.value = emptyList()
        }

        if (!_hasMore.value) return

        try {
            val response = UserService.api.getPublishHistory(
                page = currentPage,
                pageSize = pageSize,
                targetUserId = targetUserId
            )
            if (response.isSuccessful) {
                val body = response.body()!!
                Timber.d("获取发布历史记录成功，数量：${body.data.size}")
                
                if (body.data.isEmpty()) {
                    _hasMore.value = false
                } else {
                    _publishHistory.value = if (currentPage == 1) {
                        body.data
                    } else {
                        _publishHistory.value + body.data
                    }
                    currentPage++
                    _hasMore.value = currentPage <= body.pagination.totalPages
                }
            } else {
                Timber.e("获取发布历史记录失败: ${response.message()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "获取发布历史记录失败")
        }
    }

    suspend fun refreshPublishHistory(targetUserId: String? = null) {
        loadPublishHistory(isRefresh = true, targetUserId = targetUserId)
    }

    suspend fun loadMorePublishHistory(targetUserId: String? = null) {
        loadPublishHistory(isRefresh = false, targetUserId = targetUserId)
    }
}
