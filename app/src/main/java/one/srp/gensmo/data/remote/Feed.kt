package one.srp.gensmo.data.remote

import androidx.paging.PagingSource
import androidx.paging.PagingState
import one.srp.core.network.api.FeedApi
import one.srp.core.network.api.FeedTabType
import one.srp.core.network.model.FeedItem
import one.srp.gensmo.data.remote.utils.RemoteClients
import one.srp.gensmo.viewmodel.hashtag.HashtagFeedType
import timber.log.Timber

object FeedService {
    val api: FeedApi by lazy {
        RemoteClients.recommend.create(FeedApi::class.java)
    }
}

data class PagingKey(
    val offset: Int,
    val lastUpdateTime: String? = null,
)

class FeedPagingSource(
    private val tab: FeedTabType,
    private val uid: String? = null,
    private val deviceId: String? = null,
) : PagingSource<PagingKey, FeedItem>() {
    override suspend fun load(params: LoadParams<PagingKey>): LoadResult<PagingKey, FeedItem> {
        return try {
            val limit = params.loadSize
            val offset = params.key?.offset ?: 0
            val lastUpdateTime = params.key?.lastUpdateTime

            val response = FeedService.api.getFeedList(
                uid = uid,
                deviceId = deviceId,
                tab = tab,
                offset = offset,
                limit = limit,
                lastUpdateTime = lastUpdateTime
            )

            if (response.isSuccessful) {
                val res = response.body() ?: throw Exception("res is null")

                val nextKey = if (res.hasMore && res.data.isNotEmpty()) {
                    PagingKey(offset = offset + limit, lastUpdateTime = res.lastUpdateTime)
                } else {
                    null
                }

                LoadResult.Page(
                    data = res.data, prevKey = null, nextKey = nextKey
                )
            } else {
                LoadResult.Error(Exception(response.message()))
            }
        } catch (e: Exception) {
            Timber.e(e)
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<PagingKey, FeedItem>): PagingKey =
        PagingKey(offset = 0)
}

class HashtagPagingSource(
    private val hashtag: String,
    private val feedType: HashtagFeedType = HashtagFeedType.POPULAR,
) : PagingSource<Int, FeedItem>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FeedItem> {
        return try {
            // 如果 hashtag 为空，直接返回空结果
            if (hashtag.isBlank()) {
                return LoadResult.Page(
                    data = emptyList(),
                    prevKey = null,
                    nextKey = null
                )
            }

            Timber.d("Loading hashtag data for: '$hashtag', feedType: $feedType, offset: ${params.key ?: 0}")

            val offset = params.key ?: 0
            val limit = params.loadSize

            // For now, we use the same endpoint for both feed types
            // In the future, we might need different endpoints or parameters for different sorting
            val response = FeedService.api.getRecommendHashtags(
                hashtags = if (hashtag.startsWith("#")) hashtag else "#$hashtag",
                offset = offset,
                limit = limit,
                order = feedType.apiParam,
            )

            if (response.isSuccessful) {
                val res = response.body() ?: throw Exception("Response body is null")

                val nextKey = if (res.data.isNotEmpty()) {
                    offset + limit
                } else {
                    null
                }

                LoadResult.Page(
                    data = res.data,
                    prevKey = if (offset == 0) null else offset - limit,
                    nextKey = nextKey
                )
            } else {
                LoadResult.Error(Exception(response.message()))
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading hashtag data for: '$hashtag', feedType: $feedType")
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FeedItem>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }
}
