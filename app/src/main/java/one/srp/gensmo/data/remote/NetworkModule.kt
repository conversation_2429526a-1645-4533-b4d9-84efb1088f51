package one.srp.gensmo.data.remote

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import one.srp.core.network.di.ApiHeaderInterceptor
import one.srp.core.network.di.ApiService
import one.srp.core.network.utils.EnvConfig
import one.srp.gensmo.data.remote.interceptors.HeaderInterceptor
import one.srp.gensmo.utils.env.EnvConf
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    @ApiHeaderInterceptor
    fun provideHeaderInterceptor(): Interceptor{
        return HeaderInterceptor()
    }

    @Provides
    @Singleton
    @ApiService
    fun provideEnvConfig(): EnvConfig{
        return EnvConf.config
    }
}