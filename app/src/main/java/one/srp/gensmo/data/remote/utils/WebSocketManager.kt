package one.srp.gensmo.data.remote.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import timber.log.Timber

enum class WebSocketConnectionStatus {
    IDLE, CONNECTING, CONNECTED, DISCONNECTED
}

class WebSocketManager(private val coroutineScope: CoroutineScope) {
    private val client = HttpClients.websocket
    private var webSocket: WebSocket? = null

    val connectionStatus = MutableStateFlow(WebSocketConnectionStatus.IDLE)

    val messages = MutableStateFlow<List<String>>(emptyList())

    private val requestObj = MutableStateFlow<Request?>(null)
    private val onOpenFn = MutableStateFlow<(() -> Unit)> {}
    private val onCloseFn = MutableStateFlow<(() -> Unit)> {}
    private val onErrorFn = MutableStateFlow<((Throwable) -> Unit)> {}
    private val onMessageFn = MutableStateFlow<((String) -> Unit)> {}

    fun connectWebSocket(
        request: Request,
        force: Boolean = false,
        onOpen: (() -> Unit) = {},
        onClose: (() -> Unit) = {},
        onError: ((Throwable) -> Unit) = {},
        onMessage: ((String) -> Unit) = {},
    ): WebSocket? {
        requestObj.value = request
        onOpenFn.value = onOpen
        onCloseFn.value = onClose
        onErrorFn.value = onError
        onMessageFn.value = onMessage

        if (webSocket != null && !force) return webSocket

        connectionStatus.value = WebSocketConnectionStatus.CONNECTING

        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                coroutineScope.launch {
                    connectionStatus.value = WebSocketConnectionStatus.CONNECTED
                    Timber.d("[WebSocket] Connected.")
                    onOpen()
                }
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                coroutineScope.launch {
                    connectionStatus.value = WebSocketConnectionStatus.DISCONNECTED
                    Timber.d("[WebSocket] Closed: $reason")
                    onClose()
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                coroutineScope.launch {
                    connectionStatus.value = WebSocketConnectionStatus.DISCONNECTED
                    Timber.d("[WebSocket] Error: $t")
                    onError(t)
                }
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                coroutineScope.launch {
                    messages.value = messages.value + text
                    Timber.d("[WebSocket] Received: $text")
                    onMessage(text)
                }
            }
        })

        return webSocket
    }

    fun sendMessage(message: String, onSuccess: () -> Unit = {}, onFail: () -> Unit = {}) {
        val flag = webSocket?.send(message)

        if (flag == true) {
            coroutineScope.launch {
                Timber.d("[WebSocket] Sent: $message")
                messages.value = messages.value + message
                onSuccess()
            }
        } else {
            onFail()
        }
    }

    fun disconnect() {
        connectionStatus.value = WebSocketConnectionStatus.DISCONNECTED
        webSocket?.close(1000, "User closed connection")
        webSocket = null
    }

    fun clearMessages() {
        coroutineScope.launch {
            messages.value = emptyList()
        }
    }

    fun clear() {
        disconnect()
        clearMessages()
    }
}