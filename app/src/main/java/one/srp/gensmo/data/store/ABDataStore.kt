package one.srp.gensmo.data.store

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import one.srp.core.network.model.ABConfigItem
import one.srp.core.network.utils.JSON
import one.srp.gensmo.data.remote.AbService
import retrofit2.Response
import timber.log.Timber

private val Context.abDataStore: DataStore<Preferences> by preferencesDataStore(name = "user_ab_data")

private object ABPreferencesKeys {
    val LOCAL_AB_CONFIG = stringPreferencesKey("local_ab_test_config")
}

object ABDataStoreManager {
    private var dataStore: DataStore<Preferences>? = null
    private val _abOnlineConfig = MutableStateFlow<List<ABConfigItem>>(emptyList())
    val abOnlineConfig: StateFlow<List<ABConfigItem>> = _abOnlineConfig.asStateFlow()
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    fun initialize(context: Context) {
        dataStore = context.abDataStore
        scope.launch {
            requestABTest()
        }
    }

    suspend fun requestABTest() {
        try {
            val response: Response<List<ABConfigItem>> = AbService.api.getAllABConfig()
            if (response.isSuccessful) {
                val config = response.body() ?: emptyList()
                _abOnlineConfig.value = config
                Timber.d("AB config: $config")
            } else {
                Timber.e("Failed to get AB config: ${response.code()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to get AB config")
        }
    }

    suspend fun saveLocalABTest(config: List<Map<String, String>>) {
        val jsonString = JSON.encodeToString(config)
        dataStore?.edit { preferences ->
            preferences[ABPreferencesKeys.LOCAL_AB_CONFIG] = jsonString
        }
    }

    suspend fun getLocalABTest(): List<Map<String, String>> {
        val jsonString = dataStore?.data?.map { preferences ->
            preferences[ABPreferencesKeys.LOCAL_AB_CONFIG]
        }?.first()
        return if (!jsonString.isNullOrEmpty()) {
            try {
                JSON.decodeFromString<List<Map<String, String>>>(jsonString)
            } catch (e: Exception) {
                Timber.e(e, "Failed to parse local AB config")
                emptyList()
            }
        } else {
            emptyList()
        }
    }

    fun getOnlineABTest(): List<Map<String, String>> {
        return abOnlineConfig.value.map {
            mapOf(
                "project" to it.project,
                "router" to it.router
            )
        }
    }

    suspend fun getABTestConfig(): List<Map<String, String>> {
        try {
            val localConfig = getLocalABTest()
            val onlineConfig = getOnlineABTest().toMutableList()
            // 合并本地配置，覆盖同名 project
            for (localItem in localConfig) {
                val project = localItem["project"]
                if (project != null) {
                    onlineConfig.removeAll { it["project"] == project }
                    onlineConfig.add(localItem)
                }
            }
            return onlineConfig
        } catch (e: Exception) {
            Timber.w(e)
            return emptyList()
        }
    }

    suspend fun isABTestEnabled(abTestName: String): String {
        val config = getABTestConfig()
        val abConfig = config.firstOrNull { it["project"] == abTestName }
        return abConfig?.get("router") ?: "rest"
    }

    suspend fun getABTestConfigJsonString(): String {
        val config = getABTestConfig()
        return try {
            JSON.encodeToString(config)
        } catch (e: Exception) {
            Timber.e(e, "Failed to encode AB config to JSON")
            "[]"
        }
    }

    fun getTrackerABTestConfigJsonString(): String {
        val config = abOnlineConfig.value.mapNotNull { it.uniqueId }
        return try {
            JSON.encodeToString(config)
        } catch (e: Exception) {
            Timber.e(e, "Failed to encode tracker AB config to JSON")
            "[]"
        }
    }

    // 业务开关示例
    suspend fun isTryOnEnable(): Boolean = true // 可根据实际需求调整

    suspend fun testABEnable(): Boolean {
        return isABTestEnabled("bryce_test") == "test"
    }

    suspend fun closetFakeButtonShowEnable(): Boolean {
        return isABTestEnabled("FV_show_closet_fake_button_enable") == "closet"
    }

    // 其他业务开关可按需添加
} 