package one.srp.gensmo.data.remote.interceptors

import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import one.srp.gensmo.data.store.ABDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.env.MetaInfo
import one.srp.utils.datetime.getCurrMicros

class HeaderInterceptor() : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        val deviceId = runBlocking { DeviceDataStoreManager.getDeviceId() }
        val version = MetaInfo.VERSION
        val source = MetaInfo.PLATFORM
        val token = runBlocking { UserDataStoreManager.getToken() }
        val abInfo = runBlocking { ABDataStoreManager.getABTestConfigJsonString() }
        val timestamp = getCurrMicros().toString()

        val newRequest = originalRequest.newBuilder().apply {
            header("f-version", version)
            header("f-source", source)
            header("f-ab-info", abInfo)
            header("Deviceid", deviceId)
            header("X-Gensmo-Active-Id", deviceId)
            header("X-Gensmo-Timestamp", timestamp)
            if (token != null) {
                header("Authorization", "Bearer $token")
            }
        }.build()

        return chain.proceed(newRequest)
    }
}
