package one.srp.gensmo.data.repository

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import one.srp.core.network.model.HomePageInfo
import one.srp.core.network.api.MiscApi
import one.srp.gensmo.data.repository.utils.parseOrThrow
import javax.inject.Inject

class MiscRepository @Inject constructor(
    private val api: MiscApi,
) {
    fun getHomepageInfo(): Flow<Result<HomePageInfo>> = flow {
        emit(runCatching {
            parseOrThrow { api.getHomepageInfo() }
        })
    }.flowOn(Dispatchers.IO)
}
