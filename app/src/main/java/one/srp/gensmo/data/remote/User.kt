package one.srp.gensmo.data.remote

import androidx.paging.PagingSource
import androidx.paging.PagingState
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.SavedItem
import one.srp.core.network.api.UserApi
import one.srp.gensmo.data.remote.utils.RemoteClients
import timber.log.Timber

object UserService {
    val api: UserApi by lazy {
        RemoteClients.workflow.create(UserApi::class.java)
    }
}

data class SavedPagingKey(
    val page: Int,
)

class SavedPagingSource() : PagingSource<SavedPagingKey, SavedItem>() {
    override suspend fun load(params: LoadParams<SavedPagingKey>): LoadResult<SavedPagingKey, SavedItem> {
        return try {
            val limit = params.loadSize
            val page = params.key?.page ?: 1

            val response = UserService.api.getSaved(
                page = page,
                limit = limit,
            )
            val res = response.body() ?: throw Exception("res is null")

            val hasMore = res.pagination?.totalPages?.let { it > page } ?: run { false }

            val nextKey = if (hasMore) {
                SavedPagingKey(page = page + 1)
            } else {
                null
            }

            LoadResult.Page(
                data = res.data, prevKey = null, nextKey = nextKey
            )
        } catch (e: Exception) {
            Timber.e(e)
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<SavedPagingKey, SavedItem>): SavedPagingKey =
        SavedPagingKey(page = 1)
}

data class PublishHistoryPagingKey(
    val page: Int,
)

class PublishHistoryPagingSource(
    private val targetUserId: String? = null,
) : PagingSource<PublishHistoryPagingKey, FeedItem>() {
    override suspend fun load(params: LoadParams<PublishHistoryPagingKey>): LoadResult<PublishHistoryPagingKey, FeedItem> {
        return try {
            val limit = params.loadSize
            val page = params.key?.page ?: 1

            val response = UserService.api.getPublishHistory(
                page = page,
                pageSize = limit,
                targetUserId = targetUserId
            )
            val res = response.body() ?: throw Exception("res is null")

            val hasMore = res.pagination.totalPages > page

            val nextKey = if (hasMore) {
                PublishHistoryPagingKey(page = page + 1)
            } else {
                null
            }

            LoadResult.Page(
                data = res.data, prevKey = null, nextKey = nextKey
            )
        } catch (e: Exception) {
            Timber.e(e)
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<PublishHistoryPagingKey, FeedItem>): PublishHistoryPagingKey =
        PublishHistoryPagingKey(page = 1)
}
