package one.srp.gensmo.data.remote

import okhttp3.RequestBody
import one.srp.core.network.model.SearchInspoRes
import one.srp.gensmo.data.remote.utils.RemoteClients
import retrofit2.Response
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query

object SuggestService {
    val api: SuggestServiceApi by lazy {
        RemoteClients.workflow.create(SuggestServiceApi::class.java)
    }
}

interface SuggestServiceApi {
    @Multipart
    @POST("/image_search_camera_extension")
    suspend fun getInspoList(
        @Part("query") query: RequestBody,
        @Query("image_url") imageUrl: String? = null,
    ): Response<SearchInspoRes>
}
