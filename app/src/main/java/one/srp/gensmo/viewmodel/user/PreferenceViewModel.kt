package one.srp.gensmo.viewmodel.user

import android.content.Context
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.lifecycle.ViewModel
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeWebView
import com.smallbuer.jsbridge.core.CallBackFunction
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.utils.bridge.JSBridgeManager
import one.srp.gensmo.utils.env.EnvConf
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject

/**
 * 历史页面的 ViewModel
 * 负责管理历史页面的状态和业务逻辑
 */
@HiltViewModel
class PreferenceViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val jsBridgeManager: JSBridgeManager
) : ViewModel() {
    
    // 使用弱引用来避免内存泄漏
    private var webViewRef: WeakReference<BridgeWebView>? = null
    
    init {
        Timber.d("HistoryViewModel 已初始化")
    }
    
    // 创建并初始化WebView
    fun createWebView(): BridgeWebView {
        webViewRef?.get()?.let {
            return it
        }
        
        val newWebView = BridgeWebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.useWideViewPort = true
            settings.loadWithOverviewMode = true
            settings.cacheMode = WebSettings.LOAD_DEFAULT
        }
        webViewRef = WeakReference(newWebView)
        Timber.d("WebView已在ViewModel中创建")
        return newWebView
    }

    fun getWebView(): BridgeWebView? {
        return webViewRef?.get()
    }
    
    // 初始化JSBridge并加载URL
    fun initializeWebView(navActions: NavActions, collageSearchViewModel: CollageSearchViewModel, source: String, onInitialized: () -> Unit) {
        val webView = createWebView()
        webView.addHandlerLocal( "saveSuccessfully", SaveSuccessHandler(navActions, collageSearchViewModel, source))
        jsBridgeManager.apply {
            Timber.d("JSBridgeManager配置已更新")
            
            init(webView) {
                val baseUrl = EnvConf.origin
                Timber.d("loadUrl: $baseUrl/user/preference?click_src=$source")
                webView.loadUrl("$baseUrl/user/preference?click_src=$source")
                Timber.d("偏好设置页面已加载")
                onInitialized()
            }
        }
    }

     private inner class SaveSuccessHandler(
        private val navActions: NavActions,
        private val collageSearchViewModel: CollageSearchViewModel,
        private val source: String
     ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            try {
                if (source == "collage") {
                     val query = collageSearchViewModel.query.value
                     val imageUrl = collageSearchViewModel.imageURI.value
                     navActions.navigateFromPreferenceToCollageSearch(query = query, imageUrl = imageUrl)
                } else {
                     navActions.back()
                }
                function?.onCallBack("success")
            } catch (e: Exception) {
                Timber.e(e, "处理保存成功返回操作失败")
                function?.onCallBack("error")
            }
        }
    }
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        webViewRef?.clear()
        webViewRef = null
        jsBridgeManager.release()
        Timber.d("PreferenceViewModel已清除，WebView引用已释放")
    }
}
