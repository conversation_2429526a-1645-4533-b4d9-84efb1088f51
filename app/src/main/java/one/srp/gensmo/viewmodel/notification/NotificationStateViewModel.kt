package one.srp.gensmo.viewmodel.notification

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import one.srp.gensmo.data.repository.NotificationRepository
import javax.inject.Inject

@HiltViewModel
class NotificationStateViewModel @Inject constructor(
    notificationRepository: NotificationRepository
) : ViewModel() {

    // 监听未读通知数量变化
    val unreadCount: StateFlow<Int> = notificationRepository.unreadCount
} 