package one.srp.gensmo.viewmodel.navigation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.SearchItem
import one.srp.core.network.model.TryOnParams
import one.srp.core.network.model.TryOnTaskItem
import javax.inject.Inject

@HiltViewModel
class SharedNavViewModel @Inject constructor() : ViewModel() {
    private var _feedItem = MutableStateFlow<FeedItem?>(null)
    var feedItem = _feedItem.asStateFlow()
    fun updateFeedItem(item: FeedItem?) {
        viewModelScope.launch {
            _feedItem.emit(item)
        }
    }

    private var _tryOnParams = MutableStateFlow<TryOnParams?>(null)
    var tryOnParams = _tryOnParams.asStateFlow()
    fun updateTryOnParams(item: TryOnParams?) {
        viewModelScope.launch {
            _tryOnParams.emit(item)
        }
    }

    private val _searchItemMap = mutableMapOf<String, SearchItem>()
    fun getSearchItem(key: String): SearchItem? {
        return _searchItemMap[key]
    }

    fun putSearchItem(key: String, item: SearchItem) {
        _searchItemMap[key] = item
    }

    private val _tryOnTaskItemMap = mutableMapOf<String, TryOnTaskItem>()
    fun getTryOnTaskItem(key: String): TryOnTaskItem? {
        return _tryOnTaskItemMap[key]
    }

    fun putTryOnTaskItem(key: String, item: TryOnTaskItem) {
        _tryOnTaskItemMap[key] = item
    }

    fun clear() {
        viewModelScope.launch {
            updateFeedItem(null)
            updateTryOnParams(null)

            _searchItemMap.clear()
            _tryOnTaskItemMap.clear()
        }
    }
}
