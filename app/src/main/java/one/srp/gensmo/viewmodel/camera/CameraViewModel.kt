package one.srp.gensmo.viewmodel.camera

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import one.srp.gensmo.R

data class CameraMode(
    val title: String,
    val iconResId: Int,
    val query: String
)

class CameraViewModel : ViewModel() {
    
    private val cameraModes = listOf(
        CameraMode("FIND SIMILAR", R.drawable.icon_match, "Find similar style for this item"),
        CameraMode("STYLE THIS PIECE", R.drawable.icon_outfit, "Outfit styling for this piece"),
        CameraMode("GENERAL", R.drawable.icon_query, ""),
    )
    
    var selectedMode by mutableStateOf(cameraModes.last())  // 默认选择 GENERAL
        private set
        
    fun selectMode(mode: CameraMode) {
        selectedMode = mode
    }
    
    fun getCameraModes() = cameraModes
} 