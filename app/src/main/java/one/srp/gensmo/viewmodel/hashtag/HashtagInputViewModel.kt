package one.srp.gensmo.viewmodel.hashtag

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.model.HashtagItem
import one.srp.gensmo.data.repository.HashtagRepository
import timber.log.Timber
import javax.inject.Inject

data class HashtagInputUiState(
    val inputText: String = "",
    val suggestions: List<HashtagItem> = emptyList(),
    val selectedHashtags: List<HashtagItem> = emptyList(),
    val isLoadingSuggestions: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class HashtagInputViewModel @Inject constructor(
    private val hashtagRepository: HashtagRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HashtagInputUiState())
    val uiState: StateFlow<HashtagInputUiState> = _uiState.asStateFlow()
    
    private var suggestionJob: Job? = null
    private val debounceDelayMs = 300L
    
    /**
     * Updates the input text and triggers debounced suggestion fetching
     */
    fun updateInputText(text: String) {
        _uiState.value = _uiState.value.copy(inputText = text)
        
        // Cancel previous suggestion job
        suggestionJob?.cancel()
        
        if (text.isBlank()) {
            clearSuggestions()
            return
        }
        
        // Start new debounced suggestion fetching
        suggestionJob = viewModelScope.launch {
            delay(debounceDelayMs)
            fetchSuggestions(text)
        }
    }
    
    /**
     * Selects a hashtag from suggestions and adds it to selected hashtags
     */
    fun selectHashtag(hashtag: HashtagItem, maxHashtags: Int = 10) {
        val currentSelected = _uiState.value.selectedHashtags
        
        // Check if maximum hashtags limit is reached
        if (currentSelected.size >= maxHashtags) {
            _uiState.value = _uiState.value.copy(
                error = "Maximum $maxHashtags hashtags allowed."
            )
            return
        }
        
        // Check if hashtag is already selected to prevent duplicates
        if (currentSelected.any { it.hashtag == hashtag.hashtag }) {
            _uiState.value = _uiState.value.copy(
                error = "Hashtag already selected."
            )
            return
        }
        
        val updatedSelected = currentSelected + hashtag
        _uiState.value = _uiState.value.copy(
            selectedHashtags = updatedSelected,
            inputText = "", // Clear input after selection
            suggestions = emptyList(), // Clear suggestions after selection
            error = null // Clear any previous error
        )
    }
    
    /**
     * Removes a hashtag from selected hashtags
     */
    fun removeHashtag(hashtag: HashtagItem) {
        val currentSelected = _uiState.value.selectedHashtags
        val updatedSelected = currentSelected.filter { it.hashtag != hashtag.hashtag }
        _uiState.value = _uiState.value.copy(selectedHashtags = updatedSelected)
    }
    
    /**
     * Adds a custom hashtag from the input text
     */
    fun addCustomHashtag(text: String, maxHashtags: Int = 10) {
        val trimmedText = text.trim()
        if (trimmedText.isBlank()) return
        
        val currentSelected = _uiState.value.selectedHashtags
        
        // Check if maximum hashtags limit is reached
        if (currentSelected.size >= maxHashtags) {
            _uiState.value = _uiState.value.copy(
                error = "Maximum $maxHashtags hashtags allowed."
            )
            return
        }
        
        // Format hashtag (add # if not present)
        val formattedHashtag = if (trimmedText.startsWith("#")) {
            trimmedText
        } else {
            "#$trimmedText"
        }
        
        // Validate hashtag format (no spaces, basic validation)
        if (!isValidHashtag(formattedHashtag)) {
            _uiState.value = _uiState.value.copy(
                error = "Invalid hashtag format. Hashtags cannot contain spaces or special characters."
            )
            return
        }
        
        // Check if hashtag is already selected
        if (currentSelected.any { it.hashtag == formattedHashtag }) {
            _uiState.value = _uiState.value.copy(
                error = "Hashtag already selected."
            )
            return
        }
        
        // Create custom hashtag item
        val customHashtag = HashtagItem(
            hashtag = formattedHashtag,
            frequency = null,
            iconUrl = null
        )
        
        val updatedSelected = currentSelected + customHashtag
        _uiState.value = _uiState.value.copy(
            selectedHashtags = updatedSelected,
            inputText = "", // Clear input after adding
            suggestions = emptyList(), // Clear suggestions
            error = null // Clear any previous error
        )
    }
    
    /**
     * Clears all suggestions
     */
    fun clearSuggestions() {
        _uiState.value = _uiState.value.copy(
            suggestions = emptyList(),
            isLoadingSuggestions = false
        )
    }
    
    /**
     * Clears any error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * Sets initial selected hashtags (for default hashtags support)
     * This method properly initializes the component with default HashtagItem objects
     * and ensures they can be removed using the same removal mechanism
     */
    fun setInitialHashtags(hashtags: List<HashtagItem>) {
        // Clear any existing error when setting initial hashtags
        _uiState.value = _uiState.value.copy(
            selectedHashtags = hashtags,
            error = null
        )
    }
    
    /**
     * Fetches hashtag suggestions from the API with comprehensive error handling
     */
    private fun fetchSuggestions(prefix: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoadingSuggestions = true,
                error = null
            )
            
            hashtagRepository.getHashtagSuggestions(prefix).collect { result ->
                result.fold(
                    onSuccess = { hashtags ->
                        _uiState.value = _uiState.value.copy(
                            suggestions = hashtags,
                            isLoadingSuggestions = false,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Error fetching hashtag suggestions for prefix: '$prefix'")
                        val errorMessage = getErrorMessage(exception)
                        _uiState.value = _uiState.value.copy(
                            suggestions = emptyList(),
                            isLoadingSuggestions = false,
                            error = errorMessage
                        )
                    }
                )
            }
        }
    }
    
    /**
     * Converts exceptions to user-friendly error messages
     */
    private fun getErrorMessage(exception: Throwable): String {
        return when (exception) {
            is java.net.UnknownHostException -> "No internet connection. Please check your network and try again."
            is java.net.SocketTimeoutException -> "Request timed out. Please try again."
            is java.net.ConnectException -> "Unable to connect to server. Please try again later."
            is java.io.IOException -> "Network error occurred. Please check your connection."
            is IllegalStateException -> {
                if (exception.message?.contains("Failed to parse") == true) {
                    "Unable to process suggestions. You can still add hashtags manually."
                } else {
                    "Something went wrong. You can still add hashtags manually."
                }
            }
            else -> {
                val message = exception.message
                if (message.isNullOrBlank()) {
                    "Unable to load suggestions. You can still add hashtags manually."
                } else {
                    "Unable to load suggestions: $message"
                }
            }
        }
    }
    
    /**
     * Retries fetching suggestions for the current input text
     */
    fun retrySuggestions() {
        val currentInput = _uiState.value.inputText
        if (currentInput.isNotBlank()) {
            // Cancel any existing job and start fresh
            suggestionJob?.cancel()
            suggestionJob = viewModelScope.launch {
                fetchSuggestions(currentInput)
            }
        }
    }
    
    /**
     * Validates hashtag format
     */
    private fun isValidHashtag(hashtag: String): Boolean {
        // Basic validation: starts with #, no spaces, no special characters except underscore
        val pattern = Regex("^#[a-zA-Z0-9_]+$")
        return pattern.matches(hashtag)
    }
}