package one.srp.gensmo.viewmodel.tryon

import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeWebView
import com.smallbuer.jsbridge.core.CallBackFunction
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.data.remote.ImageService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.utils.bridge.JSBridgeManager
import one.srp.gensmo.utils.env.EnvConf
import one.srp.gensmo.utils.metrics.MetricManager
import org.json.JSONObject
import timber.log.Timber
import java.io.File
import java.lang.ref.WeakReference
import javax.inject.Inject

/**
 * 历史页面的 ViewModel
 * 负责管理历史页面的状态和业务逻辑
 */
@HiltViewModel
class ModelSelectViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val jsBridgeManager: JSBridgeManager,
    private val metricManager: MetricManager,
) : ViewModel() {

    private val _showCamera = MutableStateFlow(false)
    val showCamera: StateFlow<Boolean> = _showCamera.asStateFlow()

    // 使用弱引用来避免内存泄漏
    private var webViewRef: WeakReference<BridgeWebView>? = null

    private var currentCallback: CallBackFunction? = null

    init {
        Timber.d("ModelSelectViewModel 已初始化")
    }

    fun openCamera() {
        _showCamera.value = true
    }

    fun closeCamera() {
        _showCamera.value = false
    }

    fun onPhotoTaken(uri: Uri) {
        Timber.d("onPhotoTaken: $uri")
        viewModelScope.launch {
            try {
                val uploadedUrl = uploadImage(uri.toString())
                val responseJson = JSONObject().apply {
                    put("modelImageURL", uploadedUrl)
                }
                currentCallback?.onCallBack(responseJson.toString())
                currentCallback = null
            } catch (e: Exception) {
                Timber.e(e, "上传照片失败")
                currentCallback?.onCallBack("error")
            }
        }
        closeCamera()
    }

    suspend fun uploadImage(uri: String): String {
        val uri = uri.toUri()
        val file = if (uri.scheme == "file") {
            File(uri.path!!)
        } else {
            File(uri.toString())
        }

        try {
            // 获取图片宽高
            val options = android.graphics.BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            android.graphics.BitmapFactory.decodeFile(file.absolutePath, options)
            val width = options.outWidth
            val height = options.outHeight

            // 获取预签名URL
            val presignedResponse = ImageService.api.getPresignedUrl(
                purpose = "user_upload",
                width = width,
                height = height
            )

            if (!presignedResponse.isSuccessful) {
                throw Exception("获取预签名URL失败: ${presignedResponse.errorBody()?.string()}")
            }

            val presignedData = presignedResponse.body()
                ?: throw Exception("预签名数据为空")

            // 使用预签名URL上传图片
            val uploadSuccess = ImageService.uploadWithPresignedUrl(
                presignedUrl = presignedData.presignedUrl,
                file = file
            )

            if (!uploadSuccess) {
                throw Exception("使用预签名URL上传图片失败")
            }

            // 返回公共URL
            val uploadedUrl = presignedData.publicUrl
            Timber.d("uploadedImageUrl: $uploadedUrl")
            return uploadedUrl
        } catch (e: Exception) {
            Timber.e(e, "上传图片过程中发生错误")
            throw e
        }
    }


    // 创建并初始化WebView
    fun createWebView(): BridgeWebView {
        webViewRef?.get()?.let {
            return it
        }

        val newWebView = BridgeWebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.useWideViewPort = true
            settings.loadWithOverviewMode = true
            settings.cacheMode = WebSettings.LOAD_DEFAULT
        }
        webViewRef = WeakReference(newWebView)
        Timber.d("WebView已在ViewModel中创建")
        return newWebView
    }

    fun getWebView(): BridgeWebView? {
        return webViewRef?.get()
    }

    // 初始化JSBridge并加载URL
    fun initializeWebView(
        navActions: NavActions,
        setFace: String?,
        userPreference: String?,
        onInitialized: () -> Unit,
    ) {
        val webView = createWebView()
        webView.addHandlerLocal("getAppVersion", AppVersionHandler())
        webView.addHandlerLocal("getParams", ParamsHandler())
        webView.addHandlerLocal("onPreferenceClick", GoPreferenceHandler(navActions))
        webView.addHandlerLocal("TryOnConfirmClick", ConfirmHandler(navActions))
        webView.addHandlerLocal("addFaceClick", FaceClickHandler())
        webView.addHandlerLocal("getReplicaInfo", UserPreferenceHandler(userPreference))
        // webView.addHandlerLocal( "saveSuccessfully", SaveSuccessHandler(navActions))
        jsBridgeManager.apply {
            Timber.d("JSBridgeManager配置已更新")

            init(webView) {
                val baseUrl = EnvConf.origin
                val url = if (setFace.isNullOrEmpty()) {
                    "$baseUrl/try-on/model/*"
                } else {
                    "$baseUrl/try-on/model/*?setface=$setFace"
                }
                Timber.d("loadUrl: $url")
                webView.loadUrl(url)
                Timber.d("偏好设置页面已加载")
                onInitialized()
            }
        }
    }

    private inner class AppVersionHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("AppVersionHandler已触发")
            val appVersion = try {
                val packageInfo = context?.packageManager?.getPackageInfo(context.packageName, 0)
                packageInfo?.versionName ?: "unknown"
            } catch (e: PackageManager.NameNotFoundException) {
                Timber.e(e, "获取应用版本号失败")
                "unknown"
            }

            function?.onCallBack(appVersion)
            Timber.d("返回应用版本: $appVersion")
        }
    }

    private inner class ConfirmHandler(
        private val navActions: NavActions,
    ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("ConfirmHandler已触发")
            try {
                data?.let {
                    val jsonData = JSONObject(it)
                    val modelId = jsonData.getString("modelId")
                    val modelImageURL = jsonData.getString("modelImageURL")
                    function?.onCallBack("success")

                    viewModelScope.launch {
                        // 埋点：模特选择页(web)确认按键点击触发avatar生成
                        metricManager.log(
                            SelectItem(
                                itemListName = EventItemListName.ConfirmBtn,
                                method = EventMethod.Click,
                                actionType = EventActionType.AvatarGen,
                                refer = EventRefer.SelectModel,
                                items = listOf(
                                    EventItem(
                                        itemCategory = EventItemCategory.Avatar,
                                        itemId = modelId,
                                        itemName = modelImageURL
                                    )
                                )
                            )
                        )

                        UserDataStoreManager.saveModelInfo(modelImageURL, modelId)
                        Timber.d("已保存模型信息 - ID: $modelId, URL: $modelImageURL")
                        navActions.popBackStack()
                        navActions.popBackStack()
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "解析模型信息失败")
                function?.onCallBack("error")
                navActions.navigateToFeedRecommend()

            }
        }
    }

    private inner class GoPreferenceHandler(
        private val navActions: NavActions,
    ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("GoPreferenceHandler已触发")
            navActions.navigateToPreference("tryon")
            Timber.d("返回参数: $data")
            function?.onCallBack("success")
        }
    }

    private inner class FaceClickHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("FaceClickHandler已触发")
            try {
                // 埋点：模特选择页(web)设置面部按键点击
                viewModelScope.launch {
                    metricManager.log(
                        SelectItem(
                            itemListName = EventItemListName.SetFaceBtn,
                            method = EventMethod.Click,
                            refer = EventRefer.SelectModel
                        )
                    )
                }
                currentCallback = function
                openCamera()
            } catch (e: Exception) {
                Timber.e(e, "解析modelId失败")
            }
        }
    }

    private inner class ParamsHandler(
        private val params: JSONObject = JSONObject().apply {
            put("task_id", "*")
            put("click_src", "*")
        },
    ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("ParamsHandler已触发")
            function?.onCallBack(params.toString())
        }
    }

    private inner class UserPreferenceHandler(
        private val userPreference: String? = null,
    ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("UserPreferenceHandler已触发")
            function?.onCallBack(userPreference)
        }
    }


    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        webViewRef?.clear()
        webViewRef = null
        jsBridgeManager.release()
        Timber.d("PreferenceViewModel已清除，WebView引用已释放")
    }
}
