package one.srp.gensmo.viewmodel.feed

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.runBlocking
import one.srp.core.network.model.FeedItem
import one.srp.gensmo.data.remote.FeedPagingSource
import one.srp.core.network.api.FeedTabType
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import javax.inject.Inject

@HiltViewModel
class FeedTabsViewModel @Inject constructor() : ViewModel() {
    var selectedTab by mutableIntStateOf(0)
        private set

    fun onSelectTab(index: Int) {
        selectedTab = index
    }

    private val feedPagingFlows = mutableMapOf<FeedTabType, Flow<PagingData<FeedItem>>>()

    fun getPagingFlow(tab: FeedTabType): Flow<PagingData<FeedItem>> {
        val feedTabPagingFlow = feedPagingFlows.getOrPut(tab) {
            val uid = runBlocking { UserDataStoreManager.getUserId() }
            val deviceId = runBlocking { DeviceDataStoreManager.getDeviceId() }

            Pager(
                config = PagingConfig(
                    pageSize = 10, enablePlaceholders = false
                ), pagingSourceFactory = { FeedPagingSource(tab, uid, deviceId) }).flow.cachedIn(
                viewModelScope
            )
        }
        return feedTabPagingFlow
    }
}
