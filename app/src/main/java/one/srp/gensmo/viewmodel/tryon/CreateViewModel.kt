package one.srp.gensmo.viewmodel.tryon

import android.app.Activity
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import one.srp.gensmo.utils.auth.AuthManager
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import android.content.Context
import one.srp.gensmo.R
import androidx.lifecycle.viewModelScope
import one.srp.gensmo.data.store.UserDataStoreManager
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.io.File
import one.srp.gensmo.data.remote.ImageService
import one.srp.gensmo.data.remote.TryOnService
import androidx.core.net.toUri
import one.srp.core.network.model.ValidateReplicaFaceParams
import one.srp.core.network.model.GenerateReplicaFaceParams
import one.srp.core.network.model.UserPreferenceForReplica
import one.srp.gensmo.ui.screens.tryon.create._components.BodyTypeOption
import one.srp.gensmo.ui.screens.tryon.create._components.BodyShapeOption
import one.srp.gensmo.utils.integration.trackEventOnSentry
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import android.graphics.BitmapFactory
import androidx.exifinterface.media.ExifInterface


data class CreateUiState(
    val currentProgress: Float = 0f,  // 默认进度值，可根据需要调整
    val isLoading: Boolean = false,
    val selectedImageUri: String? = null,
    val uploadedImageUrl: String? = null,  // 添加新的状态字段
    val isReplicaFaceValid: Boolean = false,
    val isPhotoUploadedAndValidatedCompleted: Boolean = false,
    val isGenerating: Boolean = false,
    // 添加新的状态字段
    val showPrivacyDialog: Boolean = false,
    val showLoginDialog: Boolean = false,
    val showCameraPicker: Boolean = false,
    val bodyType: String = "AVERAGE",
    val bodyShape: String = "TRIANGLE",
    val additionalInfo: String = "",
    val replicaTaskId: String? = null,
    val isBackButtonDisabled: Boolean = false,  // 添加这一行
    val faceDetectionPassed: Boolean = false,  // 添加人脸检测成功标记
)

@HiltViewModel
class CreateViewModel @Inject constructor(
    private val authManager: AuthManager,
    private val asyncTaskManager: AsyncTaskManager
) : ViewModel() {
    private val _uiState = MutableStateFlow(CreateUiState())
    val uiState: StateFlow<CreateUiState> = _uiState.asStateFlow()
    
    private var loginSource: String = ""

    fun updateProgress(progress: Float) {
        Timber.d("updateProgress: $progress")
        _uiState.update { 
            it.copy(
                currentProgress = progress,
                selectedImageUri = if (progress == 0f) null else it.selectedImageUri,
                uploadedImageUrl = if (progress == 0f) null else it.uploadedImageUrl,
                isReplicaFaceValid = if (progress == 0f) false else it.isReplicaFaceValid,
                replicaTaskId = if (progress == 0f) null else it.replicaTaskId,
                isPhotoUploadedAndValidatedCompleted = if (progress == 0f) false else it.isPhotoUploadedAndValidatedCompleted,
                isGenerating = false,
                faceDetectionPassed = if (progress == 0f) false else it.faceDetectionPassed
            )
        }
    }

    fun isUserLoggedIn(): Boolean {
        return runBlocking {
            UserDataStoreManager.isUserLoggedIn()
        }
        
    }

    fun loginWithGoogle(context: Context, onSuccess: () -> Unit = {}) {
        viewModelScope.launch {
            authManager.startGoogleLogin(
                clientId = context.getString(R.string.default_web_client_id),
                activity = context as Activity,
                onSuccess = {
                    _uiState.update { it.copy(isLoading = false) }
                    // 调用传入的成功回调
                    onSuccess()
                },
                onError = { errorMsg ->
                    _uiState.update { it.copy(isLoading = false) }
                    Timber.e("Google登录失败: $errorMsg")
                    // 错误处理逻辑
                },
                onLoading = {
                    _uiState.update { it.copy(isLoading = true) }
                }
            )
        }
    }

    fun loginWithApple(context: Context, onSuccess: () -> Unit = {}) {
        viewModelScope.launch {
            authManager.startAppleLogin(
                activity = context as Activity,
                onSuccess = {
                    _uiState.update { it.copy(isLoading = false) }
                    // 调用传入的成功回调
                    onSuccess()
                },
                onError = { errorMsg ->
                    _uiState.update { it.copy(isLoading = false) }
                    Timber.e("Apple登录失败: $errorMsg")
                    // 错误处理逻辑
                },
                onLoading = {
                    _uiState.update { it.copy(isLoading = true) }
                }
            )
        }
    }

    fun setSelectedImage(uri: String) {
        _uiState.update { 
            it.copy(
                selectedImageUri = uri,
                isPhotoUploadedAndValidatedCompleted = false  // 添加这行，确保选择新图片时重置状态
            ) 
        }
    }

    fun validateImageByMLKit(uri: String, onSuccess: () -> Unit, onError: (String) -> Unit) {
        try {
            Timber.d("=== 开始人脸检测 ===")
            Timber.d("Input URI: $uri")

            val parsedUri = uri.toUri()
            Timber.d("Parsed URI scheme: ${parsedUri.scheme}, path: ${parsedUri.path}")
            
            val file = if (parsedUri.scheme == "file") {
                File(parsedUri.path!!)
            } else {
                File(uri.toString())
            }
            
            Timber.d("文件路径: ${file.absolutePath}")
            Timber.d("文件是否存在: ${file.exists()}")
            Timber.d("文件大小: ${file.length()} bytes")
            Timber.d("文件是否可读: ${file.canRead()}")
            
            // 获取图片的EXIF旋转信息
            val exif = ExifInterface(file.absolutePath)
            val rotationDegrees = when (exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL)) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90
                ExifInterface.ORIENTATION_ROTATE_180 -> 180
                ExifInterface.ORIENTATION_ROTATE_270 -> 270
                else -> 0
            }
            Timber.d("图片EXIF旋转角度: $rotationDegrees")
            
                        // 直接加载bitmap，不进行采样
            val bitmap = BitmapFactory.decodeFile(file.absolutePath)
            Timber.d("bitmap加载结果: ${if (bitmap != null) "成功" else "失败"}")
            
            if (bitmap == null) {
                Timber.e("无法加载图片文件，文件路径: ${file.absolutePath}")
                updateProgress(0f)
                onError("Can't load image file")
                return
            }
            
            Timber.d("图片尺寸: ${bitmap.width}x${bitmap.height}")
            Timber.d("图片配置: ${bitmap.config}")
            
            // 创建InputImage，使用正确的旋转角度
            val image = InputImage.fromBitmap(bitmap, rotationDegrees)
            Timber.d("InputImage创建成功，旋转角度: $rotationDegrees")
            
            // 配置人脸检测选项 - 使用更准确的模式
            val options = FaceDetectorOptions.Builder()
                .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST) // 改为准确模式
                .setMinFaceSize(0.1f) // 降低最小人脸大小要求
                .build()
            
            Timber.d("人脸检测器配置完成")
            
            // 获取人脸检测器
            val detector = FaceDetection.getClient(options)
            
            // 执行人脸检测
            detector.process(image)
                .addOnSuccessListener { faces ->
                    try {
                        Timber.d("人脸检测完成，检测到人脸数量: ${faces.size}")
                        
                        // 检查是否有人脸
                        if (faces.isEmpty()) {
                            updateProgress(0f)
                            onError("No face detected, please use a photo with a clear face")
                            bitmap.recycle()
                            return@addOnSuccessListener
                        }
                        
                        // 获取图片尺寸
                        val imageWidth = bitmap.width
                        val imageHeight = bitmap.height
                        val imageArea = imageWidth * imageHeight
                        
                        // 检查人脸大小
                        faces.forEachIndexed { index, face ->
                            val boundingBox = face.boundingBox
                            val faceArea = boundingBox.width() * boundingBox.height()
                            val faceRatio = faceArea.toFloat() / imageArea.toFloat()
                            Timber.d("人脸$index: 位置=${boundingBox}, 面积=$faceArea, 占比=$faceRatio")
                        }
                        
                        val face = faces[0] // 取第一个检测到的人脸
                        val boundingBox = face.boundingBox
                        val faceArea = boundingBox.width() * boundingBox.height()
                        val faceRatio = faceArea.toFloat() / imageArea.toFloat()
                        
                        Timber.d("主要人脸检测结果: 图片尺寸=${imageWidth}x${imageHeight}, 人脸区域=${boundingBox.width()}x${boundingBox.height()}, 人脸占比=$faceRatio")
                        
                        // 检查人脸是否完全在图片边界内
                        if (boundingBox.left < 0 || boundingBox.top < 0 || 
                            boundingBox.right > imageWidth || boundingBox.bottom > imageHeight) {
                            Timber.w("人脸超出图片边界: left=${boundingBox.left}, top=${boundingBox.top}, right=${boundingBox.right}, bottom=${boundingBox.bottom}, 图片尺寸=${imageWidth}x${imageHeight}")
                            updateProgress(0f)
                            onError("Face is partially outside the image, please use a photo where the entire face is visible")
                            bitmap.recycle()
                            return@addOnSuccessListener
                        }
                        
                        if (faceRatio < 0.1f) {
                            Timber.w("人脸太小，占比: $faceRatio < 0.1")
                            updateProgress(0f)
                            onError("Face is too small, please use a photo with a clearer face")
                        } else {
                            Timber.d("人脸检测通过，人脸大小符合要求，占比: $faceRatio")
                            // 验证成功，设置人脸检测标记为true
                            _uiState.update { it.copy(faceDetectionPassed = true) }
                            // 验证成功，调用成功回调
                            onSuccess()
                        }
                        
                        // 回收bitmap资源
                        bitmap.recycle()
                        
                    } catch (e: Exception) {
                        Timber.e(e, "处理人脸检测结果时出错")
                        updateProgress(0f)
                        onError("Error processing face detection result: ${e.message}")
                        bitmap.recycle()
                    }
                }
                .addOnFailureListener { e ->
                    Timber.e(e, "人脸检测失败")
                    updateProgress(0f)
                    onError("Error detecting face: ${e.message}")
                    bitmap.recycle()
                }
        } catch (oom: OutOfMemoryError) {
            Timber.e(oom, "创建人脸检测任务时内存不足")
            updateProgress(0f)
            onError("Memory insufficient. Please try using a smaller image or restart the app to free up memory.")
        } catch (e: Exception) {
            Timber.e(e, "创建人脸检测任务时出错")
            updateProgress(0f)
            onError("Error creating face detection task: ${e.message}")
        }
    }



    fun uploadImage(uri: String, onError: (String) -> Unit) {
        viewModelScope.launch {
            try {
                // 记录图片上传开始时间
                val uploadStartTime = System.currentTimeMillis()
                trackEventOnSentry("network_request", mapOf("path" to "/upload_image"))
                
                val uri = uri.toUri()
                val file = if (uri.scheme == "file") {
                    File(uri.path!!)
                } else {
                    File(uri.toString())
                }

                // 获取图片宽高
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeFile(file.absolutePath, options)
                val width = options.outWidth
                val height = options.outHeight

                // 获取预签名URL
                val presignedResponse = ImageService.api.getPresignedUrl(
                    purpose = "user_upload",
                    width = width,
                    height = height
                )

                if (!presignedResponse.isSuccessful) {
                    Timber.e("获取预签名URL失败: ${presignedResponse.errorBody()?.string()}")
                    trackEventOnSentry("network_failure", mapOf(
                        "path" to "/upload_image", 
                        "error" to "获取预签名URL失败"
                    ))
                    onError("获取上传URL失败")
                    return@launch
                }

                val presignedData = presignedResponse.body() ?: run {
                    trackEventOnSentry("network_failure", mapOf(
                        "path" to "/upload_image", 
                        "error" to "预签名数据为空"
                    ))
                    onError("获取上传URL失败")
                    return@launch
                }

                // 使用预签名URL上传图片
                val uploadSuccess = ImageService.uploadWithPresignedUrl(
                    presignedUrl = presignedData.presignedUrl,
                    file = file
                )

                if (!uploadSuccess) {
                    Timber.e("使用预签名URL上传图片失败")
                    trackEventOnSentry("network_failure", mapOf(
                        "path" to "/upload_image", 
                        "error" to "使用预签名URL上传图片失败"
                    ))
                    onError("上传图片失败")
                    return@launch
                }

                // 上传成功，更新状态
                val publicUrl = presignedData.publicUrl
                _uiState.update { it.copy(uploadedImageUrl = publicUrl) }
                Timber.d("uploadedImageUrl: $publicUrl")
                
                // 记录图片上传成功及耗时
                val uploadDuration = (System.currentTimeMillis() - uploadStartTime) / 1000.0
                Timber.d("图片上传统计 - path: /upload_image, duration: ${uploadDuration}秒")
                trackEventOnSentry("network_timing", mapOf(
                    "path" to "/upload_image", 
                    "custom_duration" to uploadDuration
                ))

                // 上传成功后验证人脸
                validateReplicaFace(
                    ValidateReplicaFaceParams(userImageUrl = publicUrl, skipFaceDetection = _uiState.value.faceDetectionPassed),
                    onError
                )
            } catch (e: Exception) {
                Timber.e(e, "上传图片过程中发生错误")
                trackEventOnSentry("network_failure", mapOf(
                    "path" to "/upload_image", 
                    "error" to (e.message ?: "未知错误")
                ))
                onError("上传图片失败: ${e.message}")
            }
        }
    }

    fun validateReplicaFace(params: ValidateReplicaFaceParams, onError: (String) -> Unit) {
        viewModelScope.launch { 
            val res = TryOnService.api.validateReplicaFace(params)
            if (!res.isSuccessful) {
                Timber.e("Validate replica face failed: ${res.errorBody()?.string()}")
                return@launch
            }
            val response = res.body()
            if (response != null) {
                _uiState.update { it.copy(isReplicaFaceValid = response.isValid, replicaTaskId = response.replicaTaskId) }
                if (!response.isValid) {
                    // 清理相关状态
                    updateProgress(0f)
                    onError(response.validMessage)
                } else {
                    _uiState.update { it.copy(isPhotoUploadedAndValidatedCompleted = true) }
                    updateProgress(1.0f)  // 更新进度到100%，表示人脸验证已完成
                }
            }
        }
    }

    fun generateAvatar(onSuccess: () -> Unit, onError: (String) -> Unit) {
        viewModelScope.launch {
            updateIsGenerating(true)
            
            try {
                val preference = UserPreferenceForReplica(
                    bodyType = listOf(BodyTypeOption.valueOf(_uiState.value.bodyType).displayName),
                    bodyShape = BodyShapeOption.valueOf(_uiState.value.bodyShape).displayName,
                    otherNeeds = _uiState.value.additionalInfo
                )
                
                val replicaTaskId = _uiState.value.replicaTaskId ?: run {
                    updateIsGenerating(false)
                    onError("No replica task ID")
                    return@launch
                }
                
                val params = GenerateReplicaFaceParams(
                    replicaTaskId = replicaTaskId,
                    type = "swap_head",
                    isAsync = true,
                    preference = preference,
                    registerModel = false
                )
                val res = TryOnService.api.generateReplicaFace(params)
                if (!res.isSuccessful) {
                    Timber.e("Generate replica face failed: ${res.errorBody()?.string()}")
                    updateIsGenerating(false)
                    onError(res.errorBody()?.string() ?: "Generate replica face failed")
                    return@launch
                }
                val response = res.body()
                if (response != null) {
                    asyncTaskManager.setReplicaTaskId(replicaTaskId)
                    onSuccess()
                    updateIsGenerating(false)
                } else {
                    updateIsGenerating(false)
                    onError("Invalid response format")
                }
            } catch (e: Exception) {
                Timber.e("Set replica face error: ${e.message}")
                updateIsGenerating(false)
                onError(e.message ?: "Unknown error occurred")
            }
        }
    }

    // 添加新的状态更新方法
    fun updatePrivacyDialog(show: Boolean) {
        _uiState.update { it.copy(showPrivacyDialog = show) }
    }

    fun updateLoginDialog(show: Boolean) {
        _uiState.update { it.copy(showLoginDialog = show) }
    }

    fun updateCameraPicker(show: Boolean) {
        _uiState.update { it.copy(showCameraPicker = show) }
    }

    fun updateBodyType(bodyType: String) {
        _uiState.update { it.copy(bodyType = bodyType) }
    }

    fun updateBodyShape(bodyShape: String) {
        _uiState.update { it.copy(bodyShape = bodyShape) }
    }

    fun updateAdditionalInfo(additionalInfo: String) {
        _uiState.update { it.copy(additionalInfo = additionalInfo) }
    }

    fun updateIsGenerating(isGenerating: Boolean) {
        _uiState.update { it.copy(isGenerating = isGenerating) }
    }

    fun setLoginSource(source: String) {
        loginSource = source
    }

    fun getLoginSource(): String {
        return loginSource
    }

    fun updateBackButtonState(disabled: Boolean) {
        _uiState.update { it.copy(isBackButtonDisabled = disabled) }
    }
    
    fun enableBackButtonAfterDelay(delayMillis: Long = 500) {
        viewModelScope.launch {
            kotlinx.coroutines.delay(delayMillis)
            updateBackButtonState(false)
        }
    }
}   