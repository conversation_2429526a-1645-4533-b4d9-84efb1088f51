package one.srp.gensmo.viewmodel.notification

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.repository.NotificationRepository
import one.srp.core.network.api.NotificationItem
import one.srp.core.network.api.Pagination
import javax.inject.Inject

@HiltViewModel
class NotificationViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository
) : ViewModel() {

    data class UiState(
        val isLoading: Boolean = false,
        val isLoadingMore: Boolean = false,
        val error: String? = null,
        val notifications: List<NotificationItem> = emptyList(),
        val pagination: Pagination? = null,
        val hasMore: Boolean = true
    )

    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    init {
        // 监听通知仓库的状态变化
        viewModelScope.launch {
            notificationRepository.state.collect { repositoryState ->
                _uiState.value = _uiState.value.copy(
                    isLoading = repositoryState.isLoading,
                    isLoadingMore = repositoryState.isLoadingMore,
                    error = repositoryState.error,
                    notifications = repositoryState.notifications,
                    pagination = repositoryState.pagination,
                    hasMore = repositoryState.hasMore
                )
            }
        }
    }

    /**
     * 刷新通知列表
     */
    fun refresh() {
        notificationRepository.refresh()
    }

    /**
     * 加载更多通知
     */
    fun loadMore() {
        notificationRepository.loadMore()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        notificationRepository.clearError()
    }

    /**
     * 标记所有未读通知为已读
     */
    fun markAllNotificationsAsRead() {
        notificationRepository.markAllNotificationsAsRead()
    }

    /**
     * 标记通知为已读
     * @param notificationIds 通知ID列表
     */
    fun markNotificationsAsRead(notificationIds: List<String>) {
        notificationRepository.markNotificationsAsRead(notificationIds)
    }
} 