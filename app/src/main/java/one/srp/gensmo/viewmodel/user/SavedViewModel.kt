package one.srp.gensmo.viewmodel.user

import android.content.Context
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.lifecycle.ViewModel
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeWebView
import com.smallbuer.jsbridge.core.CallBackFunction
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.tryon._viewmodel.TryOnGenerateViewModel
import one.srp.gensmo.utils.bridge.JSBridgeManager
import one.srp.gensmo.utils.env.EnvConf
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel
import org.json.JSONObject
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject

/**
 * 收藏页面的 ViewModel
 * 负责管理收藏页面的状态和业务逻辑
 */
@HiltViewModel
class SavedViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val jsBridgeManager: JSBridgeManager
) : ViewModel() {
    
    // 使用弱引用来避免内存泄漏
    private var webViewRef: WeakReference<BridgeWebView>? = null
    
    init {
        Timber.d("SavedViewModel 已初始化")
    }
    
    // 创建并初始化WebView
    fun createWebView(): BridgeWebView {
        webViewRef?.get()?.let {
            return it
        }
        
        val newWebView = BridgeWebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.useWideViewPort = true
            settings.loadWithOverviewMode = true
            settings.cacheMode = WebSettings.LOAD_DEFAULT
        }
        webViewRef = WeakReference(newWebView)
        Timber.d("WebView已在SavedViewModel中创建")
        return newWebView
    }
    
    // 获取WebView
    fun getWebView(): BridgeWebView? {
        return webViewRef?.get()
    }
    
    // 初始化JSBridge并加载URL
        fun initializeWebView(navActions: NavActions, feedDetailViewModel: FeedDetailViewModel, sharedNavViewModel: SharedNavViewModel, tryOnGenerateViewModel: TryOnGenerateViewModel, onInitialized: () -> Unit) {
            val webView = createWebView()
            webView.addHandlerLocal( "jumpToDetail", DetailHandler(navActions, feedDetailViewModel, sharedNavViewModel, tryOnGenerateViewModel))
        jsBridgeManager.apply {
            Timber.d("JSBridgeManager配置已更新")
            init(webView) {
                val baseUrl = EnvConf.origin
                Timber.d("loadUrl: $baseUrl/user/saved?click_src=profile")
                webView.loadUrl("$baseUrl/user/saved?click_src=profile")
                Timber.d("收藏页面已加载")
                onInitialized()
            }
        }
    }

    private inner class DetailHandler(
        private val navActions: NavActions,
        private val feedDetailViewModel: FeedDetailViewModel,
        private val sharedNavViewModel: SharedNavViewModel,
        private val tryOnGenerateViewModel: TryOnGenerateViewModel
    ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            try {
                val jsonData = JSONObject(data ?: "{}")
                val tryOnTaskId = jsonData.optString("tryOnTaskId", "")
                if (tryOnTaskId.isNotEmpty()) {
                    Timber.d("跳转到试穿任务页面: $tryOnTaskId")
                    tryOnGenerateViewModel.clear()
                    navActions.navigateToTryOnTask(tryOnTaskId)
                } else {
                    val moodboardId = jsonData.optString("moodboardId", "")
                    Timber.d("跳转到详情页面: $moodboardId")
                    feedDetailViewModel.clear()
                    sharedNavViewModel.updateFeedItem(null)
                    navActions.navigateToFeedDetail(moodboardId)
                }
                function?.onCallBack("success")
            } catch (e: Exception) {
                Timber.e(e, "跳转到详情页面失败")
                function?.onCallBack("error")
            }
        }
    }
    
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        webViewRef?.clear()
        webViewRef = null
        jsBridgeManager.release()
        Timber.d("SavedViewModel已清除，WebView引用已释放")
    }
} 