package one.srp.gensmo.viewmodel.hashtag

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.launch
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.HashtagInfo
import one.srp.gensmo.data.remote.FeedService
import one.srp.gensmo.data.remote.HashtagPagingSource
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class HashtagDetailViewModel @Inject constructor() : ViewModel() {
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _hashtag = MutableStateFlow("")
    val hashtag: StateFlow<String> = _hashtag.asStateFlow()
    
    private val _selectedTab = MutableStateFlow(HashtagFeedType.POPULAR)
    val selectedTab: StateFlow<HashtagFeedType> = _selectedTab.asStateFlow()
    
    private val _hashtagInfo = MutableStateFlow<HashtagInfo?>(null)
    val hashtagInfo: StateFlow<HashtagInfo?> = _hashtagInfo.asStateFlow()
    
    // Bottom sheet state management
    private val _showMarkdownBottomSheet = MutableStateFlow(false)
    val showMarkdownBottomSheet: StateFlow<Boolean> = _showMarkdownBottomSheet.asStateFlow()
    
    private val _markdownContent = MutableStateFlow("")
    val markdownContent: StateFlow<String> = _markdownContent.asStateFlow()
    
    // Search panel state management
    private val _showSearchPanel = MutableStateFlow(false)
    val showSearchPanel: StateFlow<Boolean> = _showSearchPanel.asStateFlow()
    
    // Separate paging flows for each tab type
    private var popularPagingFlow: Flow<PagingData<FeedItem>>? = null
    private var recentPagingFlow: Flow<PagingData<FeedItem>>? = null
    
    fun setHashtag(hashtag: String) {
        if (_hashtag.value != hashtag) {
            _hashtag.value = hashtag
            // 重置所有分页流
            popularPagingFlow = null
            recentPagingFlow = null
            // 获取hashtag信息
            fetchHashtagInfo(hashtag)
        }
    }
    
    private fun fetchHashtagInfo(hashtag: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = FeedService.api.getHashtagInfo("#$hashtag")
                if (response.isSuccessful) {
                    _hashtagInfo.value = response.body()
                } else {
                    Timber.e("Failed to fetch hashtag info: ${response.message()}")
                    _hashtagInfo.value = null
                }
            } catch (e: Exception) {
                Timber.e(e, "Error fetching hashtag info")
                _hashtagInfo.value = null
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun setSelectedTab(tab: HashtagFeedType) {
        _selectedTab.value = tab
    }
    
    fun getPagingFlow(type: HashtagFeedType = HashtagFeedType.POPULAR): Flow<PagingData<FeedItem>> {
        // 如果 hashtag 为空，返回空流
        if (_hashtag.value.isBlank()) {
            return emptyFlow()
        }
        
        return when (type) {
            HashtagFeedType.POPULAR -> {
                if (popularPagingFlow == null) {
                    popularPagingFlow = Pager(
                        config = PagingConfig(
                            pageSize = 20,
                            enablePlaceholders = false,
                            initialLoadSize = 20
                        ),
                        pagingSourceFactory = { 
                            HashtagPagingSource(
                                hashtag = _hashtag.value,
                                feedType = HashtagFeedType.POPULAR
                            )
                        }
                    ).flow.cachedIn(viewModelScope)
                }
                popularPagingFlow!!
            }
            HashtagFeedType.RECENT -> {
                if (recentPagingFlow == null) {
                    recentPagingFlow = Pager(
                        config = PagingConfig(
                            pageSize = 20,
                            enablePlaceholders = false,
                            initialLoadSize = 20
                        ),
                        pagingSourceFactory = { 
                            HashtagPagingSource(
                                hashtag = _hashtag.value,
                                feedType = HashtagFeedType.RECENT
                            )
                        }
                    ).flow.cachedIn(viewModelScope)
                }
                recentPagingFlow!!
            }
        }
    }
    
    /**
     * Shows the markdown bottom sheet with the provided content
     */
    fun showMarkdownBottomSheet(content: String = "") {
        _markdownContent.value = content.ifBlank { 
            // Use hashtag info rule content if available, otherwise show default message
            _hashtagInfo.value?.ruleContent ?: ""
        }
        _showMarkdownBottomSheet.value = true
    }
    
    /**
     * Hides the markdown bottom sheet
     */
    fun hideMarkdownBottomSheet() {
        _showMarkdownBottomSheet.value = false
        // Clear content after a delay to avoid flickering during dismiss animation
        viewModelScope.launch {
            kotlinx.coroutines.delay(300) // Wait for dismiss animation
            _markdownContent.value = ""
        }
    }
    
    /**
     * Shows the search panel
     */
    fun showSearchPanel() {
        _showSearchPanel.value = true
    }
    
    /**
     * Hides the search panel
     */
    fun hideSearchPanel() {
        _showSearchPanel.value = false
    }
    
    fun clear() {
        _hashtag.value = ""
        _isLoading.value = false
        _selectedTab.value = HashtagFeedType.POPULAR
        _hashtagInfo.value = null
        _showMarkdownBottomSheet.value = false
        _markdownContent.value = ""
        _showSearchPanel.value = false
        popularPagingFlow = null
        recentPagingFlow = null
    }
} 