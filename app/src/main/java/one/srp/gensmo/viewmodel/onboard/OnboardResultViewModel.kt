package one.srp.gensmo.viewmodel.onboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.model.MoodboardItem
import one.srp.gensmo.data.remote.MoodboardService
import one.srp.gensmo.data.repository.PublishRepository
import one.srp.gensmo.data.repository.utils.parseOrNull
import timber.log.Timber
import javax.inject.Inject

data class OnboardResultUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val moodboards: List<MoodboardItem> = emptyList(),
)

@HiltViewModel
class OnboardResultViewModel @Inject constructor(
    private val publishRepository: PublishRepository,
) : ViewModel() {

    private val _uiState = MutableStateFlow(OnboardResultUiState())
    val uiState: StateFlow<OnboardResultUiState> = _uiState.asStateFlow()

    fun fetchMoodboards(selectedTags: List<String>) {
        if (selectedTags.isEmpty()) {
            Timber.w("No tags provided, skipping API call")
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // 将标签转换为小写并用下划线连接
                val taxonomyPrimaryTags = selectedTags.joinToString("_") { it.lowercase() }

                val response = parseOrNull {
                    MoodboardService.api.getOnboardMoodboards(
                        contentNum = selectedTags.size,
                        taxonomyPrimaryTags = taxonomyPrimaryTags
                    )
                }

                if (response != null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        moodboards = response,
                        error = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "获取moodboard失败"
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to fetch moodboards")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "网络错误，请重试"
                )
            }
        }
    }

    fun publishMoodboard(documentId: String, title: String, description: String = "") {
        viewModelScope.launch {
            try {
                publishRepository.publishMoodboard(documentId, title, description).collect { result ->
                    result.onSuccess {
                        Timber.d("Publish moodboard success: id=$documentId")
                    }.onFailure { throwable ->
                        Timber.e(throwable, "Publish moodboard failed")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Publish moodboard exception")
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
} 