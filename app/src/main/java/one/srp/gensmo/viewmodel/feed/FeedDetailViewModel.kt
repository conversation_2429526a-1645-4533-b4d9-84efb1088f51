package one.srp.gensmo.viewmodel.feed

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.network.utils.JSON
import one.srp.core.network.model.CollectionType
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.MoodboardContent
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.remote.FeedService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.remote.paging.SimilarFeedPagingSource
import one.srp.gensmo.data.repository.PublishRepository
import one.srp.gensmo.data.store.UserDataStoreManager
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max

@HiltViewModel
class FeedDetailViewModel @Inject constructor(
    private val publishRepository: PublishRepository,
) : ViewModel() {

    private val _searchStatus = MutableStateFlow(TaskStatus.Idle)
    val searchStatus: StateFlow<TaskStatus> = _searchStatus.asStateFlow()

    private val _searchResult = MutableStateFlow<FeedItem?>(null)
    val searchResult: StateFlow<FeedItem?> = _searchResult.asStateFlow()

    // 相似数据的 paging flow，使用 Map 来缓存不同 itemId 的 Flow
    private val _similarFlows = mutableMapOf<String, kotlinx.coroutines.flow.Flow<androidx.paging.PagingData<FeedItem>>>()

    fun getCollageDetail(id: String) {
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Loading)
            _searchResult.emit(null)

            try {
                val res =
                    FeedService.api.getFeedDetail("collage", id, UserDataStoreManager.getUserId())

                if (res.isSuccessful) {
                    res.body()?.let { body ->
                        val feedItem = JSON.decodeFromString<FeedItem>(
                            JSON.encodeToString(body.data)
                        )
                        feedItem.moodboards?.let {
                            it.parsedContent = JSON.decodeFromString<MoodboardContent>(it.content)
                        }

                        _searchResult.emit(feedItem)
                        _searchStatus.emit(TaskStatus.Success)
                    } ?: run {
                        Timber.e("Parse failed")
                        _searchStatus.emit(TaskStatus.Fail)
                    }
                } else {
                    Timber.e("Fetch failed")
                    _searchStatus.emit(TaskStatus.Fail)
                }
            } catch (err: Exception) {
                Timber.e(err)
                _searchStatus.emit(TaskStatus.Fail)
            }
        }
    }

    fun getTryOnDetail(id: String) {
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Loading)
            _searchResult.emit(null)

            try {
                val res =
                    FeedService.api.getFeedDetail("try_on", id, UserDataStoreManager.getUserId())

                if (res.isSuccessful) {
                    res.body()?.let { body ->
                        val feedItem = JSON.decodeFromString<FeedItem>(
                            JSON.encodeToString(body.data)
                        )
                        feedItem.moodboards?.let {
                            it.parsedContent = JSON.decodeFromString<MoodboardContent>(it.content)
                        }

                        _searchResult.emit(feedItem)
                        _searchStatus.emit(TaskStatus.Success)
                    } ?: run {
                        Timber.e("Parse failed")
                        _searchStatus.emit(TaskStatus.Fail)
                    }
                } else {
                    Timber.e("Fetch failed")
                    _searchStatus.emit(TaskStatus.Fail)
                }
            } catch (err: Exception) {
                Timber.e(err)
                _searchStatus.emit(TaskStatus.Fail)
            }
        }
    }

    fun clear() {
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Idle)
            _searchResult.emit(null)
            _similarFlows.clear()
        }
    }


    fun getSimilarPagingFlow(itemId: String): kotlinx.coroutines.flow.Flow<androidx.paging.PagingData<FeedItem>> {
        return _similarFlows.getOrPut(itemId) {
            Pager(
                config = PagingConfig(
                    pageSize = 10, 
                    enablePlaceholders = false,
                    prefetchDistance = 5
                ),
                pagingSourceFactory = {
                    SimilarFeedPagingSource(itemId = itemId)
                }
            ).flow.cachedIn(viewModelScope)
        }
    }

    fun deletePost(
        feedType: String,
        documentId: String,
        title: String,
        description: String,
        onSuccess: () -> Unit = {},
        onFail: (String) -> Unit = {},
    ) {
        viewModelScope.launch {
            publishRepository.deletePublishedPost(
                feedType = feedType,
                documentId = documentId,
                title = title,
                description = description
            ).collect { result ->
                result.fold(
                    onSuccess = {
                        Timber.d("删除帖子成功")
                        onSuccess()
                    },
                    onFailure = { throwable ->
                        Timber.e(throwable, "删除帖子失败")
                        onFail(throwable.message ?: "删除失败")
                    }
                )
            }
        }
    }

    fun toggleLike(
        item: FeedItem,
        onLikeChanged: (Boolean, Int) -> Unit = { _, _ -> }
    ) {
        viewModelScope.launch {
            val newLike = !item.isLiked
            val originalLike = item.isLiked
            val originalCount = item.likedCount ?: 0

            try {
                // 立即更新UI状态
                item.isLiked = newLike
                item.likedCount = if (newLike) originalCount + 1 else max(originalCount - 1, 0)
                onLikeChanged(newLike, item.likedCount ?: 0)

                // 调用API
                item.moodboardId.let { moodboardId ->
                    val type = if (item.tryOnTaskId?.isNotBlank() == true) CollectionType.TryOn else CollectionType.Collage
                    
                    if (newLike) {
                        UserService.api.likePost(moodboardId, type)
                    } else {
                        UserService.api.unlikePost(moodboardId, type)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "点赞操作失败")
                // 回滚状态
                item.isLiked = originalLike
                item.likedCount = originalCount
                onLikeChanged(originalLike, originalCount)
            }
        }
    }

}
