package one.srp.gensmo

import android.app.Application
import android.content.Context
import androidx.lifecycle.ProcessLifecycleOwner
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import one.srp.gensmo.data.store.ABDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.SignatureValidator
import one.srp.gensmo.utils.integration.AppLifecycleObserver
import one.srp.gensmo.utils.integration.OneSignalManager
import one.srp.gensmo.utils.integration.initDevTools
import one.srp.gensmo.utils.integration.initMetricSDKs
import one.srp.gensmo.utils.metrics.MetricManager
import timber.log.Timber
import javax.inject.Inject

@HiltAndroidApp
class MainApplication : Application() {

    @Inject
    @ApplicationScope
    lateinit var appScope: CoroutineScope

    @Inject
    lateinit var metricManager: MetricManager

    @Inject
    lateinit var appLifecycleObserver: AppLifecycleObserver

    @Inject
    lateinit var oneSignalManager: OneSignalManager

    override fun onCreate() {
        super.onCreate()

        initDevTools()

        checkSignValidation(this)

        metricManager.init()
        initMetricSDKs(this, packageManager, packageName, metricManager)

        runBlocking {
            DeviceDataStoreManager.initialize(this@MainApplication)
        }
        UserDataStoreManager.initialize(this, oneSignalManager)
        ABDataStoreManager.initialize(this)
        appScope.launch {
            UserDataStoreManager.initializeGuestTokenIfNeeded()
        }

        ProcessLifecycleOwner.get().lifecycle.addObserver(appLifecycleObserver)

        // Initialize OneSignal
        oneSignalManager.initialize(this)
    }

    override fun onTerminate() {
        super.onTerminate()
        oneSignalManager.cleanup()
    }
}

private fun checkSignValidation(context: Context) {
    if (!SignatureValidator.validate(context)) {
        if (!BuildConfig.DEBUG) {
            throw RuntimeException("Signature validation failed.")
        } else {
            Timber.e("FATAL: Signature validation failed!")
        }
    }
}
