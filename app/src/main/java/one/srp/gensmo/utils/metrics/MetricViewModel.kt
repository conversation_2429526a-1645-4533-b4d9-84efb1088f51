package one.srp.gensmo.utils.metrics

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import one.srp.core.analytics.events.MetricItem
import one.srp.core.analytics.types.EventRefer
import javax.inject.Inject


@HiltViewModel
class MetricViewModel @Inject constructor(
    private val metricManager: MetricManager,
) : ViewModel() {
    fun compatMetric(refer: EventRefer): (MetricItem) -> Unit {
        return { event: MetricItem ->
            metricManager.log(event.copyWith(refer = refer))
        }
    }
}
