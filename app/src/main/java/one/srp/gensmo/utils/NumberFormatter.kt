package one.srp.gensmo.utils

import java.text.NumberFormat
import java.util.Locale
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.log10
import kotlin.math.pow

/**
 * 数字格式化工具类
 * 提供千位分隔符和k/m缩写功能
 */
object NumberFormatter {
    
    /**
     * 格式化数字，添加千位分隔符
     * @param number 要格式化的数字
     * @param locale 地区设置，默认为系统默认
     * @return 格式化后的字符串
     */
    fun formatWithCommas(number: Long, locale: Locale = Locale.getDefault()): String {
        return NumberFormat.getNumberInstance(locale).format(number)
    }
    
    /**
     * 格式化数字，添加千位分隔符
     * @param number 要格式化的数字
     * @param locale 地区设置，默认为系统默认
     * @return 格式化后的字符串
     */
    fun formatWithCommas(number: Int, locale: Locale = Locale.getDefault()): String {
        return formatWithCommas(number.toLong(), locale)
    }
    
    /**
     * 格式化数字为k/m缩写格式
     * @param number 要格式化的数字
     * @param decimalPlaces 小数位数，默认为1
     * @return 格式化后的字符串
     */
    fun formatCompact(number: Long, decimalPlaces: Int = 1): String {
        if (number < 1000) {
            return number.toString()
        }
        
        val exp = (log10(abs(number.toDouble())) / 3).toInt()
        val value = number / 10.0.pow((exp * 3).toDouble())
        
        val suffix = when (exp) {
            1 -> "k"
            2 -> "m"
            3 -> "b"
            else -> ""
        }
        
        return if (value == floor(value)) {
            "${value.toInt()}$suffix"
        } else {
            "%.${decimalPlaces}f$suffix".format(value).removeSuffix(".0")
        }
    }
    
    /**
     * 格式化数字为k/m缩写格式
     * @param number 要格式化的数字
     * @param decimalPlaces 小数位数，默认为1
     * @return 格式化后的字符串
     */
    fun formatCompact(number: Int, decimalPlaces: Int = 1): String {
        return formatCompact(number.toLong(), decimalPlaces)
    }
    
    /**
     * 格式化数字，同时支持千位分隔符和k/m缩写
     * @param number 要格式化的数字
     * @param useCompact 是否使用k/m缩写，默认为true
     * @param decimalPlaces 小数位数，默认为1
     * @param locale 地区设置，默认为系统默认
     * @return 格式化后的字符串
     */
    fun format(
        number: Long,
        useCompact: Boolean = true,
        decimalPlaces: Int = 1,
        locale: Locale = Locale.getDefault()
    ): String {
        return if (useCompact) {
            formatCompact(number, decimalPlaces)
        } else {
            formatWithCommas(number, locale)
        }
    }
    
    /**
     * 格式化数字，同时支持千位分隔符和k/m缩写
     * @param number 要格式化的数字
     * @param useCompact 是否使用k/m缩写，默认为true
     * @param decimalPlaces 小数位数，默认为1
     * @param locale 地区设置，默认为系统默认
     * @return 格式化后的字符串
     */
    fun format(
        number: Int,
        useCompact: Boolean = true,
        decimalPlaces: Int = 1,
        locale: Locale = Locale.getDefault()
    ): String {
        return format(number.toLong(), useCompact, decimalPlaces, locale)
    }
} 