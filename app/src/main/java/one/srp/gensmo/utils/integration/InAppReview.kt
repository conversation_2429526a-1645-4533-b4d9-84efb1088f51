package one.srp.gensmo.utils.integration

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import com.google.android.play.core.review.ReviewManagerFactory
import timber.log.Timber

object InAppReview {
	private fun findActivity(context: Context): Activity? {
		var ctx: Context? = context
		while (ctx is ContextWrapper) {
			if (ctx is Activity) return ctx
			ctx = ctx.baseContext
		}
		return null
	}

	fun launch(context: Context) {
		val activity = findActivity(context)
		if (activity == null) {
			Timber.w("In-App Review: Activity is null, cannot launch review flow.")
			return
		}
		val manager = ReviewManagerFactory.create(context)
		val request = manager.requestReviewFlow()
		request.addOnCompleteListener { task ->
			if (task.isSuccessful) {
				val reviewInfo = task.result
				Timber.d("In-App Review: request successful, launching review flow…")
				val flow = manager.launchReviewFlow(activity, reviewInfo)
				flow.addOnCompleteListener {
					Timber.d("In-App Review: launchReviewFlow completed (result is intentionally opaque).")
				}
			} else {
				Timber.w(task.exception, "In-App Review: requestReviewFlow failed.")
			}
		}
	}
} 