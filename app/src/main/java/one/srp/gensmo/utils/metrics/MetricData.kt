package one.srp.gensmo.utils.metrics

import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.types.STRING_DEFAULT
import one.srp.gensmo.data.store.ABDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.env.MetaInfo
import one.srp.utils.datetime.getCurrMicros
import timber.log.Timber

object MetricData {
    suspend fun getAbInfo(): String? {
        return ABDataStoreManager.getTrackerABTestConfigJsonString()
    }

    suspend fun getUserId(): String? {
        return UserDataStoreManager.getUserId()
    }

    suspend fun getUserType(): String? {
        return when (UserDataStoreManager.getLoginType()) {
            1 -> {
                "login"
            }

            else -> {
                "guest"
            }
        }
    }

    suspend fun getActiveId(): String? {
        return DeviceDataStoreManager.getDeviceId()
    }

    suspend fun getUserSourceId(): String? {
        return null
    }

    private var _appsflyerId: String? = null
    private var _appsflyerLogEventFn: ((String, Map<String, Any>) -> Unit)? = null

    fun getAppsflyerId(): String? {
        return _appsflyerId
    }

    fun setAppsflyerId(id: String) {
        _appsflyerId = id
    }

    fun setAppsflyerLogEvent(instance: AppsflyerManager, fn: (String, Map<String, Any>) -> Unit) {
        _appsflyerLogEventFn = fn
    }

    fun logEventAF(eventName: String, eventValue: Map<String, Any> = hashMapOf()) {
        Timber.d("logEventAF: $eventName, $eventValue")
        _appsflyerLogEventFn?.invoke(eventName, eventValue)
    }

    suspend fun withParams(event: MetricEvent): MetricEvent {
        val timestamp = getCurrMicros().toString()

        val abInfo = getAbInfo() ?: ""

        val userId = getUserId() ?: STRING_DEFAULT
        val userType = getUserType() ?: STRING_DEFAULT
        val activeId = getActiveId() ?: STRING_DEFAULT
        val userSourceId = getUserSourceId() ?: STRING_DEFAULT
        val appsflyerId = getAppsflyerId() ?: STRING_DEFAULT

        return event.copy(
            platform = MetaInfo.PLATFORM,
            version = MetaInfo.VERSION,

            gensmoTimestamp = timestamp,
            abInfo = abInfo,
            gensmoUserId = userId,
            gensmoUserType = userType,
            gensmoActiveId = activeId,
            gensmoUserSourceId = userSourceId,
            appsflyerId = appsflyerId,
        )
    }
}
