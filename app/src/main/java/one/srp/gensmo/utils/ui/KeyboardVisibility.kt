package one.srp.gensmo.utils.ui

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalDensity

/**
 * Remembers the keyboard visibility state based on IME (Input Method Editor) window insets.
 * 
 * @return A [State] that holds true when the keyboard is visible, false otherwise
 */
@Composable
fun rememberKeyboardVisibility(): State<Boolean> {
    val density = LocalDensity.current
    val imeInsets = WindowInsets.ime
    return remember(density) {
        derivedStateOf {
            imeInsets.getBottom(density) > 0
        }
    }
}

/**
 * Gets the current keyboard visibility as a boolean value.
 * 
 * @return true if the keyboard is currently visible, false otherwise
 */
@Composable
fun isKeyboardVisible(): Boolean {
    val density = LocalDensity.current
    return WindowInsets.ime.getBottom(density) > 0
}

/**
 * Gets the current keyboard height in pixels.
 * 
 * @return The height of the keyboard in pixels, 0 if not visible
 */
@Composable
fun getKeyboardHeight(): Int {
    val density = LocalDensity.current
    return WindowInsets.ime.getBottom(density)
}

/**
 * Utility object for keyboard state management operations.
 */
object KeyboardUtils {
    
    /**
     * Checks if the given keyboard height indicates the keyboard is visible.
     * 
     * @param keyboardHeight The keyboard height in pixels
     * @return true if the keyboard height indicates visibility, false otherwise
     */
    fun isKeyboardVisible(keyboardHeight: Int): Boolean {
        return keyboardHeight > 0
    }
    
    /**
     * Determines if the keyboard state has changed between two height values.
     * 
     * @param previousHeight The previous keyboard height
     * @param currentHeight The current keyboard height
     * @return true if the keyboard visibility state has changed, false otherwise
     */
    fun hasKeyboardStateChanged(previousHeight: Int, currentHeight: Int): Boolean {
        val wasVisible = isKeyboardVisible(previousHeight)
        val isVisible = isKeyboardVisible(currentHeight)
        return wasVisible != isVisible
    }
}