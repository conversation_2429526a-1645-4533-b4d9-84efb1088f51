<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="118dp"
    android:height="104dp"
    android:viewportWidth="118"
    android:viewportHeight="104">
  <path
      android:pathData="M15.9,75.89C16.44,75.89 16.88,75.45 16.88,74.91C16.88,74.37 16.44,73.93 15.9,73.93C15.36,73.93 14.92,74.37 14.92,74.91C14.92,75.45 15.36,75.89 15.9,75.89Z"
      android:fillColor="#CFCFCF"/>
  <path
      android:pathData="M79.45,34.93C71.08,34.93 62.56,34.59 54.59,32.37C46.77,30.2 39.59,25.99 33.19,21.12C29,17.95 25.19,15.42 19.75,15.8C14.43,16.09 9.34,18.08 5.23,21.47C-1.69,27.53 -0.65,38.76 2.12,46.62C6.28,58.49 18.94,66.76 29.7,72.11C42.13,78.32 55.79,81.93 69.48,84C81.48,85.83 96.9,87.15 107.3,79.32C116.85,72.11 119.47,55.67 117.13,44.57C116.56,41.29 114.82,38.33 112.22,36.25C105.51,31.34 95.5,34.62 87.96,34.78C85.16,34.84 82.31,34.91 79.45,34.93Z"
      android:fillColor="#F2F2F2"/>
  <path
      android:pathData="M62.02,85.24C68.26,85.24 73.31,80.19 73.31,73.95C73.31,67.71 68.26,62.66 62.02,62.66C55.79,62.66 50.73,67.71 50.73,73.95C50.73,80.19 55.79,85.24 62.02,85.24Z"
      android:fillColor="#D2D2D2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M50.35,3.47C50.98,3.33 51.62,3.31 52.25,3.42C52.88,3.53 53.49,3.77 54.03,4.11C54.57,4.45 55.03,4.9 55.4,5.42C55.77,5.94 56.03,6.54 56.17,7.16L57.97,15.25L48.45,17.37L46.64,9.28C46.5,8.66 46.49,8.01 46.6,7.38C46.71,6.75 46.94,6.14 47.29,5.6C47.63,5.06 48.08,4.6 48.61,4.23C49.14,3.86 49.73,3.6 50.35,3.47Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M94.86,66.94L36.97,79.84C36.61,79.92 36.24,79.91 35.89,79.8C35.53,79.69 35.22,79.49 34.96,79.22C34.71,78.95 34.53,78.63 34.45,78.27C34.36,77.91 34.37,77.53 34.47,77.18L34.71,76.35C36.71,69.26 38.45,61.45 36.85,54.25L33.43,38.89C30.55,25.98 38.29,12.83 51.13,9.66C54.26,8.88 57.52,8.74 60.71,9.24C63.89,9.74 66.95,10.87 69.69,12.57C72.43,14.27 74.81,16.49 76.68,19.12C78.55,21.75 79.87,24.73 80.57,27.88L84.1,43.76C85.71,50.96 90.57,57.32 95.43,62.86L96,63.51C96.23,63.79 96.39,64.12 96.46,64.48C96.52,64.83 96.5,65.2 96.38,65.54C96.27,65.88 96.07,66.19 95.8,66.44C95.54,66.68 95.21,66.86 94.86,66.94Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M50.77,51.58C51.79,53.69 53.49,55.38 55.61,56.38C57.72,57.37 60.11,57.62 62.38,57.07C64.65,56.52 66.66,55.21 68.08,53.35C69.5,51.5 70.25,49.21 70.19,46.88"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.9,40.81C70.68,40.81 71.32,40.17 71.32,39.39C71.32,38.61 70.68,37.97 69.9,37.97C69.12,37.97 68.48,38.61 68.48,39.39C68.48,40.17 69.12,40.81 69.9,40.81Z"
      android:fillColor="#BABABA"/>
  <path
      android:pathData="M47.88,45.72C48.67,45.72 49.3,45.08 49.3,44.3C49.3,43.52 48.67,42.88 47.88,42.88C47.1,42.88 46.46,43.52 46.46,44.3C46.46,45.08 47.1,45.72 47.88,45.72Z"
      android:fillColor="#BABABA"/>
  <path
      android:pathData="M74.36,1.96C74.9,1.96 75.34,1.52 75.34,0.98C75.34,0.44 74.9,0 74.36,0C73.82,0 73.38,0.44 73.38,0.98C73.38,1.52 73.82,1.96 74.36,1.96Z"
      android:fillColor="#CFCFCF"/>
  <path
      android:pathData="M15.9,75.89C16.44,75.89 16.88,75.45 16.88,74.91C16.88,74.37 16.44,73.93 15.9,73.93C15.36,73.93 14.92,74.37 14.92,74.91C14.92,75.45 15.36,75.89 15.9,75.89Z"
      android:fillColor="#CFCFCF"/>
  <path
      android:strokeWidth="1"
      android:pathData="M101.44,24.65V28.95"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M99.29,26.8H103.59"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M103.4,89.39V93.69"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M101.25,91.54H105.55"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M28.22,8.35V12.65"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M26.07,10.5H30.37"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M59.37,103.46C79.64,103.46 96.08,102.43 96.08,101.17C96.08,99.91 79.64,98.88 59.37,98.88C39.1,98.88 22.66,99.91 22.66,101.17C22.66,102.43 39.1,103.46 59.37,103.46Z"
      android:fillColor="#F2F2F2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M94.44,22.21C94.44,25.25 93.23,28.17 91.08,30.32C88.93,32.47 86.01,33.68 82.97,33.68C82.6,33.68 82.22,33.66 81.85,33.62L78.45,39.17L77.39,32.22C75.62,31.24 74.15,29.8 73.11,28.07C72.08,26.33 71.52,24.35 71.5,22.33C71.48,20.31 71.99,18.31 72.99,16.55C73.98,14.79 75.42,13.33 77.17,12.3C78.91,11.28 80.89,10.74 82.92,10.73C84.94,10.72 86.93,11.24 88.68,12.25C90.43,13.26 91.89,14.71 92.9,16.46C93.91,18.21 94.44,20.2 94.44,22.22V22.21Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M78.07,23.37C78.71,23.37 79.23,22.85 79.23,22.21C79.23,21.57 78.71,21.05 78.07,21.05C77.43,21.05 76.91,21.57 76.91,22.21C76.91,22.85 77.43,23.37 78.07,23.37Z"
      android:fillColor="#D2D2D2"
      android:strokeColor="#D2D2D2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M82.97,23.37C83.61,23.37 84.13,22.85 84.13,22.21C84.13,21.57 83.61,21.05 82.97,21.05C82.33,21.05 81.81,21.57 81.81,22.21C81.81,22.85 82.33,23.37 82.97,23.37Z"
      android:fillColor="#D2D2D2"
      android:strokeColor="#D2D2D2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M87.87,23.37C88.51,23.37 89.03,22.85 89.03,22.21C89.03,21.57 88.51,21.05 87.87,21.05C87.23,21.05 86.71,21.57 86.71,22.21C86.71,22.85 87.23,23.37 87.87,23.37Z"
      android:fillColor="#D2D2D2"
      android:strokeColor="#D2D2D2"/>
</vector>
