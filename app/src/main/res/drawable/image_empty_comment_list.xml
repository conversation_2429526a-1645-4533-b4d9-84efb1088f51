<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="135dp"
    android:height="135dp"
    android:viewportWidth="135"
    android:viewportHeight="135">
  <path
      android:pathData="M88.01,49.79C79.64,49.79 71.12,49.45 63.15,47.23C55.33,45.06 48.15,40.85 41.75,35.98C37.56,32.81 33.75,30.28 28.31,30.66C22.99,30.95 17.9,32.94 13.79,36.33C6.87,42.39 7.91,53.62 10.68,61.48C14.84,73.35 27.5,81.62 38.26,86.97C50.69,93.18 64.35,96.79 78.04,98.86C90.04,100.69 105.46,102.01 115.86,94.18C125.41,86.97 128.03,70.53 125.69,59.43C125.12,56.15 123.38,53.19 120.78,51.11C114.07,46.2 104.06,49.48 96.52,49.64C93.72,49.7 90.87,49.77 88.01,49.79Z"
      android:fillColor="#F2F2F2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M118.2,32.12C115.7,34.12 111,33.92 108.2,33.12"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M31.7,71.62C33.2,76.62 42.3,84.92 48.7,88.12C38.7,84.62 30.7,84.12 26.7,89.62C22.7,95.12 24.7,99.62 27.2,101.12C29.7,102.62 31.2,99.62 29.2,98.62C26.48,97.26 19.2,100.12 20.7,107.12"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M29.2,75.62C29.2,76.95 29.9,79.92 32.7,81.12"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M104.2,18.62C104.03,20.29 103.5,23.92 102.7,25.12"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M117.2,18.12C116.2,22.62 109.2,28.62 104.2,29.62"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M52.45,52.99C53.02,48.72 54.66,44.67 57.21,41.21C59.76,37.75 63.15,34.99 67.05,33.2C70.95,31.41 75.24,30.63 79.52,30.95C83.8,31.27 87.93,32.67 91.52,35.03L101.31,31.69C101.61,31.59 101.92,31.58 102.22,31.66C102.53,31.74 102.8,31.9 103.01,32.13C103.22,32.35 103.37,32.64 103.42,32.95C103.48,33.25 103.45,33.57 103.33,33.86L99.4,43.25C101.49,46.74 102.71,50.68 102.97,54.74C103.23,58.81 102.52,62.87 100.89,66.6C99.26,70.33 96.76,73.61 93.61,76.17C90.46,78.73 86.74,80.5 82.77,81.32C78.79,82.14 74.68,82 70.77,80.9C66.87,79.81 63.28,77.78 60.31,75.01C57.34,72.24 55.08,68.79 53.71,64.96C52.35,61.13 51.91,57.02 52.45,52.99Z"
      android:fillColor="#D2D2D2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M35.72,64.38C36.29,68.64 37.93,72.7 40.48,76.16C43.03,79.62 46.42,82.37 50.32,84.17C54.22,85.96 58.51,86.73 62.79,86.41C67.07,86.09 71.2,84.69 74.79,82.33L84.58,85.67C84.88,85.77 85.19,85.78 85.5,85.7C85.8,85.63 86.07,85.46 86.28,85.24C86.49,85.01 86.64,84.72 86.69,84.42C86.75,84.11 86.72,83.79 86.6,83.51L82.67,74.11C84.76,70.62 85.98,66.68 86.24,62.62C86.5,58.56 85.79,54.49 84.16,50.77C82.53,47.04 80.03,43.76 76.88,41.19C73.73,38.63 70.01,36.87 66.04,36.04C62.06,35.22 57.95,35.36 54.04,36.46C50.14,37.56 46.55,39.58 43.58,42.35C40.61,45.13 38.35,48.57 36.98,52.41C35.62,56.24 35.18,60.34 35.72,64.38Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M50.68,64.48C51.7,66.58 53.42,68.27 55.53,69.26C57.65,70.25 60.04,70.48 62.31,69.92C64.57,69.37 66.58,68.05 68,66.19C69.41,64.33 70.15,62.04 70.08,59.71"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.81,53.67C70.59,53.67 71.23,53.03 71.23,52.25C71.23,51.46 70.59,50.83 69.81,50.83C69.02,50.83 68.39,51.46 68.39,52.25C68.39,53.03 69.02,53.67 69.81,53.67Z"
      android:fillColor="#BABABA"/>
  <path
      android:pathData="M47.79,58.58C48.57,58.58 49.21,57.94 49.21,57.16C49.21,56.38 48.57,55.74 47.79,55.74C47,55.74 46.37,56.38 46.37,57.16C46.37,57.94 47,58.58 47.79,58.58Z"
      android:fillColor="#BABABA"/>
  <path
      android:strokeWidth="1"
      android:pathData="M50.85,26.62V30.91"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M93.85,82.62V86.91"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M48.7,28.77H52.99"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M91.7,84.77H95.99"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M67.01,129.45C87.24,129.45 103.64,128.43 103.64,127.17C103.64,125.9 87.24,124.88 67.01,124.88C46.77,124.88 30.37,125.9 30.37,127.17C30.37,128.43 46.77,129.45 67.01,129.45Z"
      android:fillColor="#F2F2F2"/>
</vector>
