import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.google.services)
    alias(libs.plugins.hilt.android)
    alias(libs.plugins.ksp)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.sentry)
}

android {
    namespace = "one.srp.gensmo"
    compileSdk = 35

    defaultConfig {
        applicationId = "one.srp.gensmo.app"
        minSdk = 29
        targetSdk = 35
        versionCode = 95
        versionName = "2.6.2"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
            // 请将此处的 SHA256 指纹替换为您自己的发布证书指纹。
            // 您可以使用以下命令获取您的指纹:
            // keytool -list -v -keystore <您的 keystore 路径> -alias <您的 alias>
            // 在输出中找到 "SHA256" 证书指纹，并将其复制到此处。
            buildConfigField(
                "String",
                "RELEASE_CERT_SHA256",
                "\"02:94:4D:2B:65:8C:5E:04:B2:C9:26:2C:AC:C3:F3:39:62:13:DE:13:E9:51:59:0E:0E:84:7E:79:A5:01:09:3F\""
            )
            buildConfigField(
                "String",
                "RELEASE_CERT_SHA256_DEV",
                "\"B4:8D:7B:A9:30:4A:AF:FB:DD:66:0C:9B:48:C2:14:58:9B:24:37:EC:A7:E4:76:27:7D:EC:9E:20:CE:30:6C:6B\""
            )
            ndk {
                debugSymbolLevel = "NONE"
            }
        }
        debug {
            isMinifyEnabled = false
            // 对于 debug 构建，我们可以使用一个空指纹或您的 debug 密钥指纹
            buildConfigField("String", "RELEASE_CERT_SHA256", "\"\"")
            buildConfigField(
                "String",
                "RELEASE_CERT_SHA256_DEV",
                "\"B4:8D:7B:A9:30:4A:AF:FB:DD:66:0C:9B:48:C2:14:58:9B:24:37:EC:A7:E4:76:27:7D:EC:9E:20:CE:30:6C:6B\""
            )
            ndk {
                debugSymbolLevel = "FULL"
            }
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }
    flavorDimensions += listOf("environment")
    productFlavors {
        create("staging") {
            dimension = "environment"
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
            buildConfigField("String", "APP_ENV", "\"staging\"")
            manifestPlaceholders["networkSecurityConfig"] = "network_security_config_staging"
        }
        create("production") {
            dimension = "environment"
            buildConfigField("String", "APP_ENV", "\"production\"")
            manifestPlaceholders["networkSecurityConfig"] = "network_security_config_release"
        }
    }
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_11)
    }
}

dependencies {
    testImplementation(libs.junit)

    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)

    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)

    // Core modules
    implementation(project(":core:network"))
    implementation(project(":core:analytics"))

    // Utils modules
    implementation(project(":utils:datetime"))

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.navigation.compose)
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics)
    implementation(libs.firebase.auth)
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.timber)
    implementation(libs.accompanist.navigation.animation)
    implementation(libs.androidx.lifecycle.runtime.compose)

    // Metric
    implementation(libs.installreferrer)
    implementation(libs.facebook.android.sdk)
    implementation(libs.feature.delivery)
    implementation(libs.feature.delivery.ktx)
    implementation(libs.androidx.lifecycle.process)

    // AppFlyer (moved from dynamic module)
    implementation(libs.af.android.sdk)

    // CameraX
    implementation(libs.androidx.camera.core)
    implementation(libs.androidx.camera.camera2)
    implementation(libs.androidx.camera.lifecycle)
    implementation(libs.androidx.camera.view)
    implementation(libs.androidx.camera.extensions)

    implementation(libs.androidx.material.icons.core)
    implementation(libs.androidx.material.icons.extended)

    // 添加 Coil 依赖
    implementation(libs.coil3.compose)
    implementation(libs.coil3.network.okhttp)

    // Network
    implementation(libs.retrofit)
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.okhttp.logging.interceptor)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.retrofit2.kotlinx.serialization.converter)
    implementation(libs.androidx.runtime.livedata)

    // Google Identity Services SDK (only required for Auth with Google)
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.play.services.auth)
    implementation(libs.google.identity)

    // Paging
    implementation(libs.androidx.paging.runtime.ktx)
    implementation(libs.androidx.paging.compose)

    // DataStore (needed for AppFlyer)
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.datastore.core)

    // Flow
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    // JSBridge
    implementation(libs.jsbridge)
    implementation(libs.oneSignal)
    implementation(libs.mlkit.face.detection)

    // Media
    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.ui)
    implementation(libs.play.review.ktx)
}

sentry {
    org.set("serendipity-one-inc")
    projectName.set("gensmo-android")

    // this will upload your source code to Sentry to show it as part of the stack traces
    // disable if you don't want to expose your sources
    includeSourceContext.set(true)
}
